# =============================================================================
# Master-Know Environment Configuration
# =============================================================================
#
# This file contains all environment variables for the Master-Know system.
# Copy this file to .env and modify the values according to your environment.
#
# Usage:
#   cp .env.example .env
#   # Edit .env with your actual values
#
# =============================================================================

# =============================================================================
# BASIC PROJECT SETTINGS
# =============================================================================

# Project identification
PROJECT_NAME=Master-Know
STACK_NAME=master-know
ENVIRONMENT=local

# Domain and frontend settings
DOMAIN=localhost
FRONTEND_HOST=http://localhost:5173

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT settings
SECRET_KEY=changethis
ACCESS_TOKEN_EXPIRE_MINUTES=11520
JWT_ALGORITHM=HS256
JWT_REFRESH_EXPIRE_DAYS=30

# CORS settings
BACKEND_CORS_ORIGINS="http://localhost,http://localhost:5173,https://localhost,https://localhost:5173"

# First superuser
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=changethis

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL)
# =============================================================================

# PostgreSQL connection settings
POSTGRES_SERVER=db
POSTGRES_PORT=5432
POSTGRES_DB=master_know
POSTGRES_USER=master_know_user
POSTGRES_PASSWORD=changethis

# Database connection pool settings
DATABASE_POOL_SIZE=10
DATABASE_POOL_TIMEOUT=30
DATABASE_ECHO=false

# Computed DATABASE_URL (used by services)
# Format: postgresql+psycopg://user:password@host:port/database

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis connection settings
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Computed REDIS_URL (used by Dramatiq and caching)
# Format: redis://host:port/db

# =============================================================================
# MANTICORE SEARCH CONFIGURATION
# =============================================================================

# Manticore Search connection settings
MANTICORE_HOST=manticore
MANTICORE_MYSQL_PORT=9306
MANTICORE_HTTP_PORT=9308
MANTICORE_SPHINXAPI_PORT=9312

# =============================================================================
# SERVICE PORTS CONFIGURATION
# =============================================================================

# Individual service ports (for development)
GATEWAY_SERVICE_PORT=9000
EMBEDDING_SERVICE_PORT=9001
USER_SERVICE_PORT=9002
DOCUMENT_SERVICE_PORT=9003
TOPIC_SERVICE_PORT=9004
LLM_SERVICE_PORT=9005
CONVERSATION_SERVICE_PORT=9006
SUMMARY_SERVICE_PORT=9007
MANTICORE_SERVICE_PORT=9008

# =============================================================================
# EMBEDDING CONFIGURATION
# =============================================================================

# OpenAI API settings for embeddings
EMBEDDING_OPENAI_API_KEY=
EMBEDDING_OPENAI_BASE_URL=https://api.openai.com/v1
EMBEDDING_DEFAULT_MODEL=text-embedding-3-small
EMBEDDING_DIM=1536
EMBEDDING_BATCH_SIZE=64
EMBEDDING_REQUEST_TIMEOUT=30
EMBEDDING_MAX_RETRIES=3

# Embedding cache settings
EMBEDDING_ENABLE_CACHE=false
EMBEDDING_CACHE_TTL=3600

# =============================================================================
# LLM INTEGRATION CONFIGURATION
# =============================================================================

# OpenAI API settings
LLM_OPENAI_API_KEY=
LLM_OPENAI_BASE_URL=https://api.openai.com/v1
LLM_OPENAI_MODEL=gpt-4o-mini
LLM_MAX_TOKENS=4096
LLM_TEMPERATURE=0.2

# LLM service settings
LLM_DEFAULT_PROVIDER=openai
LLM_REQUEST_TIMEOUT=60
LLM_MAX_RETRIES=3

# =============================================================================
# DOCUMENT PROCESSING CONFIGURATION
# =============================================================================

# File upload settings
DOCUMENT_UPLOAD_DIR=./storage/uploads
DOCUMENT_PROCESSED_DIR=./storage/processed
DOCUMENT_MAX_FILE_SIZE=10485760
DOCUMENT_ALLOWED_EXTENSIONS=[".txt", ".md", ".pdf", ".docx"]

# Text splitting settings
DOCUMENT_MAX_CHUNK_SIZE=1000
DOCUMENT_OVERLAP_SIZE=100
DOCUMENT_MIN_CHUNK_SIZE=200

# Processing settings
DOCUMENT_ENABLE_ASYNC_PROCESSING=true
DOCUMENT_PROCESSING_TIMEOUT=300
DOCUMENT_MAX_CONCURRENT_JOBS=5

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# SMTP settings
SMTP_HOST=
SMTP_PORT=587
SMTP_TLS=true
SMTP_SSL=false
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=${PROJECT_NAME}

# =============================================================================
# ASYNC PROCESSING CONFIGURATION
# =============================================================================

# Dramatiq settings
DRAMATIQ_BROKER=redis
DRAMATIQ_WORKERS=4
TASK_TIMEOUT=300

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging settings
LOG_LEVEL=INFO
LOG_FORMAT=json

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker image settings
DOCKER_IMAGE_BACKEND=backend
DOCKER_IMAGE_FRONTEND=frontend
TAG=latest
