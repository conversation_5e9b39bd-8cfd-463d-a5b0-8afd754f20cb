# Local development environment configuration
# This file is used for local testing without Docker

# Basic project settings
PROJECT_NAME=Master-Know
STACK_NAME=master-know
ENVIRONMENT=local

# Domain and frontend settings
DOMAIN=localhost
FRONTEND_HOST=http://localhost:5173

# Security configuration
SECRET_KEY=local-dev-secret-key-for-testing-only
ACCESS_TOKEN_EXPIRE_MINUTES=11520
JWT_ALGORITHM=HS256
JWT_REFRESH_EXPIRE_DAYS=30

# CORS settings
BACKEND_CORS_ORIGINS="http://localhost,http://localhost:5173,https://localhost,https://localhost:5173"

# First superuser
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=admin123

# Database configuration (本地PostgreSQL)
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_DB=master_know_local
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# Redis configuration (local)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Manticore Search configuration (local)
MANTICORE_HOST=localhost
MANTICORE_MYSQL_PORT=9306
MANTICORE_HTTP_PORT=9308
MANTICORE_SPHINXAPI_PORT=9312

# Service ports
GATEWAY_SERVICE_PORT=9000
EMBEDDING_SERVICE_PORT=9001
USER_SERVICE_PORT=9002
DOCUMENT_SERVICE_PORT=9003
TOPIC_SERVICE_PORT=9004
LLM_SERVICE_PORT=9005
CONVERSATION_SERVICE_PORT=9006
SUMMARY_SERVICE_PORT=9007
MANTICORE_SERVICE_PORT=9008

# Embedding configuration
EMBEDDING_DEFAULT_MODEL=text-embedding-3-small
EMBEDDING_DIM=1536
EMBEDDING_BATCH_SIZE=100
EMBEDDING_OPENAI_API_KEY=sk-ePLBAF0SMDPSrqZ9VS1RbYSZaphIOrHhFQHnlfjlWjNt8k4Z
EMBEDDING_OPENAI_BASE_URL=https://ai98.vip/v1

# LLM configuration
LLM_OPENAI_API_KEY=sk-ePLBAF0SMDPSrqZ9VS1RbYSZaphIOrHhFQHnlfjlWjNt8k4Z
LLM_OPENAI_BASE_URL=https://ai98.vip/v1
LLM_OPENAI_MODEL=gpt-4o-mini
LLM_MAX_TOKENS=40960
LLM_TEMPERATURE=0.5

# Email configuration (disabled for local testing)
SMTP_TLS=true
SMTP_SSL=false
SMTP_PORT=587
SMTP_HOST=
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=Master-Know

# Sentry (disabled for local testing)
SENTRY_DSN=
