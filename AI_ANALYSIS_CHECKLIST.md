# AI系统分析指导检查清单

## 🚫 避免的误解来源

### 1. 不要仅凭配置文件判断架构
- ❌ 错误: 看到Gateway配置中有多个service_url就认为是微服务
- ✅ 正确: 检查实际运行的容器和服务

### 2. 不要假设端点路径
- ❌ 错误: 假设注册端点是 /auth/signup
- ✅ 正确: 通过OpenAPI规范或实际测试发现端点

### 3. 不要忽视HTTP状态码的含义
- ❌ 错误: 看到404就认为功能不存在
- ✅ 正确: 404可能是路径错误，403可能是权限问题但功能存在

### 4. 不要被注释和文档误导
- ❌ 错误: 相信过时的文档和注释
- ✅ 正确: 以实际运行状态为准

## ✅ 正确的分析步骤

### 1. 系统架构发现
```bash
# 检查运行的容器
docker ps

# 检查网络连接
docker network ls
```

### 2. API端点发现
```bash
# 获取OpenAPI规范
curl http://localhost:9000/openapi.json
curl http://localhost:8000/openapi.json

# 检查API文档
curl http://localhost:9000/docs
```

### 3. 认证系统测试
```bash
# 测试常见的认证端点
curl -X POST http://localhost:9000/api/v1/login/access-token \
  -d "username=<EMAIL>&password=changethis"
```

### 4. 功能验证
```bash
# 使用获取的token测试受保护端点
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:9000/api/v1/users/me
```

## 🔍 关键验证点

1. **架构验证**: 容器数量和命名模式
2. **端点验证**: OpenAPI规范 > 实际测试 > 文档
3. **认证验证**: 实际登录测试
4. **功能验证**: 端到端测试

## 📝 记录模板

```
系统架构: [单体/BFF/微服务]
运行容器: [列出所有容器]
API端点数: [从OpenAPI获取的数量]
认证端点: [实际测试发现的端点]
主要功能: [列出验证通过的功能]
```
