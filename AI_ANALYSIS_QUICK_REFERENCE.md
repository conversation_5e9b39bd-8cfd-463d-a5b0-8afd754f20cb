
# AI系统分析快速参考

## 🚀 快速开始
```bash
# 1. 运行标准化发现脚本
python correct_system_discovery.py

# 2. 运行完整分析工具包
python ai_misunderstanding_prevention_toolkit.py

# 3. 查看生成的分析报告
cat system_analysis_report_*.json
```

## ⚠️ 常见误区
- ❌ 看到Gateway配置多个service_url就认为是微服务
- ❌ 假设端点路径而不查看OpenAPI规范
- ❌ 将404理解为功能不存在
- ❌ 不获取认证token就测试受保护端点
- ❌ 相信过时的文档和注释

## ✅ 正确做法
- ✅ 检查实际运行的Docker容器
- ✅ 获取OpenAPI规范确认端点
- ✅ 理解HTTP状态码含义
- ✅ 完成认证流程后测试功能
- ✅ 以实际运行状态为准

## 🔧 必备命令
```bash
# 检查容器
docker ps

# 获取API规范
curl http://localhost:9000/openapi.json

# 测试认证
curl -X POST http://localhost:9000/api/v1/login/access-token \
  -d "username=<EMAIL>&password=changethis"

# 测试功能
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:9000/api/v1/users/me
```
