"""
Integration test API routes.

This module provides API endpoints to test Manticore and Dramatiq integrations.
"""

from typing import Any, Dict

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

# Import our integration modules
from app.manticore_client import get_manticore_client
from app.tasks import (
    enqueue_hello_world,
    enqueue_slow_task,
    enqueue_important_task,
    enqueue_document_indexing
)

router = APIRouter()


class TaskResponse(BaseModel):
    """Response model for task operations."""
    success: bool
    message: str
    task_id: str = None
    data: Dict[str, Any] = None


class SearchRequest(BaseModel):
    """Request model for search operations."""
    query: str
    table_name: str = "test_table"
    limit: int = 10


class DocumentRequest(BaseModel):
    """Request model for document operations."""
    table_name: str = "test_table"
    document: Dict[str, Any]


@router.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok", "message": "Integration test API is running"}


@router.get("/manticore/health")
async def manticore_health():
    """Check Manticore Search health."""
    try:
        client = get_manticore_client()
        is_healthy = await client.health_check()
        
        if is_healthy:
            return {"status": "healthy", "message": "Manticore Search is running"}
        else:
            raise HTTPException(status_code=503, detail="Manticore Search is not healthy")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error checking Manticore health: {str(e)}")


@router.post("/manticore/search")
async def search_documents(request: SearchRequest):
    """Search documents in Manticore."""
    try:
        client = get_manticore_client()
        results = await client.search(
            table_name=request.table_name,
            query=request.query,
            limit=request.limit
        )
        
        return {
            "success": True,
            "results": results,
            "message": f"Search completed for query: {request.query}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")


@router.post("/manticore/document")
async def add_document(request: DocumentRequest):
    """Add a document to Manticore."""
    try:
        client = get_manticore_client()
        success = await client.insert_document(request.table_name, request.document)
        
        if success:
            return {
                "success": True,
                "message": f"Document added to {request.table_name}",
                "document": request.document
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to add document")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Document insertion error: {str(e)}")


@router.post("/manticore/create-table/{table_name}")
async def create_table(table_name: str):
    """Create a test table in Manticore."""
    try:
        client = get_manticore_client()
        
        # Define a simple schema
        schema = {
            "title": {"type": "text"},
            "content": {"type": "text"},
            "category_id": {"type": "int"},
            "created_at": {"type": "timestamp"}
        }
        
        success = await client.create_table(table_name, schema)
        
        if success:
            return {
                "success": True,
                "message": f"Table '{table_name}' created successfully",
                "schema": schema
            }
        else:
            raise HTTPException(status_code=500, detail=f"Failed to create table '{table_name}'")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Table creation error: {str(e)}")


@router.post("/dramatiq/hello")
async def enqueue_hello_task(name: str = "World"):
    """Enqueue a hello world task."""
    try:
        message = enqueue_hello_world(name)
        
        return TaskResponse(
            success=True,
            message=f"Hello world task enqueued for '{name}'",
            task_id=message.message_id
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Task enqueueing error: {str(e)}")


@router.post("/dramatiq/slow-task")
async def enqueue_slow_task_endpoint(duration: int = 5):
    """Enqueue a slow task."""
    try:
        if duration > 60:
            raise HTTPException(status_code=400, detail="Duration cannot exceed 60 seconds")
            
        message = enqueue_slow_task(duration)
        
        return TaskResponse(
            success=True,
            message=f"Slow task enqueued with duration {duration} seconds",
            task_id=message.message_id,
            data={"duration": duration}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Task enqueueing error: {str(e)}")


@router.post("/dramatiq/important-task")
async def enqueue_important_task_endpoint(data: Dict[str, Any]):
    """Enqueue an important task."""
    try:
        message = enqueue_important_task(data)
        
        return TaskResponse(
            success=True,
            message="Important task enqueued",
            task_id=message.message_id,
            data=data
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Task enqueueing error: {str(e)}")


@router.post("/dramatiq/index-document")
async def enqueue_indexing_task(request: DocumentRequest):
    """Enqueue a document indexing task (combines Manticore + Dramatiq)."""
    try:
        message = enqueue_document_indexing(
            table_name=request.table_name,
            document=request.document
        )
        
        return TaskResponse(
            success=True,
            message=f"Document indexing task enqueued for table '{request.table_name}'",
            task_id=message.message_id,
            data={
                "table_name": request.table_name,
                "document": request.document
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Task enqueueing error: {str(e)}")


@router.get("/test/full-integration")
async def test_full_integration():
    """Test full integration of both Manticore and Dramatiq."""
    results = {
        "manticore": {"status": "unknown", "error": None},
        "dramatiq": {"status": "unknown", "error": None, "task_id": None},
        "integration": {"status": "unknown", "error": None, "task_id": None}
    }
    
    # Test Manticore
    try:
        client = get_manticore_client()
        is_healthy = await client.health_check()
        results["manticore"]["status"] = "healthy" if is_healthy else "unhealthy"
    except Exception as e:
        results["manticore"]["status"] = "error"
        results["manticore"]["error"] = str(e)
    
    # Test Dramatiq
    try:
        message = enqueue_hello_world("Integration Test")
        results["dramatiq"]["status"] = "healthy"
        results["dramatiq"]["task_id"] = message.message_id
    except Exception as e:
        results["dramatiq"]["status"] = "error"
        results["dramatiq"]["error"] = str(e)
    
    # Test integration (Dramatiq task that uses Manticore)
    try:
        test_doc = {
            "id": 999,
            "title": "Integration Test Document",
            "content": "This document was created by the integration test",
            "category_id": 1,
            "created_at": **********
        }
        
        message = enqueue_document_indexing("test_table", test_doc)
        results["integration"]["status"] = "healthy"
        results["integration"]["task_id"] = message.message_id
    except Exception as e:
        results["integration"]["status"] = "error"
        results["integration"]["error"] = str(e)
    
    # Overall status
    overall_status = "healthy" if all(
        r["status"] == "healthy" for r in results.values()
    ) else "partial" if any(
        r["status"] == "healthy" for r in results.values()
    ) else "unhealthy"
    
    return {
        "overall_status": overall_status,
        "components": results,
        "message": "Full integration test completed"
    }
