"""
Dramatiq configuration module.

This module sets up Dramatiq with Redis broker for background task processing.
"""

import logging
import os
from typing import Optional

import dramatiq
from dramatiq.brokers.redis import RedisBroker
from dramatiq.middleware import AgeLimit, TimeLimit, Retries, Callbacks
from dramatiq.results import Results
from dramatiq.results.backends import RedisBackend

logger = logging.getLogger(__name__)


def get_redis_url() -> str:
    """Get Redis URL from environment variables.

    Returns:
        Redis connection URL
    """
    # Check for direct Redis URL first
    redis_url = os.getenv("REDIS_URL")
    if redis_url:
        return redis_url

    # Fallback to individual components for Docker Compose
    redis_host = os.getenv("REDIS_HOST", "localhost")  # Use localhost for local development
    redis_port = int(os.getenv("REDIS_PORT", "6379"))
    redis_db = int(os.getenv("REDIS_DB", "0"))
    redis_password = os.getenv("REDIS_PASSWORD")

    # Build Redis URL
    if redis_password:
        redis_url = f"redis://:{redis_password}@{redis_host}:{redis_port}/{redis_db}"
    else:
        redis_url = f"redis://{redis_host}:{redis_port}/{redis_db}"

    return redis_url


def setup_dramatiq_broker() -> RedisBroker:
    """Set up and configure Dramatiq Redis broker.
    
    Returns:
        Configured RedisBroker instance
    """
    redis_url = get_redis_url()
    logger.info(f"Setting up Dramatiq with Redis: {redis_url}")
    
    # Create Redis broker
    broker = RedisBroker(url=redis_url)
    
    # Set up result backend for task results
    result_backend = RedisBackend(url=redis_url)
    
    # Configure middleware
    broker.add_middleware(AgeLimit(max_age=3600000))  # 1 hour max age
    broker.add_middleware(TimeLimit(time_limit=300000))  # 5 minutes timeout
    broker.add_middleware(Retries(max_retries=3))  # Retry failed tasks 3 times
    broker.add_middleware(Callbacks())  # Enable callbacks
    broker.add_middleware(Results(backend=result_backend))  # Enable results storage
    
    # Set the broker globally
    dramatiq.set_broker(broker)
    
    logger.info("Dramatiq broker configured successfully")
    return broker


def init_dramatiq():
    """Initialize Dramatiq configuration."""
    try:
        broker = setup_dramatiq_broker()
        logger.info("Dramatiq initialized successfully")
        return broker
    except Exception as e:
        logger.error(f"Failed to initialize Dramatiq: {e}")
        raise


# Global broker instance
_broker: Optional[RedisBroker] = None


def get_broker() -> RedisBroker:
    """Get the global Dramatiq broker instance.
    
    Returns:
        RedisBroker instance
    """
    global _broker
    if _broker is None:
        _broker = init_dramatiq()
    return _broker


# Initialize broker when module is imported
try:
    _broker = init_dramatiq()
except Exception as e:
    logger.warning(f"Could not initialize Dramatiq broker on import: {e}")
    _broker = None
