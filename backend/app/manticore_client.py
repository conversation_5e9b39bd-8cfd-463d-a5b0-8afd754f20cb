"""
Manticore Search async client module.

This module provides an async client for interacting with Manticore Search.
Based on POC validation using official manticoresearch-asyncio-devel package.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional

# Use the official async client
import manticoresearch
from manticoresearch.rest import ApiException

logger = logging.getLogger(__name__)


class ManticoreClient:
    """Async Manticore Search client using official async client."""

    def __init__(
        self,
        host: str = "manticore",
        port: int = 9308,
        scheme: str = "http"
    ):
        """Initialize Manticore client.

        Args:
            host: Manticore server host
            port: Manticore server port (HTTP API port)
            scheme: Connection scheme (http/https)
        """
        self.host = host
        self.port = port
        self.scheme = scheme
        self.url = f"{scheme}://{host}:{port}"

        # Configure the client
        self.configuration = manticoresearch.Configuration(host=self.url)

        # API client will be created in async context
        self.api_client = None
        self.search_api = None
        self.index_api = None
        self.utils_api = None

    async def __aenter__(self):
        """Async context manager entry."""
        self.api_client = manticoresearch.ApiClient(self.configuration)
        self.search_api = manticoresearch.SearchApi(self.api_client)
        self.index_api = manticoresearch.IndexApi(self.api_client)
        self.utils_api = manticoresearch.UtilsApi(self.api_client)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.api_client:
            await self.api_client.close()

    async def health_check(self) -> bool:
        """Check if Manticore server is healthy.

        Returns:
            True if server is healthy, False otherwise
        """
        try:
            if not self.utils_api:
                return False
            # Use utils API to check server status with true async
            response = await self.utils_api.sql("SHOW VERSION")
            return True
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    async def create_table(self, table_name: str, schema: Dict[str, Any]) -> bool:
        """Create a new table in Manticore.
        
        Args:
            table_name: Name of the table to create
            schema: Table schema definition
            
        Returns:
            True if table created successfully, False otherwise
        """
        try:
            # Build CREATE TABLE SQL
            fields = []
            attributes = []
            
            for field_name, field_config in schema.items():
                field_type = field_config.get("type", "text")
                if field_type == "text":
                    fields.append(field_name)
                else:
                    attributes.append(f"{field_name} {field_type}")
            
            # Construct CREATE TABLE statement
            sql_parts = [f"CREATE TABLE {table_name} ("]
            
            # Add text fields
            if fields:
                for field in fields:
                    sql_parts.append(f"    {field} text,")
            
            # Add attributes
            if attributes:
                for attr in attributes:
                    sql_parts.append(f"    {attr},")
            
            # Remove last comma and close
            if sql_parts[-1].endswith(","):
                sql_parts[-1] = sql_parts[-1][:-1]
            
            sql_parts.append(")")
            
            sql = "\n".join(sql_parts)

            await self.utils_api.sql(sql)
            logger.info(f"Table '{table_name}' created successfully")
            return True
            
        except ApiException as e:
            logger.error(f"Failed to create table '{table_name}': {e}")
            return False
    
    async def insert_document(
        self, 
        table_name: str, 
        document: Dict[str, Any]
    ) -> bool:
        """Insert a document into a table.
        
        Args:
            table_name: Name of the table
            document: Document data to insert
            
        Returns:
            True if document inserted successfully, False otherwise
        """
        try:
            # Use the correct parameter name for async client
            # Extract id from document if present
            doc_id = document.pop("id", None)
            insert_request = {
                "table": table_name,
                "doc": document
            }

            # Add id separately if provided
            if doc_id is not None:
                insert_request["id"] = doc_id

            await self.index_api.insert(insert_request)

            logger.info(f"Document inserted into '{table_name}' successfully")
            return True
            
        except ApiException as e:
            logger.error(f"Failed to insert document into '{table_name}': {e}")
            return False
    
    async def search(
        self,
        table_name: str,
        query: str,
        limit: int = 10,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Search documents in a table.
        
        Args:
            table_name: Name of the table to search
            query: Search query
            limit: Maximum number of results to return
            offset: Number of results to skip
            filters: Additional filters to apply
            
        Returns:
            Search results dictionary
        """
        try:
            # Build search request for async client
            search_request = {
                "table": table_name,
                "query": {"match": {"*": query}} if query else {"match_all": {}},
                "limit": limit,
                "offset": offset
            }

            # Add filters if provided
            if filters:
                search_request["filter"] = filters

            response = await self.search_api.search(search_request)

            # Handle response based on async client structure
            if hasattr(response, 'to_dict'):
                response_dict = response.to_dict()
            else:
                response_dict = response

            # Extract hits information
            hits_info = response_dict.get("hits", {})
            result = {
                "hits": hits_info.get("hits", []),
                "total": hits_info.get("total", 0),
                "took": response_dict.get("took", 0),
                "timed_out": response_dict.get("timed_out", False)
            }

            logger.info(f"Search completed: {result['total']} results found")
            return result
            
        except ApiException as e:
            logger.error(f"Search failed: {e}")
            return {
                "hits": [],
                "total": 0,
                "took": 0,
                "timed_out": False,
                "error": str(e)
            }
    
    async def update_document(
        self,
        table_name: str,
        doc_id: int,
        document: Dict[str, Any]
    ) -> bool:
        """Update a document in a table.
        
        Args:
            table_name: Name of the table
            doc_id: Document ID to update
            document: Updated document data
            
        Returns:
            True if document updated successfully, False otherwise
        """
        try:
            update_request = {
                "table": table_name,
                "id": doc_id,
                "doc": document
            }

            await self.index_api.update(update_request)

            logger.info(f"Document {doc_id} updated in '{table_name}' successfully")
            return True
            
        except ApiException as e:
            logger.error(f"Failed to update document {doc_id} in '{table_name}': {e}")
            return False
    
    async def delete_document(
        self,
        table_name: str,
        doc_id: int
    ) -> bool:
        """Delete a document from a table.
        
        Args:
            table_name: Name of the table
            doc_id: Document ID to delete
            
        Returns:
            True if document deleted successfully, False otherwise
        """
        try:
            delete_request = {
                "table": table_name,
                "id": doc_id
            }

            await self.index_api.delete(delete_request)

            logger.info(f"Document {doc_id} deleted from '{table_name}' successfully")
            return True
            
        except ApiException as e:
            logger.error(f"Failed to delete document {doc_id} from '{table_name}': {e}")
            return False
    
    async def execute_sql(self, sql: str) -> Dict[str, Any]:
        """Execute raw SQL query.
        
        Args:
            sql: SQL query to execute
            
        Returns:
            Query results
        """
        try:
            response = await self.utils_api.sql(sql)
            logger.info(f"SQL executed successfully: {sql[:100]}...")
            return response
            
        except ApiException as e:
            logger.error(f"SQL execution failed: {e}")
            return {"error": str(e)}
    
    async def close(self):
        """Close the client connection."""
        if self.api_client:
            await self.api_client.close()


# Global client instance
_manticore_client: Optional[ManticoreClient] = None


def get_manticore_client() -> ManticoreClient:
    """Get the global Manticore client instance.

    Returns:
        ManticoreClient instance
    """
    global _manticore_client
    if _manticore_client is None:
        from app.core.config import settings
        _manticore_client = ManticoreClient(
            host=settings.MANTICORE_HOST,
            port=settings.MANTICORE_HTTP_PORT,
            scheme=settings.MANTICORE_SCHEME
        )
    return _manticore_client


async def init_manticore_client(
    host: str = None,
    port: int = None,
    scheme: str = None
) -> ManticoreClient:
    """Initialize the global Manticore client.

    Args:
        host: Manticore server host (defaults to config)
        port: Manticore server port (defaults to config)
        scheme: Connection scheme (defaults to config)

    Returns:
        Initialized ManticoreClient instance
    """
    global _manticore_client
    from app.core.config import settings

    _manticore_client = ManticoreClient(
        host=host or settings.MANTICORE_HOST,
        port=port or settings.MANTICORE_HTTP_PORT,
        scheme=scheme or settings.MANTICORE_SCHEME
    )

    # Test connection using async context manager
    async with _manticore_client as client:
        if await client.health_check():
            logger.info("Manticore client initialized successfully")
        else:
            logger.warning("Manticore client initialized but health check failed")

    return _manticore_client
