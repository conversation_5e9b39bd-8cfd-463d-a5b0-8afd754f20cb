"""
文档分块服务

负责文档的分块处理，包括：
- 文档分割
- 分块存储
- 分块查询和管理
"""

import uuid
from typing import List, Optional

from sqlmodel import Session

from engines.text_splitter import TextSplitterEngine
from engines.text_splitter.models import Document as EngineDocument, TextChunk
from engines.text_splitter.strategies import SplitStrategy

from app.models import Document, DocumentChunk, DocumentChunkCreate
from app.crud.document import (
    create_document_chunk,
    get_document_chunks,
    delete_document_chunks,
)


class ChunkService:
    """文档分块服务类"""
    
    def __init__(self, session: Session):
        """
        初始化分块服务
        
        Args:
            session: 数据库会话
        """
        self.session = session
        self.text_splitter = TextSplitterEngine()
    
    def split_document(
        self,
        document: Document,
        strategy: Optional[SplitStrategy] = None
    ) -> List[DocumentChunk]:
        """
        分割文档并保存分块
        
        Args:
            document: 要分割的文档
            strategy: 分割策略，如果为None则自动选择
            
        Returns:
            创建的文档分块列表
        """
        # 准备引擎文档对象
        engine_doc = EngineDocument(
            id=str(document.id),
            title=document.title,
            content=document.content,
            file_type=document.file_type,
            size=document.size,
            created_at=document.created_at
        )
        
        # 执行分割
        split_result = self.text_splitter.split_document(engine_doc, strategy)
        
        # 清除现有的分块（如果有）
        delete_document_chunks(session=self.session, document_id=document.id)
        
        # 保存新的分块
        db_chunks = []
        for chunk in split_result.chunks:
            chunk_create = DocumentChunkCreate(
                document_id=document.id,
                content=chunk.content,
                chunk_index=chunk.chunk_index,
                start_char=chunk.start_char,
                end_char=chunk.end_char,
                token_count=chunk.token_count
            )
            
            db_chunk = create_document_chunk(
                session=self.session,
                chunk_in=chunk_create
            )
            db_chunks.append(db_chunk)
        
        return db_chunks
    
    def get_document_chunks(
        self,
        document_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[DocumentChunk]:
        """
        获取文档的分块列表
        
        Args:
            document_id: 文档ID
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            文档分块列表
        """
        return get_document_chunks(
            session=self.session,
            document_id=document_id,
            skip=skip,
            limit=limit
        )
    
    def delete_document_chunks(self, document_id: uuid.UUID) -> int:
        """
        删除文档的所有分块
        
        Args:
            document_id: 文档ID
            
        Returns:
            删除的分块数量
        """
        return delete_document_chunks(
            session=self.session,
            document_id=document_id
        )
    
    def get_chunk_statistics(self, document_id: uuid.UUID) -> dict:
        """
        获取文档分块统计信息
        
        Args:
            document_id: 文档ID
            
        Returns:
            统计信息字典
        """
        chunks = self.get_document_chunks(document_id, limit=1000)
        
        if not chunks:
            return {
                "total_chunks": 0,
                "total_characters": 0,
                "total_tokens": 0,
                "avg_chunk_size": 0,
                "avg_token_count": 0
            }
        
        total_chars = sum(len(chunk.content) for chunk in chunks)
        total_tokens = sum(chunk.token_count or 0 for chunk in chunks)
        
        return {
            "total_chunks": len(chunks),
            "total_characters": total_chars,
            "total_tokens": total_tokens,
            "avg_chunk_size": total_chars / len(chunks),
            "avg_token_count": total_tokens / len(chunks) if total_tokens > 0 else 0,
            "min_chunk_size": min(len(chunk.content) for chunk in chunks),
            "max_chunk_size": max(len(chunk.content) for chunk in chunks)
        }
    
    def rebuild_chunks(
        self,
        document: Document,
        strategy: Optional[SplitStrategy] = None
    ) -> List[DocumentChunk]:
        """
        重新构建文档分块
        
        Args:
            document: 文档对象
            strategy: 分割策略
            
        Returns:
            重新创建的分块列表
        """
        return self.split_document(document, strategy)
