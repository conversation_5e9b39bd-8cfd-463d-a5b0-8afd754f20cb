"""
文档管理服务

提供文档的创建、更新、查询、删除等核心功能
与数据库 CRUD 操作和异步任务系统集成
"""

import uuid
from typing import List, Optional
from datetime import datetime

from sqlmodel import Session

from engines.text_splitter import TextSplitterEngine
from engines.text_splitter.models import Document as EngineDocument
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy

from app.models import Document, DocumentCreate, DocumentUpdate
from app.crud.document import (
    create_document,
    get_document,
    get_documents_by_owner,
    update_document,
    delete_document,
)
from app.services.search.search_manager import SearchManager, get_search_manager


class DocumentService:
    """文档管理服务类"""
    
    def __init__(self, session: Session, search_manager: Optional[SearchManager] = None):
        """
        初始化文档服务

        Args:
            session: 数据库会话
            search_manager: 搜索管理器实例
        """
        self.session = session
        self.text_splitter = TextSplitterEngine()
        self.search_manager = search_manager or get_search_manager()
    
    def create_document(
        self, 
        document_in: DocumentCreate, 
        owner_id: uuid.UUID,
        auto_process: bool = True
    ) -> Document:
        """
        创建文档
        
        Args:
            document_in: 文档创建数据
            owner_id: 文档所有者ID
            auto_process: 是否自动触发异步处理
            
        Returns:
            创建的文档对象
        """
        # 创建文档记录
        document = create_document(
            session=self.session,
            document_in=document_in,
            owner_id=owner_id
        )
        
        # 如果启用自动处理，触发异步文档分割任务
        if auto_process:
            from app.tasks import process_document_task
            process_document_task.send(str(document.id))
        
        return document
    
    def get_document(self, document_id: uuid.UUID) -> Optional[Document]:
        """
        获取文档
        
        Args:
            document_id: 文档ID
            
        Returns:
            文档对象或None
        """
        return get_document(session=self.session, document_id=document_id)
    
    def get_user_documents(
        self,
        owner_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[Document]:
        """
        获取用户的文档列表
        
        Args:
            owner_id: 用户ID
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            文档列表
        """
        return get_documents_by_owner(
            session=self.session,
            owner_id=owner_id,
            skip=skip,
            limit=limit
        )
    
    def update_document(
        self,
        document_id: uuid.UUID,
        document_in: DocumentUpdate,
        reprocess: bool = False
    ) -> Optional[Document]:
        """
        更新文档
        
        Args:
            document_id: 文档ID
            document_in: 更新数据
            reprocess: 是否重新处理文档分割
            
        Returns:
            更新后的文档对象或None
        """
        db_document = self.get_document(document_id)
        if not db_document:
            return None
        
        updated_document = update_document(
            session=self.session,
            db_document=db_document,
            document_in=document_in
        )
        
        # 如果内容发生变化且需要重新处理，触发异步任务
        if reprocess and document_in.content is not None:
            from app.tasks import process_document_task
            process_document_task.send(str(updated_document.id))
        
        return updated_document
    
    def delete_document(self, document_id: uuid.UUID) -> Optional[Document]:
        """
        删除文档
        
        Args:
            document_id: 文档ID
            
        Returns:
            被删除的文档对象或None
        """
        return delete_document(session=self.session, document_id=document_id)
    
    def prepare_for_splitting(self, document: Document) -> EngineDocument:
        """
        将数据库文档转换为引擎文档格式
        
        Args:
            document: 数据库文档对象
            
        Returns:
            引擎文档对象
        """
        return EngineDocument(
            id=str(document.id),
            title=document.title,
            content=document.content,
            file_type=document.file_type,
            size=document.size,
            created_at=document.created_at
        )
    
    def get_split_strategy(self, document: Document):
        """
        根据文档类型确定分割策略
        
        Args:
            document: 文档对象
            
        Returns:
            分割策略对象
        """
        if document.file_type.lower() in ['md', 'markdown']:
            return CharacterBasedStrategy(max_chars=1000)
        else:
            return TokenBasedStrategy(max_tokens=500)

    async def index_document_for_search(self, document: Document) -> bool:
        """
        将文档索引到搜索引擎

        Args:
            document: 文档对象

        Returns:
            是否索引成功
        """
        try:
            return await self.search_manager.index_document_chunk(
                doc_id=int(str(document.id).replace('-', '')[:10], 16),  # Convert UUID to int
                title=document.title,
                content=document.content,
                source=document.file_path or "",
                chunk_index=0
            )
        except Exception as e:
            print(f"Failed to index document {document.id}: {e}")
            return False

    async def search_documents(
        self,
        query: str,
        owner_id: Optional[uuid.UUID] = None,
        search_type: str = "hybrid",
        limit: int = 10,
        offset: int = 0
    ) -> dict:
        """
        搜索文档

        Args:
            query: 搜索查询
            owner_id: 文档所有者ID（可选，用于权限过滤）
            search_type: 搜索类型 ("text", "vector", "hybrid")
            limit: 返回结果数量限制
            offset: 跳过的结果数量

        Returns:
            搜索结果字典
        """
        try:
            # 构建过滤条件
            filters = {}
            if owner_id:
                # 注意：这里需要根据实际的索引结构调整过滤条件
                # 可能需要在索引时添加owner_id字段
                pass

            results = await self.search_manager.search(
                query=query,
                search_type=search_type,
                limit=limit,
                offset=offset,
                filters=filters if filters else None
            )

            return results
        except Exception as e:
            print(f"Search failed: {e}")
            return {
                "hits": [],
                "total": 0,
                "took": 0,
                "timed_out": False,
                "error": str(e)
            }

    async def semantic_search(
        self,
        query: str,
        owner_id: Optional[uuid.UUID] = None,
        limit: int = 10,
        similarity_threshold: float = 0.7
    ) -> dict:
        """
        语义搜索文档

        Args:
            query: 搜索查询
            owner_id: 文档所有者ID（可选）
            limit: 返回结果数量限制
            similarity_threshold: 相似度阈值

        Returns:
            搜索结果字典
        """
        try:
            results = await self.search_manager.semantic_search(
                query=query,
                limit=limit,
                similarity_threshold=similarity_threshold
            )

            return results
        except Exception as e:
            print(f"Semantic search failed: {e}")
            return {
                "hits": [],
                "total": 0,
                "took": 0,
                "timed_out": False,
                "error": str(e)
            }

    async def remove_from_search_index(self, document: Document) -> bool:
        """
        从搜索索引中移除文档

        Args:
            document: 文档对象

        Returns:
            是否移除成功
        """
        try:
            doc_id = int(str(document.id).replace('-', '')[:10], 16)
            return await self.search_manager.delete_document(doc_id)
        except Exception as e:
            print(f"Failed to remove document {document.id} from search index: {e}")
            return False
