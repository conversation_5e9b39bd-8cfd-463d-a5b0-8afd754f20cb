"""
文档异步处理服务

负责文档的异步处理任务，包括：
- 文档分割处理
- 向量化处理
- 索引处理
"""

import uuid
import logging
from typing import Optional, Dict, Any

from sqlmodel import Session

from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy

from app.core.db import engine
from app.models import Document
from app.crud.document import get_document
from .document_service import DocumentService
from .chunk_service import ChunkService

logger = logging.getLogger(__name__)


class ProcessingService:
    """文档异步处理服务类"""
    
    def __init__(self, session: Optional[Session] = None):
        """
        初始化处理服务
        
        Args:
            session: 数据库会话，如果为None则自动创建
        """
        self.session = session
        self._document_service = None
        self._chunk_service = None
    
    @property
    def document_service(self) -> DocumentService:
        """获取文档服务实例"""
        if self._document_service is None:
            if self.session is None:
                from sqlmodel import Session
                session = Session(engine)
            else:
                session = self.session
            self._document_service = DocumentService(session)
        return self._document_service

    @property
    def chunk_service(self) -> ChunkService:
        """获取分块服务实例"""
        if self._chunk_service is None:
            if self.session is None:
                from sqlmodel import Session
                session = Session(engine)
            else:
                session = self.session
            self._chunk_service = ChunkService(session)
        return self._chunk_service
    
    def process_document(self, document_id: str) -> Dict[str, Any]:
        """
        处理文档的主要方法
        
        Args:
            document_id: 文档ID字符串
            
        Returns:
            处理结果字典
        """
        try:
            # 转换文档ID
            doc_uuid = uuid.UUID(document_id)
            
            # 获取文档
            document = self.document_service.get_document(doc_uuid)
            if not document:
                raise ValueError(f"Document not found: {document_id}")
            
            logger.info(f"Starting document processing for: {document.title}")
            
            # 确定分割策略
            strategy = self._determine_strategy(document)
            
            # 执行文档分割
            chunks = self.chunk_service.split_document(document, strategy)
            
            # 获取统计信息
            stats = self.chunk_service.get_chunk_statistics(doc_uuid)
            
            result = {
                "success": True,
                "document_id": document_id,
                "document_title": document.title,
                "chunks_created": len(chunks),
                "strategy_used": strategy.__class__.__name__,
                "statistics": stats,
                "message": f"Successfully processed document '{document.title}' into {len(chunks)} chunks"
            }
            
            logger.info(f"Document processing completed: {result['message']}")
            return result
            
        except Exception as e:
            error_msg = f"Failed to process document {document_id}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            return {
                "success": False,
                "document_id": document_id,
                "error": str(e),
                "message": error_msg
            }
    
    def reprocess_document(self, document_id: str) -> Dict[str, Any]:
        """
        重新处理文档
        
        Args:
            document_id: 文档ID字符串
            
        Returns:
            处理结果字典
        """
        logger.info(f"Reprocessing document: {document_id}")
        return self.process_document(document_id)
    
    def process_document_with_custom_strategy(
        self,
        document_id: str,
        strategy_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        使用自定义策略处理文档
        
        Args:
            document_id: 文档ID字符串
            strategy_config: 策略配置
            
        Returns:
            处理结果字典
        """
        try:
            doc_uuid = uuid.UUID(document_id)
            document = self.document_service.get_document(doc_uuid)
            if not document:
                raise ValueError(f"Document not found: {document_id}")
            
            # 创建自定义策略
            strategy = self._create_strategy_from_config(strategy_config)
            
            # 执行分割
            chunks = self.chunk_service.split_document(document, strategy)
            
            result = {
                "success": True,
                "document_id": document_id,
                "chunks_created": len(chunks),
                "strategy_config": strategy_config,
                "message": f"Successfully processed document with custom strategy"
            }
            
            logger.info(f"Custom strategy processing completed: {result['message']}")
            return result
            
        except Exception as e:
            error_msg = f"Failed to process document {document_id} with custom strategy: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            return {
                "success": False,
                "document_id": document_id,
                "error": str(e),
                "strategy_config": strategy_config,
                "message": error_msg
            }
    
    def _determine_strategy(self, document: Document):
        """
        根据文档类型确定分割策略
        
        Args:
            document: 文档对象
            
        Returns:
            分割策略对象
        """
        if document.file_type.lower() in ['md', 'markdown']:
            return CharacterBasedStrategy(max_chars=1000)
        elif document.file_type.lower() in ['txt', 'text']:
            return TokenBasedStrategy(max_tokens=500)
        else:
            # 默认策略
            return TokenBasedStrategy(max_tokens=500)
    
    def _create_strategy_from_config(self, config: Dict[str, Any]):
        """
        从配置创建分割策略
        
        Args:
            config: 策略配置
            
        Returns:
            分割策略对象
        """
        strategy_type = config.get("type", "token")
        
        if strategy_type == "token":
            max_tokens = config.get("max_tokens", 500)
            model_name = config.get("model_name", "gpt-3.5-turbo")
            return TokenBasedStrategy(max_tokens=max_tokens, model_name=model_name)
        elif strategy_type == "character":
            max_chars = config.get("max_chars", 1000)
            return CharacterBasedStrategy(max_chars=max_chars)
        else:
            raise ValueError(f"Unsupported strategy type: {strategy_type}")
    
    def get_processing_status(self, document_id: str) -> Dict[str, Any]:
        """
        获取文档处理状态
        
        Args:
            document_id: 文档ID字符串
            
        Returns:
            状态信息字典
        """
        try:
            doc_uuid = uuid.UUID(document_id)
            document = self.document_service.get_document(doc_uuid)
            if not document:
                return {
                    "document_id": document_id,
                    "status": "not_found",
                    "message": "Document not found"
                }
            
            chunks = self.chunk_service.get_document_chunks(doc_uuid, limit=1)
            has_chunks = len(chunks) > 0
            
            stats = self.chunk_service.get_chunk_statistics(doc_uuid) if has_chunks else {}
            
            return {
                "document_id": document_id,
                "document_title": document.title,
                "status": "processed" if has_chunks else "pending",
                "has_chunks": has_chunks,
                "statistics": stats,
                "last_updated": document.updated_at.isoformat() if document.updated_at else None
            }
            
        except Exception as e:
            return {
                "document_id": document_id,
                "status": "error",
                "error": str(e),
                "message": f"Failed to get processing status: {str(e)}"
            }
