"""
Manticore Search service module.

This module provides high-level search functionality using Manticore Search.
Based on POC validation with official async client.
"""

import logging
from typing import Any, Dict, List, Optional, Union
import numpy as np

from app.manticore_client import ManticoreClient, get_manticore_client

logger = logging.getLogger(__name__)


class ManticoreSearchService:
    """High-level Manticore Search service."""
    
    def __init__(self, client: Optional[ManticoreClient] = None):
        """Initialize the search service.
        
        Args:
            client: Optional ManticoreClient instance. If None, uses global client.
        """
        self.client = client or get_manticore_client()
    
    async def create_documents_table(self, table_name: str = "documents") -> bool:
        """Create a documents table with standard schema.
        
        Args:
            table_name: Name of the table to create
            
        Returns:
            True if table created successfully, False otherwise
        """
        schema = {
            "id": {"type": "bigint"},
            "title": {"type": "text"},
            "content": {"type": "text"},
            "source": {"type": "string"},
            "chunk_index": {"type": "int"}
            # Note: embedding field will be added separately due to syntax complexity
        }
        
        async with self.client as client:
            # Drop table if exists to ensure clean state
            try:
                await client.execute_sql(f"DROP TABLE IF EXISTS {table_name}")
            except:
                pass
            return await client.create_table(table_name, schema)
    
    async def index_document(
        self,
        table_name: str,
        doc_id: int,
        title: str,
        content: str,
        source: str = "",
        chunk_index: int = 0,
        embedding: Optional[List[float]] = None
    ) -> bool:
        """Index a document in Manticore Search.
        
        Args:
            table_name: Name of the table
            doc_id: Document ID
            title: Document title
            content: Document content
            source: Document source
            chunk_index: Chunk index for document parts
            embedding: Document embedding vector
            
        Returns:
            True if document indexed successfully, False otherwise
        """
        document = {
            "id": doc_id,
            "title": title,
            "content": content,
            "source": source,
            "chunk_index": chunk_index
        }
        
        if embedding:
            document["embedding"] = embedding
        
        async with self.client as client:
            return await client.insert_document(table_name, document)
    
    async def search_documents(
        self,
        table_name: str,
        query: str,
        limit: int = 10,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Search documents using full-text search.
        
        Args:
            table_name: Name of the table to search
            query: Search query
            limit: Maximum number of results
            offset: Number of results to skip
            filters: Additional filters
            
        Returns:
            Search results dictionary
        """
        async with self.client as client:
            return await client.search(table_name, query, limit, offset, filters)
    
    async def vector_search(
        self,
        table_name: str,
        query_vector: List[float],
        k: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Perform vector similarity search.
        
        Args:
            table_name: Name of the table to search
            query_vector: Query embedding vector
            k: Number of nearest neighbors to return
            filters: Additional filters
            
        Returns:
            Search results dictionary
        """
        # Build KNN search request
        search_request = {
            "table": table_name,
            "query": {
                "knn": {
                    "field": "embedding",
                    "query_vector": query_vector,
                    "k": k
                }
            }
        }
        
        if filters:
            search_request["filter"] = filters
        
        async with self.client as client:
            response = await client.search_api.search(search_request)
            
            # Handle response structure
            if hasattr(response, 'to_dict'):
                response_dict = response.to_dict()
            else:
                response_dict = response
            
            hits_info = response_dict.get("hits", {})
            return {
                "hits": hits_info.get("hits", []),
                "total": hits_info.get("total", 0),
                "took": response_dict.get("took", 0),
                "timed_out": response_dict.get("timed_out", False)
            }
    
    async def hybrid_search(
        self,
        table_name: str,
        text_query: str,
        query_vector: Optional[List[float]] = None,
        limit: int = 10,
        text_weight: float = 0.5,
        vector_weight: float = 0.5
    ) -> Dict[str, Any]:
        """Perform hybrid search combining full-text and vector search.
        
        Args:
            table_name: Name of the table to search
            text_query: Text search query
            query_vector: Query embedding vector
            limit: Maximum number of results
            text_weight: Weight for text search results
            vector_weight: Weight for vector search results
            
        Returns:
            Combined search results
        """
        results = []
        
        # Perform text search
        if text_query:
            text_results = await self.search_documents(table_name, text_query, limit)
            for hit in text_results.get("hits", []):
                hit["search_type"] = "text"
                hit["weighted_score"] = hit.get("_score", 0) * text_weight
                results.append(hit)
        
        # Perform vector search
        if query_vector:
            vector_results = await self.vector_search(table_name, query_vector, limit)
            for hit in vector_results.get("hits", []):
                hit["search_type"] = "vector"
                hit["weighted_score"] = hit.get("_score", 0) * vector_weight
                results.append(hit)
        
        # Combine and deduplicate results
        seen_ids = set()
        combined_results = []
        
        for hit in sorted(results, key=lambda x: x.get("weighted_score", 0), reverse=True):
            doc_id = hit.get("_id")
            if doc_id not in seen_ids:
                seen_ids.add(doc_id)
                combined_results.append(hit)
                if len(combined_results) >= limit:
                    break
        
        return {
            "hits": combined_results,
            "total": len(combined_results),
            "took": 0,
            "timed_out": False
        }
    
    async def delete_document(self, table_name: str, doc_id: int) -> bool:
        """Delete a document from the search index.
        
        Args:
            table_name: Name of the table
            doc_id: Document ID to delete
            
        Returns:
            True if document deleted successfully, False otherwise
        """
        async with self.client as client:
            return await client.delete_document(table_name, doc_id)
    
    async def update_document(
        self,
        table_name: str,
        doc_id: int,
        updates: Dict[str, Any]
    ) -> bool:
        """Update a document in the search index.
        
        Args:
            table_name: Name of the table
            doc_id: Document ID to update
            updates: Fields to update
            
        Returns:
            True if document updated successfully, False otherwise
        """
        async with self.client as client:
            return await client.update_document(table_name, doc_id, updates)
    
    async def get_table_stats(self, table_name: str) -> Dict[str, Any]:
        """Get statistics for a table.
        
        Args:
            table_name: Name of the table
            
        Returns:
            Table statistics
        """
        async with self.client as client:
            try:
                response = await client.execute_sql(f"SELECT COUNT(*) as total FROM {table_name}")
                return response
            except Exception as e:
                logger.error(f"Failed to get table stats: {e}")
                return {"error": str(e)}
