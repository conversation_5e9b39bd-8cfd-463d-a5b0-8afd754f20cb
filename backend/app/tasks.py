"""
Background tasks module using Dramatiq.

This module contains background tasks that can be executed asynchronously.
"""

import logging
import time
from datetime import datetime
from typing import Any, Dict, Optional

import dramatiq
from dramatiq import actor

# Import the broker configuration
from app.dramatiq_config import get_broker

logger = logging.getLogger(__name__)

# Ensure broker is initialized
get_broker()


@actor(queue_name="default", max_retries=3)
def hello_world_task(name: str = "World") -> str:
    """Simple hello world task for testing.
    
    Args:
        name: Name to greet
        
    Returns:
        Greeting message
    """
    message = f"Hello, {name}! Task executed at {datetime.now()}"
    logger.info(f"Executing hello_world_task: {message}")
    return message


@actor(queue_name="default", max_retries=3)
def slow_task(duration: int = 5) -> Dict[str, Any]:
    """A slow task that takes some time to complete.
    
    Args:
        duration: How long to sleep in seconds
        
    Returns:
        Task result with timing information
    """
    start_time = time.time()
    logger.info(f"Starting slow_task with duration: {duration} seconds")
    
    # Simulate work
    time.sleep(duration)
    
    end_time = time.time()
    actual_duration = end_time - start_time
    
    result = {
        "requested_duration": duration,
        "actual_duration": actual_duration,
        "start_time": start_time,
        "end_time": end_time,
        "message": f"Task completed after {actual_duration:.2f} seconds"
    }
    
    logger.info(f"Completed slow_task: {result['message']}")
    return result


@actor(queue_name="high_priority", max_retries=5)
def important_task(data: Dict[str, Any]) -> Dict[str, Any]:
    """An important task that processes data.
    
    Args:
        data: Data to process
        
    Returns:
        Processing result
    """
    logger.info(f"Processing important task with data: {data}")
    
    # Simulate data processing
    processed_data = {
        "original": data,
        "processed_at": datetime.now().isoformat(),
        "status": "completed",
        "items_processed": len(data) if isinstance(data, (list, dict)) else 1
    }
    
    logger.info(f"Important task completed: {processed_data}")
    return processed_data


@actor(queue_name="default", max_retries=2)
def failing_task(should_fail: bool = True) -> str:
    """A task that can be used to test failure handling.
    
    Args:
        should_fail: Whether the task should fail
        
    Returns:
        Success message if should_fail is False
        
    Raises:
        Exception: If should_fail is True
    """
    logger.info(f"Executing failing_task with should_fail={should_fail}")
    
    if should_fail:
        logger.error("Task is configured to fail")
        raise Exception("This task was configured to fail for testing purposes")
    
    message = "Task completed successfully (was configured not to fail)"
    logger.info(message)
    return message


@actor(queue_name="search", max_retries=3)
def index_document_task(
    table_name: str,
    document: Dict[str, Any],
    document_id: Optional[int] = None
) -> Dict[str, Any]:
    """Task to index a document in Manticore Search.
    
    This is an example of how you might integrate Manticore with Dramatiq.
    
    Args:
        table_name: Name of the Manticore table
        document: Document data to index
        document_id: Optional document ID
        
    Returns:
        Indexing result
    """
    logger.info(f"Indexing document in table '{table_name}': {document}")
    
    try:
        # Import here to avoid circular imports
        from manticore_client import get_manticore_client
        import asyncio
        
        # Get Manticore client
        client = get_manticore_client()
        
        # Add ID if provided
        if document_id:
            document["id"] = document_id
        
        # Index the document (run async function in sync context)
        success = asyncio.run(client.insert_document(table_name, document))
        
        result = {
            "success": success,
            "table_name": table_name,
            "document_id": document.get("id"),
            "indexed_at": datetime.now().isoformat(),
            "message": "Document indexed successfully" if success else "Failed to index document"
        }
        
        logger.info(f"Document indexing result: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Error indexing document: {e}")
        result = {
            "success": False,
            "table_name": table_name,
            "error": str(e),
            "indexed_at": datetime.now().isoformat(),
            "message": f"Failed to index document: {e}"
        }
        return result


@actor(queue_name="email", max_retries=3)
def send_email_task(
    to_email: str,
    subject: str,
    body: str,
    from_email: Optional[str] = None
) -> Dict[str, Any]:
    """Task to send an email (mock implementation).
    
    Args:
        to_email: Recipient email address
        subject: Email subject
        body: Email body
        from_email: Sender email address
        
    Returns:
        Email sending result
    """
    logger.info(f"Sending email to {to_email} with subject: {subject}")
    
    # Mock email sending - in real implementation, you would use
    # an email service like SendGrid, AWS SES, etc.
    time.sleep(1)  # Simulate network delay
    
    result = {
        "success": True,
        "to_email": to_email,
        "subject": subject,
        "from_email": from_email or "<EMAIL>",
        "sent_at": datetime.now().isoformat(),
        "message": "Email sent successfully (mock)"
    }
    
    logger.info(f"Email sent: {result}")
    return result


# Utility functions for task management

def enqueue_hello_world(name: str = "World") -> dramatiq.Message:
    """Enqueue a hello world task.
    
    Args:
        name: Name to greet
        
    Returns:
        Dramatiq message object
    """
    return hello_world_task.send(name)


def enqueue_slow_task(duration: int = 5) -> dramatiq.Message:
    """Enqueue a slow task.
    
    Args:
        duration: How long the task should take
        
    Returns:
        Dramatiq message object
    """
    return slow_task.send(duration)


def enqueue_important_task(data: Dict[str, Any]) -> dramatiq.Message:
    """Enqueue an important task.
    
    Args:
        data: Data to process
        
    Returns:
        Dramatiq message object
    """
    return important_task.send(data)


def enqueue_document_indexing(
    table_name: str,
    document: Dict[str, Any],
    document_id: Optional[int] = None
) -> dramatiq.Message:
    """Enqueue a document indexing task.
    
    Args:
        table_name: Name of the Manticore table
        document: Document data to index
        document_id: Optional document ID
        
    Returns:
        Dramatiq message object
    """
    return index_document_task.send(table_name, document, document_id)


def enqueue_email(
    to_email: str,
    subject: str,
    body: str,
    from_email: Optional[str] = None
) -> dramatiq.Message:
    """Enqueue an email sending task.

    Args:
        to_email: Recipient email address
        subject: Email subject
        body: Email body
        from_email: Sender email address

    Returns:
        Dramatiq message object
    """
    return send_email_task.send(to_email, subject, body, from_email)


# Document processing tasks

@actor(queue_name="document", max_retries=3)
def process_document_task(document_id: str) -> Dict[str, Any]:
    """Task to process a document (split into chunks).

    Args:
        document_id: Document ID string

    Returns:
        Processing result
    """
    logger.info(f"Processing document: {document_id}")

    try:
        from app.services.document import ProcessingService

        # Create processing service
        processing_service = ProcessingService()

        # Process the document
        result = processing_service.process_document(document_id)

        logger.info(f"Document processing result: {result}")
        return result

    except Exception as e:
        logger.error(f"Error processing document {document_id}: {e}")
        result = {
            "success": False,
            "document_id": document_id,
            "error": str(e),
            "message": f"Failed to process document: {e}"
        }
        return result


@actor(queue_name="document", max_retries=2)
def reprocess_document_task(document_id: str) -> Dict[str, Any]:
    """Task to reprocess a document with new chunks.

    Args:
        document_id: Document ID string

    Returns:
        Processing result
    """
    logger.info(f"Reprocessing document: {document_id}")

    try:
        from app.services.document import ProcessingService

        processing_service = ProcessingService()
        result = processing_service.reprocess_document(document_id)

        logger.info(f"Document reprocessing result: {result}")
        return result

    except Exception as e:
        logger.error(f"Error reprocessing document {document_id}: {e}")
        result = {
            "success": False,
            "document_id": document_id,
            "error": str(e),
            "message": f"Failed to reprocess document: {e}"
        }
        return result


@actor(queue_name="document", max_retries=3)
def process_document_with_strategy_task(
    document_id: str,
    strategy_config: Dict[str, Any]
) -> Dict[str, Any]:
    """Task to process a document with custom strategy.

    Args:
        document_id: Document ID string
        strategy_config: Strategy configuration

    Returns:
        Processing result
    """
    logger.info(f"Processing document {document_id} with custom strategy: {strategy_config}")

    try:
        from app.services.document import ProcessingService

        processing_service = ProcessingService()
        result = processing_service.process_document_with_custom_strategy(
            document_id, strategy_config
        )

        logger.info(f"Custom strategy processing result: {result}")
        return result

    except Exception as e:
        logger.error(f"Error processing document {document_id} with custom strategy: {e}")
        result = {
            "success": False,
            "document_id": document_id,
            "strategy_config": strategy_config,
            "error": str(e),
            "message": f"Failed to process document with custom strategy: {e}"
        }
        return result


# Document processing utility functions

def enqueue_document_processing(document_id: str) -> dramatiq.Message:
    """Enqueue a document processing task.

    Args:
        document_id: Document ID string

    Returns:
        Dramatiq message object
    """
    return process_document_task.send(document_id)


def enqueue_document_reprocessing(document_id: str) -> dramatiq.Message:
    """Enqueue a document reprocessing task.

    Args:
        document_id: Document ID string

    Returns:
        Dramatiq message object
    """
    return reprocess_document_task.send(document_id)


def enqueue_document_processing_with_strategy(
    document_id: str,
    strategy_config: Dict[str, Any]
) -> dramatiq.Message:
    """Enqueue a document processing task with custom strategy.

    Args:
        document_id: Document ID string
        strategy_config: Strategy configuration

    Returns:
        Dramatiq message object
    """
    return process_document_with_strategy_task.send(document_id, strategy_config)


# Embedding tasks

@actor(queue_name="embedding", max_retries=3)
def process_embedding_batch_task(chunk_ids: list[str], texts: list[str]) -> dict[str, Any]:
    """
    Task to process embedding batch asynchronously.
    This would typically send results to Manticore Search.

    Args:
        chunk_ids: List of chunk identifiers
        texts: List of texts to embed

    Returns:
        Processing result
    """
    logger.info(f"Processing embedding batch for {len(texts)} texts")

    try:
        import asyncio
        from app.services.embedding import EmbeddingService

        async def _process():
            # Create embedding service
            embedding_service = EmbeddingService()

            # Get embeddings
            embeddings = await embedding_service.embed_texts(texts)

            # Here you would typically send to Manticore Search
            # For now, we just log the results
            logger.info(f"Generated {len(embeddings)} embeddings for chunks: {chunk_ids[:3]}...")

            # TODO: Send to Manticore Search
            # manticore_client.insert_embeddings(chunk_ids, embeddings)

            return {
                "success": True,
                "processed": len(embeddings),
                "chunk_ids": chunk_ids,
                "embedding_dimensions": len(embeddings[0]) if embeddings else 0
            }

        # Run async function in sync context
        result = asyncio.run(_process())
        logger.info(f"Embedding batch processing completed: {result}")
        return result

    except Exception as e:
        logger.error(f"Error processing embedding batch: {e}")
        return {
            "success": False,
            "error": str(e),
            "chunk_ids": chunk_ids,
            "message": f"Failed to process embedding batch: {e}"
        }


@actor(queue_name="embedding", max_retries=2)
def embed_and_index_task(chunk_id: str, text: str, table_name: str = "documents") -> dict[str, Any]:
    """
    Task to embed text and index it in Manticore Search.

    Args:
        chunk_id: Chunk identifier
        text: Text to embed
        table_name: Manticore table name

    Returns:
        Processing result
    """
    logger.info(f"Embedding and indexing chunk: {chunk_id}")

    try:
        import asyncio
        from app.services.embedding import EmbeddingService

        async def _process():
            # Create embedding service
            embedding_service = EmbeddingService()

            # Get embedding
            embedding = await embedding_service.embed_single_text(text)

            # TODO: Index in Manticore Search
            # manticore_client.insert_document(table_name, {
            #     "id": chunk_id,
            #     "content": text,
            #     "embedding": embedding
            # })

            return {
                "success": True,
                "chunk_id": chunk_id,
                "embedding_dimension": len(embedding),
                "table_name": table_name
            }

        result = asyncio.run(_process())
        logger.info(f"Embed and index completed: {result}")
        return result

    except Exception as e:
        logger.error(f"Error embedding and indexing chunk {chunk_id}: {e}")
        return {
            "success": False,
            "chunk_id": chunk_id,
            "error": str(e),
            "message": f"Failed to embed and index: {e}"
        }


# Embedding utility functions

def enqueue_embedding_batch(chunk_ids: list[str], texts: list[str]) -> dramatiq.Message:
    """Enqueue an embedding batch processing task.

    Args:
        chunk_ids: List of chunk identifiers
        texts: List of texts to embed

    Returns:
        Dramatiq message object
    """
    return process_embedding_batch_task.send(chunk_ids, texts)


def enqueue_embed_and_index(chunk_id: str, text: str, table_name: str = "documents") -> dramatiq.Message:
    """Enqueue an embed and index task.

    Args:
        chunk_id: Chunk identifier
        text: Text to embed
        table_name: Manticore table name

    Returns:
        Dramatiq message object
    """
    return embed_and_index_task.send(chunk_id, text, table_name)
