#!/usr/bin/env python3
"""
Master-Know CLI 对话产品

一个完整的命令行对话界面，支持：
1. 与AI实时对话
2. 文档上传和处理
3. 后端运行状态监控
4. 对话历史管理
5. 搜索功能测试
"""

import os
import sys
import uuid
import asyncio
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
import argparse

# 设置环境变量
os.environ["ENV_FILE"] = "../.env.local"
sys.path.insert(0, str(Path(__file__).parent))

class MasterKnowCLI:
    def __init__(self, debug=False):
        self.debug = debug
        self.conversation_id = str(uuid.uuid4())
        self.user_id = None
        self.current_topic_id = None
        self.conversation_history = []
        self.session = None
        self.engine = None
        
        # 初始化服务
        self.setup_services()
        
    def setup_services(self):
        """初始化所有服务"""
        try:
            from sqlmodel import Session, create_engine, SQLModel
            from app.core.config import settings
            from app import models  # 导入所有模型
            
            # 创建数据库引擎
            self.engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
            SQLModel.metadata.create_all(self.engine)
            
            if self.debug:
                print(f"🔧 [DEBUG] 数据库连接: {settings.SQLALCHEMY_DATABASE_URI}")
                print(f"🔧 [DEBUG] 对话ID: {self.conversation_id}")
            
        except Exception as e:
            print(f"❌ 服务初始化失败: {e}")
            sys.exit(1)
    
    async def create_user_session(self):
        """创建用户会话"""
        try:
            from sqlmodel import Session
            from app.models import UserCreate
            from app.crud.user import create_user, get_user_by_email
            
            with Session(self.engine) as session:
                # 尝试获取或创建用户
                test_email = "<EMAIL>"
                user = get_user_by_email(session=session, email=test_email)
                
                if not user:
                    user_data = UserCreate(
                        email=test_email,
                        password="cli_password",
                        full_name="CLI用户"
                    )
                    user = create_user(session=session, user_create=user_data)
                    if self.debug:
                        print(f"🔧 [DEBUG] 创建新用户: {user.email}")
                else:
                    if self.debug:
                        print(f"🔧 [DEBUG] 使用现有用户: {user.email}")
                
                self.user_id = user.id
                return user
                
        except Exception as e:
            print(f"❌ 用户会话创建失败: {e}")
            return None
    
    async def upload_document(self, file_path: str):
        """上传并处理文档"""
        try:
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return None
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if self.debug:
                print(f"🔧 [DEBUG] 读取文件: {file_path} ({len(content)} 字符)")
            
            from sqlmodel import Session
            from app.models import DocumentCreate, TopicCreate
            from app.crud.document import create_document
            from app.crud.topic import create_topic
            
            with Session(self.engine) as session:
                # 创建主题（如果不存在）
                if not self.current_topic_id:
                    topic_data = TopicCreate(
                        name=f"CLI学习主题_{datetime.now().strftime('%H%M%S')}",
                        description="通过CLI上传的学习材料"
                    )
                    topic = create_topic(session=session, topic_in=topic_data, owner_id=self.user_id)
                    self.current_topic_id = topic.id
                    
                    if self.debug:
                        print(f"🔧 [DEBUG] 创建主题: {topic.name}")
                
                # 创建文档
                doc_data = DocumentCreate(
                    title=Path(file_path).stem,
                    content=content,
                    file_type=Path(file_path).suffix[1:] or "txt",
                    size=len(content.encode('utf-8'))
                )
                
                document = create_document(session=session, document_in=doc_data, owner_id=self.user_id)
                
                if self.debug:
                    print(f"🔧 [DEBUG] 文档存储: {document.title} (ID: {document.id})")
                
                # 处理文档分割
                await self.process_document(document)
                
                print(f"✅ 文档上传成功: {document.title}")
                return document
                
        except Exception as e:
            print(f"❌ 文档上传失败: {e}")
            if self.debug:
                import traceback
                traceback.print_exc()
            return None
    
    async def process_document(self, document):
        """处理文档分割和向量化"""
        try:
            from engines.text_splitter import TextSplitterEngine
            from engines.text_splitter.strategies import TokenBasedStrategy
            from engines.text_splitter.models import Document as EngineDocument
            from app.models import DocumentChunkCreate
            from app.crud.document import create_document_chunk
            from app.services.embedding import EmbeddingService
            from sqlmodel import Session
            
            # 文档分割
            text_engine = TextSplitterEngine()
            strategy = TokenBasedStrategy(max_tokens=300)
            
            engine_doc = EngineDocument(
                title=document.title,
                content=document.content,
                file_type=document.file_type,
                size=document.size
            )
            
            split_result = text_engine.split_document(engine_doc, strategy)
            
            if self.debug:
                print(f"🔧 [DEBUG] 文档分割: {split_result.total_chunks} 个块")
            
            # 存储分块
            with Session(self.engine) as session:
                chunks = []
                for i, chunk in enumerate(split_result.chunks):
                    chunk_data = DocumentChunkCreate(
                        document_id=document.id,
                        content=chunk.content,
                        chunk_index=i,
                        token_count=chunk.token_count,
                        start_char=chunk.start_char,
                        end_char=chunk.end_char
                    )
                    
                    db_chunk = create_document_chunk(session=session, chunk_in=chunk_data)
                    chunks.append(db_chunk)
                    
                    if self.debug:
                        print(f"🔧 [DEBUG] 存储块 {i+1}: {len(chunk.content)} 字符")
            
            # 向量化处理
            embedding_service = EmbeddingService()
            texts = [chunk.content for chunk in split_result.chunks[:5]]  # 限制前5个块
            
            if texts:
                embeddings = await embedding_service.embed_texts(texts)
                if self.debug:
                    print(f"🔧 [DEBUG] 向量化: {len(embeddings)} 个向量，维度 {len(embeddings[0]) if embeddings else 0}")
            
            print(f"✅ 文档处理完成: {split_result.total_chunks} 个块已处理")
            
        except Exception as e:
            print(f"❌ 文档处理失败: {e}")
            if self.debug:
                import traceback
                traceback.print_exc()
    
    async def search_relevant_context(self, user_message: str):
        """搜索相关上下文（实现F4.2 - 主题内记忆）"""
        context_info = {
            "document_chunks": [],
            "historical_summaries": [],
            "recent_messages": []
        }

        try:
            # 1. 搜索相关文档块（知识库）
            if self.debug:
                print(f"🔧 [DEBUG] 搜索相关文档块...")

            from sqlmodel import Session, select
            from app.models import DocumentChunk, Document

            with Session(self.engine) as session:
                # 改进的关键词匹配搜索
                keywords = user_message.lower().split()[:5]  # 取前5个关键词

                # 先尝试直接关键词匹配
                found_chunks = set()  # 避免重复

                for keyword in keywords:
                    if len(keyword) > 2:  # 忽略太短的词
                        stmt = select(DocumentChunk, Document).join(Document).where(
                            DocumentChunk.content.ilike(f"%{keyword}%")
                        ).limit(5)

                        results = session.exec(stmt).all()
                        for chunk, doc in results:
                            chunk_key = f"{chunk.id}_{chunk.chunk_index}"
                            if chunk_key not in found_chunks:
                                found_chunks.add(chunk_key)
                                context_info["document_chunks"].append({
                                    "content": chunk.content[:300] + "..." if len(chunk.content) > 300 else chunk.content,
                                    "document_title": doc.title,
                                    "chunk_index": chunk.chunk_index
                                })

                                if self.debug:
                                    print(f"🔧 [DEBUG] 找到相关文档块: {doc.title} 块{chunk.chunk_index} (关键词: {keyword})")

                # 如果没找到，尝试更宽泛的搜索
                if not context_info["document_chunks"]:
                    if self.debug:
                        print(f"🔧 [DEBUG] 关键词搜索无结果，尝试获取最新文档块...")

                    # 获取最新的几个文档块作为上下文
                    stmt = select(DocumentChunk, Document).join(Document).order_by(
                        DocumentChunk.id.desc()
                    ).limit(3)

                    results = session.exec(stmt).all()
                    for chunk, doc in results:
                        context_info["document_chunks"].append({
                            "content": chunk.content[:300] + "..." if len(chunk.content) > 300 else chunk.content,
                            "document_title": doc.title,
                            "chunk_index": chunk.chunk_index
                        })

                        if self.debug:
                            print(f"🔧 [DEBUG] 添加最新文档块: {doc.title} 块{chunk.chunk_index}")

            # 2. 搜索历史摘要（长期记忆锚点）
            # TODO: 实现摘要搜索功能

            # 3. 获取最近消息（短期记忆）
            context_info["recent_messages"] = self.conversation_history[-6:]  # 最近3轮对话

            if self.debug:
                print(f"🔧 [DEBUG] 上下文统计:")
                print(f"    - 文档块: {len(context_info['document_chunks'])} 个")
                print(f"    - 历史摘要: {len(context_info['historical_summaries'])} 个")
                print(f"    - 最近消息: {len(context_info['recent_messages'])} 条")

            return context_info

        except Exception as e:
            if self.debug:
                print(f"🔧 [DEBUG] 上下文搜索失败: {e}")
            return context_info

    async def build_dynamic_prompt(self, user_message: str, context_info: dict):
        """动态构建包含上下文的Prompt（实现F4.2）"""
        messages = []

        # 1. 固定人设
        system_prompt = """你是Master-Know AI学习导师。你的任务是帮助用户学习和理解知识。

你的工作方式：
1. 优先基于用户上传的文档内容回答问题
2. 结合对话历史提供连贯的学习体验
3. 提供清晰、有用的解释和学习建议
4. 如果文档中没有相关信息，请明确说明并提供通用建议

请始终保持学习导师的身份，耐心、专业地帮助用户理解知识。"""

        # 2. 知识库上下文（从关联文档切片中搜索）
        if context_info["document_chunks"]:
            knowledge_context = "\n\n=== 相关文档内容 ===\n"
            for i, chunk in enumerate(context_info["document_chunks"][:3], 1):
                knowledge_context += f"\n文档《{chunk['document_title']}》片段{chunk['chunk_index']}:\n{chunk['content']}\n"

            system_prompt += knowledge_context

            if self.debug:
                print(f"🔧 [DEBUG] 添加知识库上下文: {len(context_info['document_chunks'])} 个文档块")

        # 3. 长期记忆锚点（从历史摘要中搜索）
        if context_info["historical_summaries"]:
            memory_context = "\n\n=== 相关历史摘要 ===\n"
            for summary in context_info["historical_summaries"][:2]:
                memory_context += f"\n{summary}\n"

            system_prompt += memory_context

            if self.debug:
                print(f"🔧 [DEBUG] 添加长期记忆: {len(context_info['historical_summaries'])} 个摘要")

        messages.append({"role": "system", "content": system_prompt})

        # 4. 短期记忆（最近消息）
        for msg in context_info["recent_messages"]:
            messages.append(msg)

        if self.debug and context_info["recent_messages"]:
            print(f"🔧 [DEBUG] 添加短期记忆: {len(context_info['recent_messages'])} 条最近消息")

        # 5. 当前用户消息
        messages.append({"role": "user", "content": user_message})

        return messages

    async def chat_with_ai(self, user_message: str):
        """与AI对话（实现完整的上下文检索和拼接）"""
        try:
            from app.services.llm import OpenAIProvider
            from app.core.config import settings

            # 创建LLM服务
            llm_provider = OpenAIProvider(
                api_key=settings.LLM_OPENAI_API_KEY,
                base_url=settings.LLM_OPENAI_BASE_URL,
                model="gpt-4o-mini"
            )

            if self.debug:
                print(f"🔧 [DEBUG] 用户消息: {user_message[:50]}...")

            # 1. 搜索相关上下文
            context_info = await self.search_relevant_context(user_message)

            # 2. 动态构建Prompt
            messages = await self.build_dynamic_prompt(user_message, context_info)

            if self.debug:
                total_context_length = sum(len(msg["content"]) for msg in messages)
                print(f"🔧 [DEBUG] 构建完整Prompt: {len(messages)} 条消息, 总长度 {total_context_length} 字符")

            # 3. 调用LLM
            response = await llm_provider.generate(
                messages=messages,
                max_tokens=1000,
                temperature=0.7
            )

            if response and response.get("text"):
                ai_response = response["text"]

                # 保存对话历史
                self.conversation_history.append({"role": "user", "content": user_message})
                self.conversation_history.append({"role": "assistant", "content": ai_response})

                if self.debug:
                    print(f"🔧 [DEBUG] AI响应: {len(ai_response)} 字符")
                    print(f"🔧 [DEBUG] Token使用: {response.get('usage', {}).get('total_tokens', 'N/A')}")
                    print(f"🔧 [DEBUG] 上下文生效: {'是' if context_info['document_chunks'] else '否'}")

                return ai_response
            else:
                print("❌ AI响应为空")
                return None

        except Exception as e:
            print(f"❌ AI对话失败: {e}")
            if self.debug:
                import traceback
                traceback.print_exc()
            return None
    
    async def ensure_manticore_data(self):
        """确保Manticore中有数据（实现F4.1 - 数据持久化）"""
        try:
            import httpx
            from app.core.config import settings
            from sqlmodel import Session, select
            from app.models import DocumentChunk, Document

            manticore_url = f"http://{settings.MANTICORE_HOST}:{settings.MANTICORE_HTTP_PORT}"

            if self.debug:
                print(f"🔧 [DEBUG] 检查Manticore数据同步...")

            # 检查Manticore中是否有数据
            check_sql = "SELECT COUNT(*) as count FROM documents"

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{manticore_url}/cli",
                    data=check_sql,
                    headers={"Content-Type": "text/plain"}
                )

                if response.status_code == 200 and "0" in response.text:
                    # Manticore中没有数据，需要同步
                    if self.debug:
                        print(f"🔧 [DEBUG] Manticore为空，开始同步数据...")

                    with Session(self.engine) as session:
                        # 获取所有文档块
                        stmt = select(DocumentChunk, Document).join(Document)
                        results = session.exec(stmt).all()

                        for chunk, doc in results:
                            # 清理内容
                            clean_content = chunk.content.replace("'", "").replace('"', '').replace('\n', ' ').strip()
                            clean_title = doc.title.replace("'", "").replace('"', '')

                            insert_sql = f"""
                            INSERT INTO documents (id, title, content, chunk_index, document_id, user_id)
                            VALUES ({chunk.id}, '{clean_title}', '{clean_content}',
                                    {chunk.chunk_index}, '{chunk.document_id}', '{doc.owner_id}')
                            """

                            await client.post(
                                f"{manticore_url}/cli",
                                data=insert_sql,
                                headers={"Content-Type": "text/plain"}
                            )

                        if self.debug:
                            print(f"🔧 [DEBUG] 同步了 {len(results)} 个文档块到Manticore")

        except Exception as e:
            if self.debug:
                print(f"🔧 [DEBUG] Manticore数据同步失败: {e}")

    async def search_documents(self, query: str):
        """搜索文档（增强版，显示详细结果）"""
        try:
            import httpx
            from app.core.config import settings

            # 确保Manticore中有数据
            await self.ensure_manticore_data()

            manticore_url = f"http://{settings.MANTICORE_HOST}:{settings.MANTICORE_HTTP_PORT}"

            if self.debug:
                print(f"🔧 [DEBUG] 在Manticore中搜索: '{query}'")

            search_sql = f"""
            SELECT id, title, content, chunk_index
            FROM documents
            WHERE MATCH('{query}')
            LIMIT 5
            """

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{manticore_url}/cli",
                    data=search_sql,
                    headers={"Content-Type": "text/plain"}
                )

                if response.status_code == 200:
                    result_text = response.text.strip()
                    if result_text and "Empty set" not in result_text:
                        print(f"🔍 搜索结果 '{query}':")
                        print("-" * 60)

                        # 解析结果并格式化显示
                        lines = result_text.split('\n')
                        for line in lines:
                            if '|' in line and not line.startswith('+'):
                                parts = [p.strip() for p in line.split('|') if p.strip()]
                                if len(parts) >= 4:
                                    try:
                                        chunk_id, title, content, chunk_index = parts[:4]
                                        if chunk_id.isdigit():  # 确保是数据行
                                            print(f"📄 {title} (块{chunk_index})")
                                            print(f"   {content[:100]}...")
                                            print()
                                    except:
                                        continue

                        if self.debug:
                            print(f"🔧 [DEBUG] 搜索成功，找到相关结果")

                        return True
                    else:
                        print(f"🔍 搜索 '{query}': 无结果")
                        if self.debug:
                            print(f"🔧 [DEBUG] Manticore响应: {result_text}")
                        return False
                else:
                    print(f"❌ 搜索失败: {response.status_code}")
                    if self.debug:
                        print(f"🔧 [DEBUG] 错误响应: {response.text}")
                    return False

        except Exception as e:
            print(f"❌ 搜索功能失败: {e}")
            if self.debug:
                import traceback
                traceback.print_exc()
            return False
    
    def print_help(self):
        """打印帮助信息"""
        help_text = """
🤖 Master-Know CLI 对话系统

可用命令：
  /help          - 显示此帮助信息
  /upload <file> - 上传文档文件
  /search <query>- 搜索文档内容
  /history       - 显示对话历史
  /clear         - 清空对话历史
  /debug         - 切换调试模式
  /quit 或 /exit - 退出程序

直接输入消息即可与AI对话。

示例：
  /upload document.txt
  /search Python编程
  你好，请介绍一下Python的特点
        """
        print(help_text)
    
    def print_history(self):
        """打印对话历史"""
        if not self.conversation_history:
            print("📝 暂无对话历史")
            return
        
        print(f"📝 对话历史 (共 {len(self.conversation_history)//2} 轮):")
        print("-" * 60)
        
        for i, msg in enumerate(self.conversation_history):
            role = "👤 用户" if msg["role"] == "user" else "🤖 AI"
            content = msg["content"]
            if len(content) > 100:
                content = content[:100] + "..."
            print(f"{role}: {content}")
            if i % 2 == 1:  # 每两条消息后添加分隔线
                print("-" * 60)
    
    async def run(self):
        """运行CLI对话系统"""
        print("🚀 Master-Know CLI 对话系统启动")
        print("输入 /help 查看帮助信息")
        
        # 初始化用户会话
        user = await self.create_user_session()
        if not user:
            print("❌ 无法创建用户会话")
            return
        
        print(f"✅ 用户会话已建立: {user.email}")
        print("-" * 60)
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n👤 你: ").strip()
                
                if not user_input:
                    continue
                
                # 处理命令
                if user_input.startswith('/'):
                    command_parts = user_input.split(' ', 1)
                    command = command_parts[0].lower()
                    args = command_parts[1] if len(command_parts) > 1 else ""
                    
                    if command in ['/quit', '/exit']:
                        print("👋 再见！")
                        break
                    elif command == '/help':
                        self.print_help()
                    elif command == '/upload':
                        if args:
                            await self.upload_document(args)
                        else:
                            print("❌ 请指定文件路径: /upload <file_path>")
                    elif command == '/search':
                        if args:
                            await self.search_documents(args)
                        else:
                            print("❌ 请指定搜索关键词: /search <query>")
                    elif command == '/history':
                        self.print_history()
                    elif command == '/clear':
                        self.conversation_history.clear()
                        print("✅ 对话历史已清空")
                    elif command == '/debug':
                        self.debug = not self.debug
                        print(f"🔧 调试模式: {'开启' if self.debug else '关闭'}")
                    else:
                        print(f"❌ 未知命令: {command}")
                        print("输入 /help 查看可用命令")
                
                else:
                    # 与AI对话
                    print("🤖 AI正在思考...")
                    ai_response = await self.chat_with_ai(user_input)
                    
                    if ai_response:
                        print(f"\n🤖 AI: {ai_response}")
                    else:
                        print("❌ AI响应失败，请重试")
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被中断，再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")
                if self.debug:
                    import traceback
                    traceback.print_exc()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Master-Know CLI 对话系统")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    args = parser.parse_args()
    
    cli = MasterKnowCLI(debug=args.debug)
    asyncio.run(cli.run())

if __name__ == "__main__":
    main()
