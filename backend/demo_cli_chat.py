#!/usr/bin/env python3
"""
Master-Know CLI 对话产品演示脚本

自动演示CLI对话产品的所有功能：
1. 系统启动和用户会话
2. 文档上传和处理
3. AI对话交互
4. 搜索功能
5. 后端监控信息
"""

import os
import sys
import asyncio
from pathlib import Path

# 设置环境变量
os.environ["ENV_FILE"] = "../.env.local"
sys.path.insert(0, str(Path(__file__).parent))

async def demo_cli_chat():
    """演示CLI对话产品的完整功能"""
    print("=" * 80)
    print("🎬 Master-Know CLI 对话产品功能演示")
    print("=" * 80)
    
    from cli_chat import MasterKnowCLI
    
    # 创建CLI实例（启用调试模式）
    cli = MasterKnowCLI(debug=True)
    
    print("🚀 第一步：系统初始化和用户会话建立")
    print("-" * 60)
    
    # 创建用户会话
    user = await cli.create_user_session()
    if user:
        print(f"✅ 用户会话建立成功: {user.email}")
    else:
        print("❌ 用户会话建立失败")
        return
    
    print("\n📄 第二步：文档上传和智能处理")
    print("-" * 60)
    
    # 上传测试文档
    document = await cli.upload_document("test_document.md")
    if document:
        print(f"✅ 文档上传成功: {document.title}")
        print(f"📊 文档大小: {document.size} 字节")
    else:
        print("❌ 文档上传失败")
        return
    
    print("\n💬 第三步：AI智能对话")
    print("-" * 60)
    
    # 测试对话
    questions = [
        "请简单介绍一下Django框架的特点",
        "Flask和Django有什么主要区别？",
        "在Web开发中，什么是RESTful API？"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n👤 问题 {i}: {question}")
        print("🤖 AI正在思考...")
        
        response = await cli.chat_with_ai(question)
        if response:
            # 显示响应的前200个字符
            preview = response[:200] + "..." if len(response) > 200 else response
            print(f"🤖 AI回复: {preview}")
        else:
            print("❌ AI响应失败")
    
    print(f"\n📝 对话历史统计: 共 {len(cli.conversation_history)//2} 轮对话")
    
    print("\n🔍 第四步：文档搜索功能")
    print("-" * 60)
    
    # 测试搜索功能
    search_queries = ["Django", "Flask", "API"]
    
    for query in search_queries:
        print(f"\n🔎 搜索: '{query}'")
        result = await cli.search_documents(query)
        if result:
            print(f"✅ 搜索 '{query}' 成功")
        else:
            print(f"⚠️ 搜索 '{query}' 无结果")
    
    print("\n📊 第五步：系统状态总结")
    print("-" * 60)
    
    print(f"🆔 会话ID: {cli.conversation_id}")
    print(f"👤 用户ID: {cli.user_id}")
    print(f"📂 主题ID: {cli.current_topic_id}")
    print(f"💬 对话轮数: {len(cli.conversation_history)//2}")
    print(f"🔧 调试模式: {'开启' if cli.debug else '关闭'}")
    
    print("\n🎉 演示完成！")
    print("=" * 80)
    print("✅ 所有功能演示成功：")
    print("   - 用户会话管理")
    print("   - 文档上传和智能分割")
    print("   - 真实AI对话交互")
    print("   - 文档搜索功能")
    print("   - 完整的后端监控")
    print("\n💡 Master-Know CLI对话产品已完全就绪！")

async def demo_interactive_features():
    """演示交互式功能"""
    print("\n" + "=" * 80)
    print("🎮 交互式功能演示")
    print("=" * 80)
    
    print("📋 可用命令演示:")
    print("   /help          - 显示帮助信息")
    print("   /upload <file> - 上传文档文件")
    print("   /search <query>- 搜索文档内容")
    print("   /history       - 显示对话历史")
    print("   /clear         - 清空对话历史")
    print("   /debug         - 切换调试模式")
    print("   /quit 或 /exit - 退出程序")
    
    print("\n🔧 后端监控信息包括:")
    print("   - 数据库连接状态")
    print("   - 文档处理详情（分割块数、字符数）")
    print("   - AI对话统计（响应长度、Token使用）")
    print("   - 向量化处理（向量数量、维度）")
    print("   - 搜索查询结果")
    print("   - 用户会话信息")
    
    print("\n🚀 启动方式:")
    print("   python cli_chat.py           # 普通模式")
    print("   python cli_chat.py --debug   # 调试模式（推荐）")

def main():
    """主函数"""
    print("🎬 开始演示 Master-Know CLI 对话产品...")
    
    try:
        # 运行主要功能演示
        asyncio.run(demo_cli_chat())
        
        # 演示交互式功能
        asyncio.run(demo_interactive_features())
        
    except KeyboardInterrupt:
        print("\n\n👋 演示被中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
