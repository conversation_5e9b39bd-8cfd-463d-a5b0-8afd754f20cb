"""
Text-Splitter Engine - 语义化文档分割引擎
基于 semantic-text-splitter 库的高级封装
"""

from typing import List, Dict, Any, Optional
from semantic_text_splitter import TextSplitter, MarkdownSplitter
from .strategies import SplitStrategy, TokenBasedStrategy, CharacterBasedStrategy
from .models import TextChunk, Document, SplitResult
from .config import TextSplitterConfig
import logging

logger = logging.getLogger(__name__)

class TextSplitterEngine:
    """文本分割引擎主类"""
    
    def __init__(self, config: Optional[TextSplitterConfig] = None):
        self.config = config or TextSplitterConfig()
        self._splitters: Dict[str, Any] = {}
        self._initialize_splitters()
    
    def _initialize_splitters(self):
        """初始化各种分割器"""
        try:
            # Token-based splitter
            self._splitters['token'] = TextSplitter.from_tiktoken_model(
                self.config.default_model, 
                self.config.default_max_tokens
            )
            
            # Character-based splitter  
            self._splitters['char'] = TextSplitter(self.config.default_max_chars)
            
            # Markdown splitter
            self._splitters['markdown'] = MarkdownSplitter(self.config.default_max_chars)
            
            logger.info("Text splitters initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize splitters: {e}")
            raise
    
    def split_text(self, text: str, strategy: SplitStrategy) -> List[TextChunk]:
        """分割文本的主方法"""
        try:
            splitter = self._get_splitter(strategy)
            raw_chunks = list(splitter.chunks(text))
            
            chunks = []
            char_offset = 0
            
            for i, chunk_text in enumerate(raw_chunks):
                chunk = TextChunk(
                    content=chunk_text,
                    chunk_index=i,
                    start_char=char_offset,
                    end_char=char_offset + len(chunk_text),
                    token_count=self._estimate_tokens(chunk_text)
                )
                chunks.append(chunk)
                char_offset += len(chunk_text)
            
            logger.info(f"Split text into {len(chunks)} chunks")
            return chunks
            
        except Exception as e:
            logger.error(f"Text splitting failed: {e}")
            raise
    
    def split_document(self, document: Document, strategy: Optional[SplitStrategy] = None) -> SplitResult:
        """分割文档"""
        if strategy is None:
            strategy = self._determine_strategy(document)

        chunks = self.split_text(document.content, strategy)

        return SplitResult(
            document_id=document.id,
            chunks=chunks,
            strategy_used=strategy.name,
            total_chunks=len(chunks)
        )
    
    def batch_split(self, documents: List[Document]) -> List[SplitResult]:
        """批量分割文档"""
        results = []
        for doc in documents:
            try:
                result = self.split_document(doc)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to split document {doc.id}: {e}")
                # 创建错误结果
                error_result = SplitResult(
                    document_id=doc.id,
                    chunks=[],
                    strategy_used="error",
                    total_chunks=0,
                    error=str(e)
                )
                results.append(error_result)
        
        return results
    
    def _get_splitter(self, strategy: SplitStrategy):
        """根据策略获取对应的分割器"""
        if isinstance(strategy, TokenBasedStrategy):
            # 为每个策略创建专用的分割器
            return TextSplitter.from_tiktoken_model(
                strategy.model_name,
                strategy.max_tokens
            )
        elif isinstance(strategy, CharacterBasedStrategy):
            # 创建字符基础的分割器
            return TextSplitter(strategy.max_chars)
        else:
            return self._splitters['token']  # 默认使用 token-based
    
    def _determine_strategy(self, document: Document) -> SplitStrategy:
        """根据文档类型确定分割策略"""
        if document.file_type.lower() in ['md', 'markdown']:
            return CharacterBasedStrategy(max_chars=self.config.markdown_max_chars)
        else:
            return TokenBasedStrategy(max_tokens=self.config.default_max_tokens)
    
    def _estimate_tokens(self, text: str) -> int:
        """估算文本的 token 数量"""
        if not text:
            return 0

        # 更准确的估算方法
        # 统计中文字符和英文单词
        import re

        # 中文字符（包括中文标点）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]', text))

        # 英文单词（简单按空格分割）
        english_text = re.sub(r'[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]', '', text)
        english_words = len(english_text.split())

        # 估算：中文字符约 1.3 token/字符，英文单词约 1.3 token/单词
        estimated_tokens = int(chinese_chars * 1.3 + english_words * 1.3)

        # 确保至少返回 1（如果有内容的话）
        return max(1, estimated_tokens) if text.strip() else 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        return {
            "splitters_loaded": len(self._splitters),
            "config": self.config.model_dump()
        }
