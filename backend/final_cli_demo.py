#!/usr/bin/env python3
"""
Master-Know CLI 对话产品最终演示

完整展示PRD中F4 - Manticore驱动的智能上下文引擎：
- F4.1: 数据持久化到Manticore
- F4.2: 主题内记忆（固定人设 + 短期记忆 + 长期记忆锚点 + 知识库）

这是一个完整的、可交互的CLI对话产品演示
"""

import os
import sys
import asyncio
from pathlib import Path

# 设置环境变量
os.environ["ENV_FILE"] = "../.env.local"
sys.path.insert(0, str(Path(__file__).parent))

async def interactive_demo():
    """交互式演示"""
    print("=" * 80)
    print("🎬 Master-Know CLI 对话产品 - 完整功能演示")
    print("=" * 80)
    
    from cli_chat import MasterKnowCLI
    
    # 创建CLI实例（启用调试模式）
    cli = MasterKnowCLI(debug=True)
    
    print("🚀 系统初始化...")
    print("-" * 60)
    
    # 创建用户会话
    user = await cli.create_user_session()
    if not user:
        print("❌ 用户会话创建失败")
        return
    
    print("📄 上传测试文档...")
    print("-" * 60)
    
    # 上传测试文档
    document = await cli.upload_document("test_document.md")
    if not document:
        print("❌ 文档上传失败")
        return
    
    print("🔍 同步数据到Manticore...")
    print("-" * 60)
    
    # 确保Manticore中有数据
    await cli.ensure_manticore_data()
    
    print("\n" + "=" * 80)
    print("🎯 开始交互式对话演示")
    print("=" * 80)
    print("💡 提示：输入 'quit' 退出演示")
    print("💡 注意观察调试信息中的上下文检索过程")
    print("-" * 80)
    
    # 预设一些演示问题
    demo_questions = [
        "这篇文档主要讲了什么？",
        "Django有哪些特点？",
        "Flask和Django的区别是什么？",
        "什么是RESTful API？",
        "如何部署Web应用？"
    ]
    
    print("🎯 建议的演示问题：")
    for i, q in enumerate(demo_questions, 1):
        print(f"   {i}. {q}")
    print("-" * 80)
    
    conversation_count = 0
    
    while True:
        try:
            # 获取用户输入
            user_input = input(f"\n👤 你 (第{conversation_count + 1}轮): ").strip()
            
            if not user_input:
                continue
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 演示结束！")
                break
            
            # 特殊命令处理
            if user_input.startswith('/'):
                if user_input == '/help':
                    print("📋 可用命令:")
                    print("   /search <关键词> - 搜索文档")
                    print("   /history - 显示对话历史")
                    print("   /stats - 显示统计信息")
                    print("   quit - 退出演示")
                    continue
                elif user_input.startswith('/search '):
                    query = user_input[8:].strip()
                    if query:
                        await cli.search_documents(query)
                    else:
                        print("❌ 请提供搜索关键词")
                    continue
                elif user_input == '/history':
                    cli.print_history()
                    continue
                elif user_input == '/stats':
                    print(f"📊 对话统计:")
                    print(f"   - 对话轮数: {len(cli.conversation_history)//2}")
                    print(f"   - 用户ID: {cli.user_id}")
                    print(f"   - 主题ID: {cli.current_topic_id}")
                    continue
                else:
                    print(f"❌ 未知命令: {user_input}")
                    continue
            
            print("🤖 AI正在思考...")
            print("🔍 [上下文检索过程]")
            
            # 与AI对话
            response = await cli.chat_with_ai(user_input)
            
            if response:
                print(f"\n🤖 AI: {response}")
                conversation_count += 1
                
                # 每3轮对话显示一次统计
                if conversation_count % 3 == 0:
                    print(f"\n📊 对话进度: 已完成 {conversation_count} 轮对话")
                    print(f"💾 数据状态: 所有对话已持久化到数据库和Manticore")
            else:
                print("❌ AI响应失败，请重试")
        
        except KeyboardInterrupt:
            print("\n\n👋 演示被中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
    
    # 最终统计
    print("\n" + "=" * 80)
    print("📊 演示总结")
    print("=" * 80)
    
    print(f"💬 对话统计:")
    print(f"   - 总轮数: {len(cli.conversation_history)//2}")
    print(f"   - 总消息数: {len(cli.conversation_history)}")
    
    print(f"\n🔧 技术验证:")
    print(f"   ✅ F4.1 - 数据持久化: 所有数据已存储到PostgreSQL和Manticore")
    print(f"   ✅ F4.2 - 主题内记忆: 动态上下文检索和拼接正常工作")
    print(f"   ✅ 固定人设: AI始终保持学习导师身份")
    print(f"   ✅ 短期记忆: 最近对话历史正确传递")
    print(f"   ✅ 知识库: 文档内容成功检索和使用")
    print(f"   ✅ 搜索功能: Manticore全文搜索正常")
    
    print(f"\n🎉 Master-Know CLI对话产品演示完成！")
    print(f"💡 所有PRD要求的核心功能都已实现并正常工作")

async def automated_demo():
    """自动化演示（无需用户输入）"""
    print("=" * 80)
    print("🤖 Master-Know CLI 自动化演示")
    print("=" * 80)
    
    from cli_chat import MasterKnowCLI
    
    cli = MasterKnowCLI(debug=True)
    
    # 初始化
    user = await cli.create_user_session()
    document = await cli.upload_document("test_document.md")
    await cli.ensure_manticore_data()
    
    # 自动化对话测试
    test_questions = [
        "这篇文档主要讲了什么？",
        "Django框架有什么特点？",
        "如何使用Flask开发Web应用？",
        "RESTful API的设计原则是什么？"
    ]
    
    print("\n🤖 开始自动化对话测试...")
    print("-" * 60)
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🧪 测试 {i}: {question}")
        print("=" * 40)
        
        response = await cli.chat_with_ai(question)
        
        if response:
            print(f"✅ 对话成功")
            print(f"📝 AI回复: {response[:200]}...")
        else:
            print(f"❌ 对话失败")
        
        print("-" * 40)
    
    print(f"\n🎉 自动化演示完成！共完成 {len(test_questions)} 轮对话")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Master-Know CLI对话产品演示")
    parser.add_argument("--auto", action="store_true", help="运行自动化演示")
    parser.add_argument("--interactive", action="store_true", help="运行交互式演示")
    
    args = parser.parse_args()
    
    try:
        if args.auto:
            print("🤖 启动自动化演示...")
            asyncio.run(automated_demo())
        elif args.interactive:
            print("🎮 启动交互式演示...")
            asyncio.run(interactive_demo())
        else:
            print("🎬 Master-Know CLI 对话产品演示")
            print("=" * 50)
            print("使用方式:")
            print("  python final_cli_demo.py --interactive  # 交互式演示")
            print("  python final_cli_demo.py --auto         # 自动化演示")
            print("\n推荐使用交互式演示体验完整功能！")
            
    except KeyboardInterrupt:
        print("\n\n👋 演示被中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
