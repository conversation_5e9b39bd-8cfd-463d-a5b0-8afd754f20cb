#!/usr/bin/env python3
"""
启动本地开发服务

启动PostgreSQL和Manticore搜索服务用于本地开发测试
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def check_docker():
    """检查Docker是否可用"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker可用")
            return True
        else:
            print("❌ Docker不可用")
            return False
    except FileNotFoundError:
        print("❌ Docker未安装")
        return False

def start_postgres():
    """启动PostgreSQL容器"""
    print("🐘 启动PostgreSQL...")
    
    # 检查是否已经运行
    result = subprocess.run(['docker', 'ps', '--filter', 'name=master-know-postgres', '--format', '{{.Names}}'], 
                          capture_output=True, text=True)
    
    if 'master-know-postgres' in result.stdout:
        print("   ✅ PostgreSQL已在运行")
        return True
    
    # 启动PostgreSQL容器
    cmd = [
        'docker', 'run', '-d',
        '--name', 'master-know-postgres',
        '--restart', 'unless-stopped',
        '-e', 'POSTGRES_DB=master_know_local',
        '-e', 'POSTGRES_USER=postgres',
        '-e', 'POSTGRES_PASSWORD=postgres',
        '-p', '5432:5432',
        'postgres:12'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ PostgreSQL启动成功")
            # 等待数据库启动
            print("   ⏳ 等待数据库启动...")
            time.sleep(10)
            return True
        else:
            print(f"   ❌ PostgreSQL启动失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ PostgreSQL启动异常: {e}")
        return False

def start_manticore():
    """启动Manticore搜索容器"""
    print("🔍 启动Manticore搜索...")
    
    # 检查是否已经运行
    result = subprocess.run(['docker', 'ps', '--filter', 'name=master-know-manticore', '--format', '{{.Names}}'], 
                          capture_output=True, text=True)
    
    if 'master-know-manticore' in result.stdout:
        print("   ✅ Manticore已在运行")
        return True
    
    # 启动Manticore容器
    cmd = [
        'docker', 'run', '-d',
        '--name', 'master-know-manticore',
        '--restart', 'unless-stopped',
        '-p', '9306:9306',
        '-p', '9308:9308',
        'manticoresearch/manticore:latest'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ Manticore启动成功")
            # 等待搜索引擎启动
            print("   ⏳ 等待搜索引擎启动...")
            time.sleep(5)
            return True
        else:
            print(f"   ❌ Manticore启动失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ Manticore启动异常: {e}")
        return False

def start_redis():
    """启动Redis容器"""
    print("🔴 启动Redis...")
    
    # 检查是否已经运行
    result = subprocess.run(['docker', 'ps', '--filter', 'name=master-know-redis', '--format', '{{.Names}}'], 
                          capture_output=True, text=True)
    
    if 'master-know-redis' in result.stdout:
        print("   ✅ Redis已在运行")
        return True
    
    # 启动Redis容器
    cmd = [
        'docker', 'run', '-d',
        '--name', 'master-know-redis',
        '--restart', 'unless-stopped',
        '-p', '6379:6379',
        'redis:7-alpine'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ Redis启动成功")
            return True
        else:
            print(f"   ❌ Redis启动失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ Redis启动异常: {e}")
        return False

def check_services():
    """检查服务状态"""
    print("\n🔍 检查服务状态...")
    
    services = [
        ('PostgreSQL', 'master-know-postgres', '5432'),
        ('Manticore', 'master-know-manticore', '9308'),
        ('Redis', 'master-know-redis', '6379')
    ]
    
    all_running = True
    
    for service_name, container_name, port in services:
        result = subprocess.run(['docker', 'ps', '--filter', f'name={container_name}', '--format', '{{.Status}}'], 
                              capture_output=True, text=True)
        
        if result.stdout.strip() and 'Up' in result.stdout:
            print(f"   ✅ {service_name}: 运行中 (端口 {port})")
        else:
            print(f"   ❌ {service_name}: 未运行")
            all_running = False
    
    return all_running

def stop_services():
    """停止所有服务"""
    print("\n🛑 停止本地服务...")
    
    containers = ['master-know-postgres', 'master-know-manticore', 'master-know-redis']
    
    for container in containers:
        try:
            subprocess.run(['docker', 'stop', container], capture_output=True)
            subprocess.run(['docker', 'rm', container], capture_output=True)
            print(f"   ✅ 已停止: {container}")
        except Exception as e:
            print(f"   ⚠️ 停止失败: {container} - {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Master-Know 本地服务管理")
    print("=" * 60)
    
    if len(sys.argv) > 1 and sys.argv[1] == 'stop':
        stop_services()
        return
    
    # 检查Docker
    if not check_docker():
        print("请先安装Docker")
        return
    
    # 启动服务
    postgres_ok = start_postgres()
    manticore_ok = start_manticore()
    redis_ok = start_redis()
    
    # 检查服务状态
    if check_services():
        print("\n🎉 所有服务启动成功！")
        print("\n📋 服务信息:")
        print("   PostgreSQL: localhost:5432 (用户: postgres, 密码: postgres, 数据库: master_know_local)")
        print("   Manticore:  localhost:9308 (HTTP API)")
        print("   Redis:      localhost:6379")
        print("\n💡 使用 'python start_local_services.py stop' 停止所有服务")
    else:
        print("\n⚠️ 部分服务启动失败，请检查Docker日志")

if __name__ == "__main__":
    main()
