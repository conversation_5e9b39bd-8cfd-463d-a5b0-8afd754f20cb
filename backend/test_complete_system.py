#!/usr/bin/env python3
"""
Master-Know 完整系统测试

整合所有功能的端到端测试：
1. 用户注册登录
2. 文档上传和处理
3. 真实LLM对话
4. 向量化存储
5. 数据库存储
6. Manticore搜索
7. 对话摘要生成

这是最终的完整系统验证测试
"""

import os
import sys
import uuid
import asyncio
from pathlib import Path
from datetime import datetime

# 设置环境变量
os.environ["ENV_FILE"] = "../.env.local"
sys.path.insert(0, str(Path(__file__).parent))

def create_comprehensive_test_document():
    """创建综合测试文档"""
    return """
# 人工智能与机器学习完整指南

## 第一章：人工智能基础

人工智能（AI）是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。
这些任务包括学习、推理、问题解决、感知和语言理解。

### 1.1 AI的发展历程

1. **符号主义时期（1950-1980）**：基于逻辑和符号推理
2. **连接主义时期（1980-2010）**：神经网络的兴起
3. **深度学习时期（2010-至今）**：大数据和计算能力的突破

### 1.2 AI的主要分支

- **机器学习**：让计算机从数据中学习
- **深度学习**：使用多层神经网络
- **自然语言处理**：理解和生成人类语言
- **计算机视觉**：理解和分析图像
- **机器人学**：智能机器人系统

## 第二章：机器学习详解

机器学习是AI的核心技术，它使计算机能够在没有明确编程的情况下学习和改进。

### 2.1 监督学习

监督学习使用标记的训练数据来学习输入和输出之间的映射关系。
主要算法包括：
- 线性回归
- 逻辑回归
- 决策树
- 随机森林
- 支持向量机

### 2.2 无监督学习

无监督学习从未标记的数据中发现隐藏的模式和结构。
主要技术包括：
- 聚类分析
- 降维技术
- 关联规则挖掘

### 2.3 强化学习

强化学习通过与环境交互来学习最优策略。
关键概念：
- 智能体（Agent）
- 环境（Environment）
- 状态（State）
- 动作（Action）
- 奖励（Reward）

## 第三章：深度学习革命

深度学习是机器学习的一个子集，使用多层神经网络来学习数据的复杂表示。

### 3.1 神经网络基础

神经网络由相互连接的节点（神经元）组成，模拟人脑的工作方式。
基本组件：
- 输入层
- 隐藏层
- 输出层
- 激活函数
- 权重和偏置

### 3.2 卷积神经网络（CNN）

CNN特别适用于图像处理任务：
- 卷积层：特征提取
- 池化层：降维
- 全连接层：分类

### 3.3 循环神经网络（RNN）

RNN适用于序列数据处理：
- LSTM：长短期记忆网络
- GRU：门控循环单元
- Transformer：注意力机制

## 第四章：实际应用

AI和机器学习在各个领域都有广泛应用：

### 4.1 商业应用
- 推荐系统
- 欺诈检测
- 客户服务聊天机器人
- 供应链优化

### 4.2 医疗健康
- 医学影像诊断
- 药物发现
- 个性化治疗
- 健康监测

### 4.3 自动驾驶
- 环境感知
- 路径规划
- 决策制定
- 安全系统

这就是AI和机器学习的完整概览，希望对你的学习有所帮助！
"""

async def run_complete_system_test():
    """运行完整系统测试"""
    print("=" * 100)
    print("🚀 Master-Know 完整系统端到端测试")
    print("=" * 100)
    
    start_time = datetime.now()
    results = {}
    
    # 1. 数据库和基础设施测试
    print("🗄️ 第一阶段：基础设施验证...")
    from test_db_simple import test_db_connection, test_manticore_connection
    
    db_ok = test_db_connection()
    manticore_ok = test_manticore_connection()
    results['infrastructure'] = db_ok and manticore_ok
    
    if not results['infrastructure']:
        print("❌ 基础设施测试失败，无法继续")
        return False
    
    # 2. 用户旅程测试
    print("\n👤 第二阶段：用户旅程验证...")
    from test_real_user_journey import (
        test_user_registration_login,
        test_document_upload_and_processing,
        test_real_llm_conversation,
        test_real_embedding_generation,
        test_conversation_summary
    )
    
    user = await test_user_registration_login()
    results['user_auth'] = user is not None
    
    document, chunks = await test_document_upload_and_processing()
    results['document_processing'] = document is not None and chunks is not None
    
    question, answer = await test_real_llm_conversation()
    results['llm_conversation'] = question is not None and answer is not None
    
    embeddings = await test_real_embedding_generation()
    results['embedding_generation'] = embeddings is not None
    
    summary = await test_conversation_summary()
    results['conversation_summary'] = summary is not None
    
    # 3. 数据存储和搜索测试
    print("\n🔍 第三阶段：数据存储和搜索验证...")
    from test_database_and_search import (
        test_database_connection,
        test_document_storage,
        test_document_processing_and_chunks,
        test_embedding_storage,
        test_manticore_connection as test_manticore_conn,
        test_manticore_index_creation,
        test_manticore_search
    )
    
    engine = await test_database_connection()
    results['database_storage'] = engine is not None
    
    if engine:
        doc, topic = await test_document_storage(engine)
        results['document_storage'] = doc is not None
        
        if doc:
            stored_chunks = await test_document_processing_and_chunks(engine, doc)
            results['chunk_storage'] = len(stored_chunks) > 0
            
            if stored_chunks:
                stored_embeddings = await test_embedding_storage(stored_chunks)
                results['vector_storage'] = len(stored_embeddings) > 0
                
                manticore_url = await test_manticore_conn()
                if manticore_url:
                    index_ok = await test_manticore_index_creation(manticore_url, stored_chunks)
                    results['search_indexing'] = index_ok
                    
                    if index_ok:
                        search_ok = await test_manticore_search(manticore_url)
                        results['search_functionality'] = search_ok
                    else:
                        results['search_functionality'] = False
                else:
                    results['search_indexing'] = False
                    results['search_functionality'] = False
            else:
                results['vector_storage'] = False
                results['search_indexing'] = False
                results['search_functionality'] = False
        else:
            results['chunk_storage'] = False
            results['vector_storage'] = False
            results['search_indexing'] = False
            results['search_functionality'] = False
    else:
        results['document_storage'] = False
        results['chunk_storage'] = False
        results['vector_storage'] = False
        results['search_indexing'] = False
        results['search_functionality'] = False
    
    # 输出最终结果
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print("\n" + "=" * 100)
    print("📊 完整系统测试结果总结")
    print("=" * 100)
    
    test_categories = [
        ("🏗️ 基础设施", [
            ("数据库连接", "infrastructure"),
        ]),
        ("👤 用户体验", [
            ("用户认证", "user_auth"),
            ("文档处理", "document_processing"),
            ("LLM对话", "llm_conversation"),
            ("向量生成", "embedding_generation"),
            ("对话摘要", "conversation_summary"),
        ]),
        ("💾 数据存储", [
            ("数据库存储", "database_storage"),
            ("文档存储", "document_storage"),
            ("分块存储", "chunk_storage"),
            ("向量存储", "vector_storage"),
        ]),
        ("🔍 搜索功能", [
            ("搜索索引", "search_indexing"),
            ("搜索查询", "search_functionality"),
        ])
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for category_name, tests in test_categories:
        print(f"\n{category_name}")
        for test_name, key in tests:
            status = "✅ 通过" if results.get(key, False) else "❌ 失败"
            print(f"  {test_name:<15} : {status}")
            total_tests += 1
            if results.get(key, False):
                passed_tests += 1
    
    print(f"\n📈 总计: {passed_tests}/{total_tests} 测试通过")
    print(f"⏱️ 总耗时: {duration:.2f}s")
    print(f"🎯 成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有系统测试通过！Master-Know已完全就绪！")
        print("\n🚀 系统功能验证完成：")
        print("   ✅ 用户可以注册登录")
        print("   ✅ 用户可以上传文档并自动处理")
        print("   ✅ 用户可以与AI进行智能对话")
        print("   ✅ 系统可以生成学习摘要")
        print("   ✅ 所有数据正确存储到数据库")
        print("   ✅ 搜索功能完全可用")
        print("\n💡 Master-Know 已准备好为用户提供完整的AI学习导师体验！")
        return True
    else:
        print(f"\n⚠️ {total_tests - passed_tests} 个测试失败，系统需要进一步优化")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_complete_system_test())
    sys.exit(0 if success else 1)
