#!/usr/bin/env python3
"""
测试完整的上下文检索和拼接功能

验证PRD中F4.2 - 主题内记忆功能：
1. 固定人设
2. 短期记忆（最近消息）
3. 长期记忆锚点（从历史摘要中搜索）
4. 知识库（从关联文档切片中搜索）
"""

import os
import sys
import asyncio
from pathlib import Path

# 设置环境变量
os.environ["ENV_FILE"] = "../.env.local"
sys.path.insert(0, str(Path(__file__).parent))

async def test_context_retrieval():
    """测试上下文检索功能"""
    print("=" * 80)
    print("🧠 Master-Know 上下文检索功能测试")
    print("=" * 80)
    
    from cli_chat import MasterKnowCLI
    
    # 创建CLI实例（启用调试模式）
    cli = MasterKnowCLI(debug=True)
    
    print("🚀 第一步：初始化系统")
    print("-" * 60)
    
    # 创建用户会话
    user = await cli.create_user_session()
    if not user:
        print("❌ 用户会话创建失败")
        return False
    
    print("📄 第二步：上传测试文档")
    print("-" * 60)
    
    # 上传测试文档
    document = await cli.upload_document("test_document.md")
    if not document:
        print("❌ 文档上传失败")
        return False
    
    print("🔍 第三步：确保Manticore数据同步")
    print("-" * 60)
    
    # 确保Manticore中有数据
    await cli.ensure_manticore_data()
    
    print("💬 第四步：测试上下文检索对话")
    print("-" * 60)
    
    # 测试问题，应该能从文档中找到相关内容
    test_questions = [
        {
            "question": "这篇文档主要讲了什么内容？",
            "expected_context": "应该检索到文档的概述信息"
        },
        {
            "question": "Django和Flask有什么区别？",
            "expected_context": "应该检索到Django和Flask的对比内容"
        },
        {
            "question": "什么是RESTful API？",
            "expected_context": "应该检索到REST API相关内容"
        },
        {
            "question": "如何部署Python Web应用？",
            "expected_context": "应该检索到部署相关内容"
        }
    ]
    
    for i, test_case in enumerate(test_questions, 1):
        print(f"\n🧪 测试 {i}: {test_case['question']}")
        print(f"📋 预期: {test_case['expected_context']}")
        print("-" * 40)
        
        # 测试上下文搜索
        context_info = await cli.search_relevant_context(test_case['question'])
        
        print(f"🔍 上下文搜索结果:")
        print(f"   - 文档块: {len(context_info['document_chunks'])} 个")
        print(f"   - 历史摘要: {len(context_info['historical_summaries'])} 个")
        print(f"   - 最近消息: {len(context_info['recent_messages'])} 条")
        
        if context_info['document_chunks']:
            print(f"📄 相关文档块:")
            for j, chunk in enumerate(context_info['document_chunks'][:2], 1):
                print(f"   {j}. 《{chunk['document_title']}》块{chunk['chunk_index']}")
                print(f"      内容: {chunk['content'][:100]}...")
        
        # 测试动态Prompt构建
        messages = await cli.build_dynamic_prompt(test_case['question'], context_info)
        
        print(f"🧠 动态Prompt构建:")
        print(f"   - 消息数量: {len(messages)} 条")
        print(f"   - 系统提示长度: {len(messages[0]['content'])} 字符")
        
        # 实际对话测试
        print(f"💬 AI对话测试:")
        response = await cli.chat_with_ai(test_case['question'])
        
        if response:
            print(f"✅ 对话成功")
            print(f"🤖 AI回复预览: {response[:150]}...")
            
            # 检查回复是否包含文档相关内容
            has_context = any(
                chunk['document_title'].lower() in response.lower() or
                any(word in response.lower() for word in chunk['content'].lower().split()[:10])
                for chunk in context_info['document_chunks']
            )
            
            if has_context:
                print(f"✅ 回复包含文档上下文")
            else:
                print(f"⚠️ 回复可能未使用文档上下文")
        else:
            print(f"❌ 对话失败")
        
        print("-" * 40)
    
    print("\n🔍 第五步：测试搜索功能")
    print("-" * 60)
    
    # 测试搜索功能
    search_queries = ["Django", "Flask", "REST", "部署"]
    
    for query in search_queries:
        print(f"\n🔎 搜索测试: '{query}'")
        result = await cli.search_documents(query)
        print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
    
    print("\n📊 第六步：对话历史分析")
    print("-" * 60)
    
    print(f"💬 对话统计:")
    print(f"   - 总轮数: {len(cli.conversation_history)//2}")
    print(f"   - 总消息数: {len(cli.conversation_history)}")
    
    if cli.conversation_history:
        print(f"📝 最近对话:")
        for i, msg in enumerate(cli.conversation_history[-4:], 1):
            role = "👤 用户" if msg["role"] == "user" else "🤖 AI"
            content = msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
            print(f"   {i}. {role}: {content}")
    
    print("\n🎉 测试完成！")
    print("=" * 80)
    
    # 验证核心功能
    success_criteria = [
        len(context_info['document_chunks']) > 0,  # 能检索到文档内容
        len(cli.conversation_history) > 0,         # 有对话历史
        response is not None                       # AI能正常响应
    ]
    
    success_count = sum(success_criteria)
    total_criteria = len(success_criteria)
    
    print(f"✅ 功能验证: {success_count}/{total_criteria} 项通过")
    
    if success_count == total_criteria:
        print("🎉 所有核心功能正常工作！")
        print("✅ F4.1 - 数据持久化: Manticore存储正常")
        print("✅ F4.2 - 主题内记忆: 上下文检索和拼接正常")
        return True
    else:
        print("⚠️ 部分功能需要优化")
        return False

def main():
    """主函数"""
    try:
        success = asyncio.run(test_context_retrieval())
        if success:
            print("\n🚀 上下文检索功能测试通过！")
            print("💡 现在可以运行 'python cli_chat.py --debug' 体验完整功能")
        else:
            print("\n⚠️ 测试发现问题，需要进一步调试")
    except KeyboardInterrupt:
        print("\n\n👋 测试被中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
