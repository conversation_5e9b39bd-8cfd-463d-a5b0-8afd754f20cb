#!/usr/bin/env python3
"""
数据库和搜索功能测试

测试完整的数据存储和Manticore搜索功能：
1. 数据库连接和表创建
2. 文档数据存储
3. 向量数据存储
4. Manticore搜索索引
5. 混合搜索功能
"""

import os
import sys
import uuid
import asyncio
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# 设置环境变量
os.environ["ENV_FILE"] = "../.env.local"
sys.path.insert(0, str(Path(__file__).parent))

async def test_database_connection():
    """测试数据库连接"""
    print("🗄️ 测试数据库连接...")
    
    try:
        from sqlmodel import Session, create_engine, SQLModel
        from app.core.config import settings
        
        # 创建数据库引擎
        engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
        print(f"   📍 数据库URL: {settings.SQLALCHEMY_DATABASE_URI}")

        # 测试连接
        with Session(engine) as session:
            from sqlalchemy import text
            result = session.exec(text("SELECT 1 as test")).first()
            if result:
                print("   ✅ 数据库连接成功")

                # 导入所有模型以确保它们被注册到metadata中
                from app import models  # 这会导入所有模型

                # 创建所有表
                SQLModel.metadata.create_all(engine)
                print("   ✅ 数据表创建成功")

                return engine
            else:
                print("   ❌ 数据库连接失败")
                return None
                
    except Exception as e:
        print(f"   ❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_document_storage(engine):
    """测试文档存储"""
    print("\n📄 测试文档存储...")
    
    try:
        from sqlmodel import Session
        from app.models import DocumentCreate, TopicCreate
        from app.crud.document import create_document
        from app.crud.topic import create_topic
        
        with Session(engine) as session:
            # 先创建测试用户（或获取已存在的用户）
            from app.models import UserCreate
            from app.crud.user import create_user, get_user_by_email

            test_email = f"testuser_{uuid.uuid4().hex[:8]}@example.com"
            user_data = UserCreate(
                email=test_email,
                password="testpassword123",
                full_name="测试用户"
            )

            user = create_user(session=session, user_create=user_data)
            print(f"   ✅ 用户创建成功: {user.email}")

            # 创建测试主题
            topic_data = TopicCreate(
                name="Python学习主题",
                description="学习Python编程的主题"
            )

            topic = create_topic(session=session, topic_in=topic_data, owner_id=user.id)
            print(f"   ✅ 主题创建成功: {topic.name}")
            
            # 创建测试文档
            doc_content = """
            # Python异步编程指南
            
            ## 什么是异步编程
            异步编程是一种编程范式，允许程序在等待某些操作完成时继续执行其他任务。
            
            ## asyncio库
            Python的asyncio库提供了编写异步代码的工具。
            
            ## async/await语法
            使用async def定义异步函数，使用await调用异步操作。
            """
            
            doc_data = DocumentCreate(
                title="Python异步编程指南",
                content=doc_content,
                file_type="md",
                size=len(doc_content.encode('utf-8'))
            )
            
            document = create_document(session=session, document_in=doc_data, owner_id=user.id)
            print(f"   ✅ 文档存储成功: {document.title}")
            print(f"   📊 文档ID: {document.id}")
            
            return document, topic
            
    except Exception as e:
        print(f"   ❌ 文档存储测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

async def test_document_processing_and_chunks(engine, document):
    """测试文档处理和分块存储"""
    print("\n✂️ 测试文档处理和分块...")
    
    try:
        from sqlmodel import Session
        from app.services.document import DocumentService, ChunkService
        from engines.text_splitter import TextSplitterEngine
        from engines.text_splitter.strategies import TokenBasedStrategy
        from app.models import DocumentChunkCreate
        from app.crud.document import create_document_chunk
        
        with Session(engine) as session:
            # 分割文档
            text_engine = TextSplitterEngine()
            strategy = TokenBasedStrategy(max_tokens=200)
            
            from engines.text_splitter.models import Document as EngineDocument
            engine_doc = EngineDocument(
                title=document.title,
                content=document.content,
                file_type=document.file_type,
                size=document.size
            )
            
            split_result = text_engine.split_document(engine_doc, strategy)
            print(f"   ✅ 文档分割完成: {split_result.total_chunks} 个块")
            
            # 存储分块到数据库
            stored_chunks = []
            for i, chunk in enumerate(split_result.chunks):
                chunk_data = DocumentChunkCreate(
                    document_id=document.id,
                    content=chunk.content,
                    chunk_index=i,
                    token_count=chunk.token_count,
                    start_char=chunk.start_char,
                    end_char=chunk.end_char
                )
                
                db_chunk = create_document_chunk(session=session, chunk_in=chunk_data)
                stored_chunks.append(db_chunk)
                print(f"      块 {i+1}: {len(chunk.content)} 字符, {chunk.token_count} tokens")
            
            print(f"   ✅ 分块存储成功: {len(stored_chunks)} 个块")
            return stored_chunks
            
    except Exception as e:
        print(f"   ❌ 文档处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

async def test_embedding_storage(chunks):
    """测试向量存储"""
    print("\n🔢 测试向量存储...")
    
    try:
        from app.services.embedding import EmbeddingService
        
        # 创建向量化服务
        embedding_service = EmbeddingService()
        
        # 提取文本内容
        texts = [chunk.content for chunk in chunks[:3]]  # 只处理前3个块
        print(f"   📤 向量化 {len(texts)} 个文本块...")
        
        # 生成向量
        embeddings = await embedding_service.embed_texts(texts)
        
        if embeddings and len(embeddings) > 0:
            print(f"   ✅ 向量生成成功: {len(embeddings)} 个向量")
            print(f"   📊 向量维度: {len(embeddings[0])} 维")
            
            # 这里可以存储向量到数据库或向量数据库
            # 为了演示，我们只是验证向量质量
            for i, embedding in enumerate(embeddings):
                if len(embedding) == 1536:  # text-embedding-3-small的维度
                    print(f"      向量 {i+1}: 维度正确 ✅")
                else:
                    print(f"      向量 {i+1}: 维度错误 ❌")
            
            return embeddings
        else:
            print("   ❌ 向量生成失败")
            return []
            
    except Exception as e:
        print(f"   ❌ 向量存储测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

async def test_manticore_connection():
    """测试Manticore搜索连接"""
    print("\n🔍 测试Manticore搜索连接...")
    
    try:
        import httpx
        from app.core.config import settings
        
        # 构建Manticore HTTP URL
        manticore_url = f"http://{settings.MANTICORE_HOST}:{settings.MANTICORE_HTTP_PORT}"
        print(f"   📍 Manticore URL: {manticore_url}")
        
        async with httpx.AsyncClient() as client:
            # 测试连接
            response = await client.get(f"{manticore_url}/")
            
            if response.status_code == 200:
                print("   ✅ Manticore连接成功")
                return manticore_url
            else:
                print(f"   ❌ Manticore连接失败: {response.status_code}")
                return None
                
    except Exception as e:
        print(f"   ❌ Manticore连接测试失败: {e}")
        return None

async def test_manticore_index_creation(manticore_url, chunks):
    """测试Manticore索引创建"""
    print("\n📝 测试Manticore索引创建...")
    
    try:
        import httpx
        
        async with httpx.AsyncClient() as client:
            # 创建文档索引
            create_index_sql = """
            CREATE TABLE IF NOT EXISTS documents (
                id bigint,
                title text,
                content text,
                chunk_index integer,
                document_id string,
                user_id string
            ) engine='columnar'
            """
            
            response = await client.post(
                f"{manticore_url}/cli",
                data=create_index_sql,
                headers={"Content-Type": "text/plain"}
            )
            
            if response.status_code == 200:
                print("   ✅ 文档索引创建成功")
                
                # 插入测试数据
                for i, chunk in enumerate(chunks[:1]):  # 只插入第一个块避免复杂性
                    # 清理内容，移除特殊字符
                    clean_content = chunk.content.replace("'", "").replace('"', '').replace('\n', ' ').strip()
                    clean_title = "Python异步编程指南"  # 直接使用标题，避免lazy loading

                    insert_sql = f"""
                    INSERT INTO documents (id, title, content, chunk_index, document_id, user_id)
                    VALUES ({i+1}, '{clean_title}', '{clean_content}',
                            {chunk.chunk_index}, '{chunk.document_id}', 'test-user')
                    """

                    insert_response = await client.post(
                        f"{manticore_url}/cli",
                        data=insert_sql,
                        headers={"Content-Type": "text/plain"}
                    )

                    if insert_response.status_code == 200:
                        print(f"      块 {i+1}: 索引成功 ✅")
                    else:
                        print(f"      块 {i+1}: 索引失败 ❌")
                        print(f"      错误: {insert_response.text}")
                
                return True
            else:
                print(f"   ❌ 索引创建失败: {response.status_code}")
                print(f"   📄 响应: {response.text}")
                return False
                
    except Exception as e:
        print(f"   ❌ Manticore索引测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_manticore_search(manticore_url):
    """测试Manticore搜索功能"""
    print("\n🔎 测试Manticore搜索功能...")
    
    try:
        import httpx
        
        async with httpx.AsyncClient() as client:
            # 执行搜索查询
            search_queries = [
                "异步编程",
                "asyncio",
                "Python async"
            ]
            
            for query in search_queries:
                search_sql = f"""
                SELECT id, title, content, chunk_index 
                FROM documents 
                WHERE MATCH('{query}') 
                LIMIT 5
                """
                
                response = await client.post(
                    f"{manticore_url}/cli",
                    data=search_sql,
                    headers={"Content-Type": "text/plain"}
                )
                
                if response.status_code == 200:
                    try:
                        # Manticore CLI返回的是纯文本，不是JSON
                        result_text = response.text.strip()
                        if result_text and "Empty set" not in result_text:
                            print(f"   ✅ 搜索 '{query}': 找到结果")
                            print(f"      结果预览: {result_text[:100]}...")
                        else:
                            print(f"   ⚠️ 搜索 '{query}': 无结果")
                    except Exception as e:
                        print(f"   ⚠️ 搜索 '{query}': 响应解析失败 - {e}")
                else:
                    print(f"   ❌ 搜索 '{query}' 失败: {response.status_code}")
                    print(f"      错误: {response.text}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ Manticore搜索测试失败: {e}")
        return False

async def run_database_and_search_tests():
    """运行完整的数据库和搜索测试"""
    print("=" * 80)
    print("🚀 Master-Know 数据库和搜索功能测试")
    print("=" * 80)
    
    start_time = datetime.now()
    results = {}
    
    # 1. 数据库连接测试
    engine = await test_database_connection()
    results['database_connection'] = engine is not None
    
    if not engine:
        print("\n❌ 数据库连接失败，无法继续测试")
        return False
    
    # 2. 文档存储测试
    document, topic = await test_document_storage(engine)
    results['document_storage'] = document is not None
    
    if not document:
        print("\n❌ 文档存储失败，无法继续测试")
        return False
    
    # 3. 文档处理和分块测试
    chunks = await test_document_processing_and_chunks(engine, document)
    results['document_processing'] = len(chunks) > 0
    
    # 4. 向量存储测试
    embeddings = await test_embedding_storage(chunks)
    results['embedding_storage'] = len(embeddings) > 0
    
    # 5. Manticore连接测试
    manticore_url = await test_manticore_connection()
    results['manticore_connection'] = manticore_url is not None
    
    if manticore_url and chunks:
        # 6. Manticore索引测试
        index_success = await test_manticore_index_creation(manticore_url, chunks)
        results['manticore_indexing'] = index_success
        
        # 7. Manticore搜索测试
        search_success = await test_manticore_search(manticore_url)
        results['manticore_search'] = search_success
    else:
        results['manticore_indexing'] = False
        results['manticore_search'] = False
    
    # 输出结果
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print("\n" + "=" * 80)
    print("📊 数据库和搜索测试结果")
    print("=" * 80)
    
    test_names = [
        ("数据库连接", "database_connection"),
        ("文档存储", "document_storage"),
        ("文档处理", "document_processing"),
        ("向量存储", "embedding_storage"),
        ("Manticore连接", "manticore_connection"),
        ("Manticore索引", "manticore_indexing"),
        ("Manticore搜索", "manticore_search")
    ]
    
    passed = 0
    for test_name, key in test_names:
        status = "✅ 通过" if results[key] else "❌ 失败"
        print(f"{test_name:<15} : {status}")
        if results[key]:
            passed += 1
    
    print(f"\n📈 总计: {passed}/{len(test_names)} 测试通过")
    print(f"⏱️ 总耗时: {duration:.2f}s")
    
    if passed == len(test_names):
        print("\n🎉 所有数据库和搜索测试通过！完整的数据存储和搜索功能正常工作。")
        return True
    else:
        print(f"\n⚠️ {len(test_names) - passed} 个测试失败，请检查相关服务。")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_database_and_search_tests())
    sys.exit(0 if success else 1)
