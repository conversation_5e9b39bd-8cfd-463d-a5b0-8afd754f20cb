#!/usr/bin/env python3
"""
简单数据库连接测试
"""

import os
import sys
from pathlib import Path

# 设置环境变量
os.environ["ENV_FILE"] = "../.env.local"
sys.path.insert(0, str(Path(__file__).parent))

def test_db_connection():
    """测试数据库连接"""
    print("🗄️ 测试数据库连接...")
    
    try:
        from app.core.config import settings
        print(f"   📍 数据库配置:")
        print(f"      服务器: {settings.POSTGRES_SERVER}")
        print(f"      端口: {settings.POSTGRES_PORT}")
        print(f"      数据库: {settings.POSTGRES_DB}")
        print(f"      用户: {settings.POSTGRES_USER}")
        print(f"      URL: {settings.SQLALCHEMY_DATABASE_URI}")
        
        # 尝试连接
        from sqlmodel import Session, create_engine
        from sqlalchemy import text
        
        engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
        
        with Session(engine) as session:
            result = session.exec(text("SELECT 1 as test")).first()
            if result:
                print("   ✅ 数据库连接成功")
                return True
            else:
                print("   ❌ 数据库连接失败")
                return False
                
    except Exception as e:
        print(f"   ❌ 数据库连接异常: {e}")
        return False

def test_manticore_connection():
    """测试Manticore连接"""
    print("\n🔍 测试Manticore连接...")

    try:
        import httpx
        from app.core.config import settings

        manticore_url = f"http://{settings.MANTICORE_HOST}:{settings.MANTICORE_HTTP_PORT}"
        print(f"   📍 Manticore URL: {manticore_url}")

        # 使用同步客户端避免事件循环冲突
        with httpx.Client(timeout=10.0) as client:
            try:
                response = client.get(f"{manticore_url}/")
                if response.status_code == 200:
                    print("   ✅ Manticore连接成功")
                    return True
                else:
                    print(f"   ❌ Manticore连接失败: {response.status_code}")
                    return False
            except Exception as e:
                print(f"   ❌ Manticore连接异常: {e}")
                return False

    except Exception as e:
        print(f"   ❌ Manticore测试异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Master-Know 服务连接测试")
    print("=" * 60)
    
    db_ok = test_db_connection()
    manticore_ok = test_manticore_connection()
    
    print("\n" + "=" * 60)
    print("📊 连接测试结果")
    print("=" * 60)
    
    print(f"数据库连接      : {'✅ 通过' if db_ok else '❌ 失败'}")
    print(f"Manticore连接   : {'✅ 通过' if manticore_ok else '❌ 失败'}")
    
    if db_ok and manticore_ok:
        print("\n🎉 所有服务连接正常！")
        return True
    else:
        print("\n⚠️ 部分服务连接失败，请检查Docker服务状态")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
