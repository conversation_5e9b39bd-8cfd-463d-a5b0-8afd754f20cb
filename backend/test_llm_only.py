#!/usr/bin/env python3
"""
单独测试LLM API调用
"""

import os
import sys
import asyncio
from pathlib import Path

# 设置环境变量
os.environ["ENV_FILE"] = ".env.local"
sys.path.insert(0, str(Path(__file__).parent))

async def test_llm_api():
    """测试LLM API调用"""
    print("🧪 测试LLM API调用...")
    
    try:
        from app.services.llm import get_openai_provider
        from app.core.config import settings

        print(f"   🔧 配置检查:")
        print(f"      API Key: {settings.LLM_OPENAI_API_KEY[:10]}...")
        print(f"      Base URL: {settings.LLM_OPENAI_BASE_URL}")
        print(f"      Model: {settings.LLM_OPENAI_MODEL}")

        # 创建LLM服务，手动指定模型
        from app.services.llm import OpenAIProvider
        llm_provider = OpenAIProvider(
            api_key=settings.LLM_OPENAI_API_KEY,
            base_url=settings.LLM_OPENAI_BASE_URL,
            model="gpt-4o-mini"  # 强制使用正确的模型
        )
        print("   ✅ LLM服务初始化成功")
        
        # 简单的测试消息
        messages = [
            {"role": "user", "content": "请简单回答：1+1等于几？"}
        ]
        
        print("   📤 发送简单问题...")
        
        # 调用API
        response = await llm_provider.generate(
            messages=messages,
            max_tokens=50,
            temperature=0.1
        )
        
        print(f"   🔍 完整响应: {response}")
        
        if response.get("text"):
            print(f"   ✅ 成功获得回复: {response['text']}")
        else:
            print(f"   ❌ 回复为空，原始响应: {response.get('raw_response', 'N/A')}")
            
    except Exception as e:
        print(f"   ❌ LLM测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_llm_api())
