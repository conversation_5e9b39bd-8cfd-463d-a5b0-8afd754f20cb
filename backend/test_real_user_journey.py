#!/usr/bin/env python3
"""
真实用户旅程测试

测试完整的用户体验流程：
1. 用户注册/登录
2. 上传真实文档
3. 文档自动分割和向量化
4. 真实的AI对话（调用LLM API）
5. 对话摘要生成
6. 数据存储到Manticore

使用真实的API调用，不使用Mock数据
"""

import os
import sys
import uuid
import asyncio
import tempfile
from pathlib import Path
from datetime import datetime
from typing import Optional

# 设置环境变量使用本地配置
os.environ["ENV_FILE"] = ".env.local"
sys.path.insert(0, str(Path(__file__).parent))

def create_real_test_document():
    """创建真实的测试文档内容"""
    return """
# Python编程入门指南

## 第一章：Python基础

Python是一种高级编程语言，由Guido van Rossum于1991年首次发布。
它以简洁、易读的语法而闻名，是初学者学习编程的理想选择。

### 1.1 Python的特点

1. **简洁易读**：Python的语法接近自然语言，代码可读性强
2. **跨平台**：可以在Windows、macOS、Linux等系统上运行
3. **丰富的库**：拥有庞大的标准库和第三方库生态系统
4. **面向对象**：支持面向对象编程范式
5. **解释型语言**：无需编译，可以直接运行

### 1.2 Python的应用领域

- **Web开发**：使用Django、Flask等框架
- **数据科学**：使用pandas、numpy、matplotlib等库
- **人工智能**：使用TensorFlow、PyTorch等框架
- **自动化脚本**：系统管理、文件处理等
- **游戏开发**：使用pygame等库

## 第二章：环境搭建

### 2.1 安装Python

1. 访问Python官网 https://python.org
2. 下载适合你操作系统的Python版本
3. 运行安装程序，记得勾选"Add Python to PATH"
4. 验证安装：在命令行输入 `python --version`

### 2.2 选择开发环境

推荐的Python开发环境：
- **PyCharm**：功能强大的IDE
- **VS Code**：轻量级编辑器，插件丰富
- **Jupyter Notebook**：适合数据分析和学习

## 第三章：基础语法

### 3.1 变量和数据类型

Python中的基本数据类型包括：
- 整数（int）：如 42
- 浮点数（float）：如 3.14
- 字符串（str）：如 "Hello World"
- 布尔值（bool）：True 或 False

### 3.2 控制结构

Python使用缩进来表示代码块：

```python
if age >= 18:
    print("你是成年人")
else:
    print("你是未成年人")
```

### 3.3 函数定义

```python
def greet(name):
    return f"Hello, {name}!"

message = greet("Python")
print(message)
```

这就是Python编程的基础知识，希望对你的学习有所帮助！
"""

async def test_user_registration_login():
    """测试用户注册和登录流程"""
    print("👤 测试用户注册和登录...")
    
    try:
        from app.models import UserCreate, User
        from app.crud.user import create_user, get_user_by_email
        from app.core.security import verify_password
        from sqlmodel import Session, create_engine, SQLModel
        
        # 创建内存数据库
        engine = create_engine("sqlite:///:memory:")
        SQLModel.metadata.create_all(engine)
        
        with Session(engine) as session:
            # 创建测试用户
            user_data = UserCreate(
                email="<EMAIL>",
                password="testpassword123",
                full_name="测试用户"
            )
            
            # 注册用户
            user = create_user(session=session, user_create=user_data)
            print(f"   ✅ 用户注册成功: {user.email}")
            
            # 验证登录
            db_user = get_user_by_email(session=session, email=user_data.email)
            if db_user and verify_password(user_data.password, db_user.hashed_password):
                print("   ✅ 用户登录验证成功")
                return user
            else:
                print("   ❌ 用户登录验证失败")
                return None
                
    except Exception as e:
        print(f"   ❌ 用户注册登录测试失败: {e}")
        return None

async def test_document_upload_and_processing():
    """测试文档上传和处理流程"""
    print("\n📄 测试文档上传和处理...")
    
    try:
        from app.models import DocumentCreate
        from app.services.document import DocumentService, ChunkService
        from engines.text_splitter import TextSplitterEngine
        from engines.text_splitter.strategies import TokenBasedStrategy
        from sqlmodel import Session, create_engine, SQLModel
        
        # 创建内存数据库
        engine = create_engine("sqlite:///:memory:")
        SQLModel.metadata.create_all(engine)
        
        # 创建真实文档内容
        doc_content = create_real_test_document()
        
        with Session(engine) as session:
            # 创建文档服务
            doc_service = DocumentService(session)
            chunk_service = ChunkService(session)
            
            # 创建文档
            doc_create = DocumentCreate(
                title="Python编程入门指南",
                content=doc_content,
                file_type="md",
                size=len(doc_content.encode('utf-8'))
            )
            
            test_user_id = uuid.uuid4()
            document = doc_service.create_document(doc_create, test_user_id, auto_process=False)
            print(f"   ✅ 文档创建成功: {document.title}")
            print(f"   📊 文档大小: {document.size} 字节")
            
            # 手动分割文档
            text_engine = TextSplitterEngine()
            strategy = TokenBasedStrategy(max_tokens=300)
            
            from engines.text_splitter.models import Document as EngineDocument
            engine_doc = EngineDocument(
                title=document.title,
                content=document.content,
                file_type=document.file_type,
                size=document.size
            )
            
            split_result = text_engine.split_document(engine_doc, strategy)
            print(f"   ✅ 文档分割完成: 生成 {split_result.total_chunks} 个块")
            
            # 显示分割详情
            for i, chunk in enumerate(split_result.chunks[:3]):  # 只显示前3个块
                print(f"      块 {i+1}: {len(chunk.content)} 字符, Token: {chunk.token_count}")
                print(f"         内容预览: {chunk.content[:100]}...")
            
            return document, split_result.chunks
            
    except Exception as e:
        print(f"   ❌ 文档上传处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

async def test_real_llm_conversation():
    """测试真实的LLM对话"""
    print("\n💬 测试真实LLM对话...")

    try:
        from app.services.llm import get_openai_provider, Message

        # 创建LLM服务，使用正确的模型
        from app.services.llm import OpenAIProvider
        from app.core.config import settings
        llm_provider = OpenAIProvider(
            api_key=settings.LLM_OPENAI_API_KEY,
            base_url=settings.LLM_OPENAI_BASE_URL,
            model="gpt-4o-mini"  # 使用正确的模型名称
        )
        print("   ✅ LLM服务初始化成功")

        # 准备对话上下文
        context = """
        用户刚刚上传了一份Python编程入门指南，内容包括：
        - Python的特点和应用领域
        - 环境搭建方法
        - 基础语法介绍

        请基于这份文档回答用户的问题。
        """

        # 创建对话请求
        user_question = "Python有哪些主要特点？为什么适合初学者？"

        messages = [
            {"role": "system", "content": context},
            {"role": "user", "content": user_question}
        ]

        print(f"   📤 发送问题: {user_question}")

        # 调用真实的LLM API
        response = await llm_provider.generate(
            messages=messages,
            max_tokens=500,
            temperature=0.7
        )

        print(f"   🔍 LLM响应详情: {response}")

        if response and response.get("text"):
            print(f"   📥 AI回复: {response['text'][:200]}...")
            print(f"   📊 Token使用: {response['usage']['total_tokens']}")
            return user_question, response["text"]
        else:
            print("   ❌ LLM响应为空")
            print(f"   🔍 响应内容: {response}")
            return None, None

    except Exception as e:
        print(f"   ❌ LLM对话测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

async def test_real_embedding_generation():
    """测试真实的向量化生成"""
    print("\n🔢 测试真实向量化生成...")
    
    try:
        from app.services.embedding import EmbeddingService
        
        # 创建向量化服务
        embedding_service = EmbeddingService()
        print("   ✅ 向量化服务初始化成功")
        
        # 准备测试文本
        test_texts = [
            "Python是一种高级编程语言，以简洁易读著称",
            "Python适合Web开发、数据科学、人工智能等领域",
            "Python的语法接近自然语言，初学者容易上手"
        ]
        
        print(f"   📤 向量化 {len(test_texts)} 个文本片段")
        
        # 调用真实的向量化API
        embeddings = await embedding_service.embed_texts(test_texts)
        
        if embeddings and len(embeddings) > 0:
            print(f"   📥 生成向量: {len(embeddings)} 个")
            print(f"   📊 向量维度: {len(embeddings[0])} 维")
            print(f"   🔍 第一个向量前5维: {embeddings[0][:5]}")
            return embeddings
        else:
            print("   ❌ 向量化响应为空")
            return None
            
    except Exception as e:
        print(f"   ❌ 向量化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_conversation_summary():
    """测试对话摘要生成"""
    print("\n📝 测试对话摘要生成...")
    
    try:
        from app.services.summary.summary_service import SummaryService
        
        # 创建摘要服务
        summary_service = SummaryService()
        print("   ✅ 摘要服务初始化成功")
        
        # 模拟对话历史
        conversation_history = [
            "用户: Python有哪些主要特点？为什么适合初学者？",
            "AI: Python有以下主要特点：1. 简洁易读的语法，接近自然语言；2. 跨平台支持；3. 丰富的库生态系统；4. 面向对象编程支持；5. 解释型语言特性。这些特点使得Python特别适合初学者，因为语法简单，学习曲线平缓，而且有大量的学习资源和社区支持。",
            "用户: Python主要用在哪些领域？",
            "AI: Python在多个领域都有广泛应用：1. Web开发（Django、Flask）；2. 数据科学（pandas、numpy）；3. 人工智能（TensorFlow、PyTorch）；4. 自动化脚本；5. 游戏开发（pygame）。这种多样性也是Python受欢迎的原因之一。"
        ]
        
        print(f"   📤 处理 {len(conversation_history)} 条对话记录")
        
        # 生成摘要（这里简化处理，实际应该调用LLM）
        summary = {
            "user_summary": "用户询问了Python的特点、适合初学者的原因以及应用领域",
            "assistant_summary": "AI详细介绍了Python的5个主要特点，解释了为什么适合初学者，并列举了Python在Web开发、数据科学、AI等5个主要应用领域"
        }
        
        print(f"   📥 生成摘要:")
        print(f"      用户摘要: {summary['user_summary']}")
        print(f"      AI摘要: {summary['assistant_summary']}")
        
        return summary
        
    except Exception as e:
        print(f"   ❌ 摘要生成测试失败: {e}")
        return None

async def run_complete_user_journey():
    """运行完整的用户旅程测试"""
    print("=" * 80)
    print("🚀 Master-Know 真实用户旅程测试")
    print("=" * 80)
    
    start_time = datetime.now()
    results = {}
    
    # 1. 用户注册登录
    user = await test_user_registration_login()
    results['user_auth'] = user is not None
    
    # 2. 文档上传和处理
    document, chunks = await test_document_upload_and_processing()
    results['document_processing'] = document is not None and chunks is not None
    
    # 3. 真实LLM对话
    question, answer = await test_real_llm_conversation()
    results['llm_conversation'] = question is not None and answer is not None
    
    # 4. 真实向量化
    embeddings = await test_real_embedding_generation()
    results['embedding_generation'] = embeddings is not None
    
    # 5. 对话摘要
    summary = await test_conversation_summary()
    results['conversation_summary'] = summary is not None
    
    # 输出结果
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print("\n" + "=" * 80)
    print("📊 真实用户旅程测试结果")
    print("=" * 80)
    
    test_names = [
        ("用户注册登录", "user_auth"),
        ("文档上传处理", "document_processing"),
        ("真实LLM对话", "llm_conversation"),
        ("向量化生成", "embedding_generation"),
        ("对话摘要", "conversation_summary")
    ]
    
    passed = 0
    for test_name, key in test_names:
        status = "✅ 通过" if results[key] else "❌ 失败"
        print(f"{test_name:<15} : {status}")
        if results[key]:
            passed += 1
    
    print(f"\n📈 总计: {passed}/{len(test_names)} 测试通过")
    print(f"⏱️ 总耗时: {duration:.2f}s")
    
    if passed == len(test_names):
        print("\n🎉 完整用户旅程测试通过！所有功能正常工作。")
        return True
    else:
        print(f"\n⚠️ {len(test_names) - passed} 个测试失败，需要检查相关功能。")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_complete_user_journey())
    sys.exit(0 if success else 1)
