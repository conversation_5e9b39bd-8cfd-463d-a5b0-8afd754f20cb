# =============================================================================
# 基础设施服务 Docker Compose 配置
# 阶段1：仅包含数据库和缓存服务
# =============================================================================

services:
  # PostgreSQL 数据库
  db:
    image: postgres:12
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    volumes:
      - app-db-data:/var/lib/postgresql/data/pgdata
    env_file:
      - .env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_DB=${POSTGRES_DB?Variable not set}
    ports:
      - "5432:5432"
    networks:
      - infrastructure

  # Redis 缓存
  redis:
    image: redis:7-alpine
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - infrastructure
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Manticore Search
  manticore:
    image: manticoresearch/manticore:latest
    restart: always
    environment:
      - EXTRA=1
    volumes:
      - manticore-data:/var/lib/manticore
      - ./manticore/manticore.conf:/etc/manticoresearch/manticore.conf
    ports:
      - "9306:9306"  # MySQL protocol
      - "9308:9308"  # HTTP API
    networks:
      - infrastructure
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9308/"]
      interval: 15s
      timeout: 10s
      retries: 5

  # Adminer 数据库管理工具
  adminer:
    image: adminer
    restart: always
    depends_on:
      - db
    environment:
      - ADMINER_DESIGN=pepa-linha-dark
    ports:
      - "8080:8080"
    networks:
      - infrastructure

volumes:
  app-db-data:
  redis-data:
  manticore-data:

networks:
  infrastructure:
    driver: bridge
