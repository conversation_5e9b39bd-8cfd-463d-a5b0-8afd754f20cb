**说明**: 本项目简介聚焦产品愿景与用户价值。技术实现与模块化架构的详细说明请参见最新模块化重构方案：[`docs/2_Architecture/03_modular_refactor_plan.md`](../2_Architecture/03_modular_refactor_plan.md:1)。实现上，核心引擎已放置于 `engines/text_splitter/`，后端模型与 CRUD 已模块化至 `backend/app/models/` 与 `backend/app/crud/`，并通过 `__init__.py` 保持原有 import 路径的向后兼容。
  
### 项目简介：知深学习导师 (Phoenix 项目)
**1. 目标愿景**

打造一款个人化 AI 学习伴行系统，核心目标不是“给答案”，而是通过可追溯、可复盘的引导式对话，帮助用户“真正内化”知识，将碎片化的学习沉淀为结构化的个人知识库。

**2. 核心解决方案**

我们提供一个“**在对话中无感学习，让智慧自动沉淀**”的全新体验，它建立在三大支柱之上：

- **核心体验：无负担的引导式对话**
  - 用户可以围绕任何**主题**（无论是否基于外部文档）与 AI 导师开启一场轻松的对话。AI 会通过提问、类比和启发，引导用户主动思考并用自己的话讲清楚，从而实现深度理解。
- **核心基础：持久化的统一知识库** 
  - 系统会永久保存所有对话内容和用户上传的文档资料。用户可以随时回来，无缝衔接上一次的对话，或直接指定文档的特定章节开始学习，无需重复上传，实现真正的“一次投入，长期受益”。
- **核心成果：可交互的动态摘要**
  - 在对话的同时，系统将自动生成一份可交互的学习笔记。系统将自动生成一份保留了对话感的精炼笔记。它不是生硬的结论总结，而是将用户与AI的每一轮核心思想，提炼成如“用户：...”、“AI：...”这样的流式摘要。这份“剧本式”的笔记，让用户在回顾时能瞬间沉浸到当时的思考语境中，轻松重温学习的完整脉络。这份笔记将每一段对话与摘要要点建立映射，用户可以随时点击笔记回溯到当时的对话情境，彻底解决AI对话“聊后即忘”的痛点 。

**3. 关键技术支撑**

本项目的核心“记忆与联想”能力由 **Manticore Search** 强力驱动。我们利用 Manticore 同时支持高速全文搜索和现代向量搜索的混合能力，来构建一个能打破上下文窗口限制的“**智能上下文引擎**”，为用户提供具备长期、精准记忆的对话体验。

**4. 演进路径**

- **MVP (V1.1):** 聚焦于验证“主题内”的长期记忆和可回溯对话的核心价值 \[4\]。用户可以在一个选定的主题或文档内，体验到无缝续聊和点击摘要复盘的功能。
- **Post-MVP (V2.0):** 扩展为“全局上下文感知”，实现跨主题的智能关联。让 AI 能够主动理解用户的新问题与历史上所有对话的相关性，提供更高维度的洞见。