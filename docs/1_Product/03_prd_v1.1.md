# 产品需求文档 (Product Requirements Document - v1.1)

## 1. 项目概述

本文档旨在明确“知深学习导师”项目在 v1.1 阶段的核心目标、功能范围和成功标准。

v1.1 的核心目标是：**通过引入“智能体层 (Agent Layer)”，将 v1.0 验证通过的核心体验（长期记忆、可回溯对话）进行架构升级，使其更健壮、更智能、更易于扩展，为未来实现更高级的AI能力（如跨主题关联、主动洞察）奠定坚实基础。**

## 2. 核心用户故事 (无变化)

“作为一名需要持续学习新领域的知识工作者，**我希望** 能将一份或多份复杂的资料‘喂’给一个AI导师，然后通过轻松、无压力的对话来逐步消化它。**我希望能** 随时中断和继续学习，而系统能记住我们聊到哪里了。**当我完成后**，系统应该已经自动为我整理好了一份可随时回顾、并能链接回原始对话的精炼笔记，这样我就真正地把知识‘装进’了脑子里，而不只是‘收藏’了一个文档。”

## 3. 功能需求 (v1.1 升级点)

v1.1 的功能需求在 v1.0 的基础上，重点在于**通过智能体对后端实现进行重构和升级**，前端用户体验无显著变化。

- **F1 - 主题式会话管理 (无变化)**
  - 保持 v1.0 的所有功能。

- **F2 - 双栏专注学习界面 (无变化)**
  - 保持 v1.0 的所有功能。

- **F3 - 核心交互循环 (后端实现升级)**
  - **F3.1 (升级)**: 当一个“逻辑回合”结束后，后端 **`AgentCoordinator`** 将对话内容**并行**分发给 `EpisodicAgent` 和 `SemanticAgent`。
  - **F3.2 (升级)**:
    - **`EpisodicAgent`** 负责将原始对话回合存入数据库，实现**情景记忆**。
    - **`SemanticAgent`** 负责调用LLM提炼摘要，生成结构化的摘要对象，实现**语义记忆**。
  - **F3.3 (无变化)**: 前端收到 `SemanticAgent` 生成的摘要后，在右侧摘要区域渲染。
  - **F3.4 (无变化)**: 点击摘要能平滑滚动到 `EpisodicAgent` 记录的对应原始对话处。

- **F4 - 智能体驱动的上下文引擎 (架构升级)**
  - **F4.1 (升级)**: **数据持久化**由各个专职智能体负责。
    - `DocumentAgent` 负责文档及切片的**完整生命周期（包括未来的查询、更新、删除）**。
    - `EpisodicAgent` 负责对话记录的生命周期。
    - `SemanticAgent` 负责摘要的生命周期。
  - **F4.2 (升级)**: **主题内记忆**的构建由 `AgentCoordinator` 统一协调。
    - 在用户提问时，`AgentCoordinator` 向 `EpisodicAgent` 请求短期记忆（最近对话），向 `SemanticAgent` 请求长期记忆锚点（相关摘要），向 `DocumentAgent` 请求知识库（相关文档切片），然后动态构建最终的 Prompt。

- **F5 - 文档处理工作流 (新增)**
  - **F5.1**: 引入 `DocumentAgent`，负责封装文档上传、分割、向量化的完整流程。
  - **F5.2**: 引入 `ProcessingBuffer` 机制，支持文档的异步、批量处理，提升系统响应速度和吞吐量。

## 4. v1.1 范围之外 (Out of Scope)

- 与 v1.0 保持一致。
- **主动的用户画像构建 (`UserPersonaAgent`)** 和 **系统自我反思与进化机制 (`ReflexionAgent`)** 等更高级的智能体，将作为 v1.2 及未来版本的重要探索方向。