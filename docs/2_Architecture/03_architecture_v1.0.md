# 知深学习导师 - 模块化架构重构方案

## 📋 项目概述

**项目名称**: 知深学习导师 (Master-Know)  
**核心目标**: 个人化AI学习伴行系统，通过可追溯、可复盘的引导式对话，帮助用户真正内化知识  
**技术特色**: 基于 Manticore Search 的智能上下文引擎 + 语义化文档分割

## 🏗️ 模块化架构设计

### 核心原则
1. **保持现有架构不变**: 完全保持 backend/app 目录结构和配置
2. **内部模块化重构**: 在现有结构内进行模块化组织
3. **向后兼容性 100%**: 不改变任何 import 路径和 API 接口
4. **渐进式实施**: 基于现有 FastAPI 模板逐步模块化

### 实施策略
- **保持 Docker Compose 不变**: 不修改部署配置和容器设置
- **复用现有基础设施**: 认证、数据库、任务系统、配置管理全部复用
- **遵循 FastAPI 最佳实践**: 依赖注入、路由模式、错误处理完全一致
- **engines/text_splitter 无缝集成**: 通过相对路径导入，不破坏现有结构

### 架构分层
```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 Frontend Layer                    │
│  ┌─────────────┐ ┌─────────────┐                           │
│  │   Web UI    │ │  WebSocket  │                           │
│  │   (React)   │ │   Client    │                           │
│  └─────────────┘ └─────────────┘                           │
├─────────────────────────────────────────────────────────────┤
│                  API网关层 API Gateway Layer                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │     BFF Gateway (Traefik + FastAPI)                    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                  应用服务层 Application Services             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   User      │ │   Topic     │ │ Conversation│          │
│  │  Service    │ │  Service    │ │  Service    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Document   │ │   Summary   │ │     LLM     │          │
│  │  Service    │ │  Service    │ │ Integration │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                  核心引擎层 Core Engine Layer                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Text-Splitter│ │ Embedding   │ │ Context     │          │
│  │   Engine    │ │  Service    │ │  Engine     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                  数据持久层 Data Persistence Layer          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Manticore   │ │ PostgreSQL  │ │   Redis     │          │
│  │   Search    │ │  (关系数据)  │ │  (缓存)     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 📁 实际实施架构

### Backend 内部模块化结构
```
backend/app/                       # 保持现有 FastAPI 模板结构
├── api/
│   ├── deps.py                    # 扩展依赖注入
│   ├── main.py                    # 注册新路由
│   └── routes/
│       ├── users.py               # 现有用户路由
│       ├── items.py               # 现有项目路由
│       ├── documents.py           # 新增：文档处理路由
│       ├── topics.py              # 新增：主题管理路由
│       ├── conversations.py       # 新增：对话管理路由
│       └── summaries.py           # 新增：摘要管理路由
├── models/                        # 模块化数据模型
│   ├── __init__.py                # 导出所有模型，保持兼容性
│   ├── base.py                    # 基础模型类
│   ├── user.py                    # 用户模型（从 models.py 迁移）
│   ├── item.py                    # 项目模型（从 models.py 迁移）
│   ├── document.py                # 文档相关模型
│   ├── topic.py                   # 主题相关模型
│   └── conversation.py            # 对话相关模型
├── crud/                          # 模块化 CRUD 操作
│   ├── __init__.py                # 导出所有 CRUD 函数，保持兼容性
│   ├── base.py                    # 基础 CRUD 操作
│   ├── user.py                    # 用户 CRUD（从 crud.py 迁移）
│   ├── item.py                    # 项目 CRUD（从 crud.py 迁移）
│   ├── document.py                # 文档 CRUD
│   ├── topic.py                   # 主题 CRUD
│   └── conversation.py            # 对话 CRUD
├── services/                      # 业务逻辑服务
│   ├── __init__.py                # 导出所有服务
│   ├── document/
│   │   ├── __init__.py
│   │   ├── document_service.py    # 文档管理服务
│   │   ├── chunk_service.py       # 文档分块服务
│   │   └── processing_service.py  # 文档处理服务
│   ├── topic/
│   ├── conversation/
│   └── summary/
├── tasks.py                       # 扩展异步任务
├── models.py                      # 保留，通过 models/__init__.py 重新导出
└── crud.py                        # 保留，通过 crud/__init__.py 重新导出
```

### 外部引擎集成
```
engines/text_splitter/             # 已实现的文本分割引擎
├── engine.py                      # 核心引擎
├── strategies.py                  # 分割策略
└── models.py                      # 数据模型
```

### 项目整体结构（保持不变）
```
master-know/
├── backend/                       # FastAPI 后端（内部模块化）
├── frontend/                      # React 前端
├── services/                      # 预留的独立服务目录
├── engines/                       # 核心引擎（已实现 text_splitter）
├── shared/                        # 共享组件
├── docker-compose.yml             # 保持不变
└── docs/                          # 项目文档
```

## 🎯 模块详细设计

### 1. Text-Splitter Engine (核心引擎)
**位置**: `engines/text_splitter/`
**职责**: 语义化文档分割
**技术栈**: semantic-text-splitter (Rust + Python bindings)

**核心功能**:
- 支持多种文档格式 (Text, Markdown, Code)
- 语义感知的智能分割
- 多种分割策略 (Token-based, Character-based, Range-based)
- 批量处理能力

**API接口**:
```python
class TextSplitterEngine:
    def split_text(self, text: str, strategy: SplitStrategy) -> List[TextChunk]
    def split_markdown(self, content: str, max_tokens: int) -> List[TextChunk]
    def split_code(self, code: str, language: str) -> List[TextChunk]
    def batch_split(self, documents: List[Document]) -> List[List[TextChunk]]
```

### 2. Document Service (应用服务)
**位置**: `services/document/`
**职责**: 文档管理和处理
**依赖**: Text-Splitter Engine, Embedding Service

**核心功能**:
- 文档上传和存储
- 调用 Text-Splitter Engine 进行分割
- 文档元数据管理
- 与主题服务集成

### 3. Embedding Service (核心引擎)
**位置**: `services/embedding/`
**职责**: 文本向量化
**技术栈**: HuggingFace Transformers / OpenAI API

**核心功能**:
- 文本向量化
- 批量处理
- 多模型支持
- 向量缓存

### 4. Context Engine (核心引擎)
**位置**: `services/context/`
**职责**: 智能上下文检索
**依赖**: Manticore Search

**核心功能**:
- 混合搜索 (全文 + 向量)
- 上下文排序和过滤
- 历史对话检索
- 主题相关性计算

## 🚀 实施计划

### Phase 1: 核心引擎重构 (Week 1)
1. **Text-Splitter Engine 独立化**
   - 从现有 document_parser.py 重构
   - 增强语义分割能力
   - 添加批量处理支持

2. **Embedding Service 创建**
   - 独立的向量化服务
   - 支持多种模型
   - 与 Manticore 集成

3. **Context Engine 优化**
   - 基于现有 Manticore 配置
   - 增强检索算法
   - 添加缓存机制

### Phase 2: 应用服务重构 (Week 2)
4. **Document Service 重构**
   - 集成新的 Text-Splitter Engine
   - 异步处理流水线
   - 文档状态管理

5. **Topic Service 完善**
   - 主题管理功能
   - 文档关联
   - 权限控制

6. **User Service 简化**
   - 基础用户管理
   - 会话管理
   - 权限验证

### Phase 3: 高级功能 (Week 3-4)
7. **LLM Integration 优化**
8. **Conversation Service 重构**
9. **Summary Service 增强**
10. **Frontend 适配**

## 📊 技术决策

### Text-Splitter 集成策略
1. **保持现有集成**: 继续使用 semantic-text-splitter
2. **增强功能**: 添加更多分割策略和优化
3. **性能优化**: 批量处理和缓存机制
4. **监控**: 添加性能指标和错误追踪

### 服务通信
- **同步**: HTTP/REST API
- **异步**: Redis Pub/Sub + Dramatiq
- **实时**: WebSocket

### 数据一致性
- **最终一致性**: 异步处理场景
- **强一致性**: 关键业务操作
- **事务管理**: 跨服务事务处理

## 🔧 开发工具和规范

### 代码规范
- Python: PEP 8 + Type Hints
- API: OpenAPI 3.0 规范
- 测试: pytest + 覆盖率 > 80%
- 文档: 自动生成 + 手动维护

### 部署策略
- 容器化: Docker + Docker Compose
- 服务发现: Traefik
- 监控: Prometheus + Grafana
- 日志: 结构化日志 + ELK Stack

## 🛠️ 具体实施步骤

### Step 1: Text-Splitter Engine 重构
```bash
# 创建独立的文本分割引擎
mkdir -p engines/text_splitter
cd engines/text_splitter

# 核心文件结构
├── __init__.py
├── engine.py              # 主引擎类
├── strategies.py          # 分割策略
├── models.py             # 数据模型
├── config.py             # 配置管理
└── tests/                # 测试文件
```

**核心代码框架**:
```python
# engines/text_splitter/engine.py
from semantic_text_splitter import TextSplitter, MarkdownSplitter
from typing import List, Dict, Any
from .strategies import SplitStrategy
from .models import TextChunk, Document

class TextSplitterEngine:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self._splitters = {}

    def get_splitter(self, strategy: SplitStrategy) -> TextSplitter:
        """获取或创建分割器实例"""
        pass

    def split_text(self, text: str, strategy: SplitStrategy) -> List[TextChunk]:
        """文本分割主方法"""
        pass

    def batch_split(self, documents: List[Document]) -> Dict[str, List[TextChunk]]:
        """批量处理文档"""
        pass
```

### Step 2: 服务模块创建脚本
```bash
# 创建服务生成脚本
cat > scripts/create_service.py << 'EOF'
#!/usr/bin/env python3
"""
服务模块创建脚本
用法: python scripts/create_service.py <service_name>
"""
import os
import sys
from pathlib import Path

def create_service(service_name: str):
    service_dir = Path(f"services/{service_name}")
    service_dir.mkdir(parents=True, exist_ok=True)

    # 创建基础文件结构
    files = {
        "__init__.py": "",
        "main.py": f"# {service_name.title()} Service Main",
        "api.py": f"# {service_name.title()} Service API",
        "models.py": f"# {service_name.title()} Service Models",
        "config.py": f"# {service_name.title()} Service Config",
        "Dockerfile": f"# {service_name.title()} Service Dockerfile",
        "requirements.txt": "# Service specific requirements",
        "tests/__init__.py": "",
        "tests/test_api.py": f"# {service_name.title()} API Tests"
    }

    for file_path, content in files.items():
        full_path = service_dir / file_path
        full_path.parent.mkdir(exist_ok=True)
        full_path.write_text(content)

    print(f"✅ Service '{service_name}' created at {service_dir}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python scripts/create_service.py <service_name>")
        sys.exit(1)
    create_service(sys.argv[1])
EOF
```

### Step 3: 共享组件设计
```python
# shared/models/base.py
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

class BaseEntity(BaseModel):
    id: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

class TextChunk(BaseEntity):
    content: str
    chunk_index: int
    start_char: int
    end_char: int
    token_count: Optional[int] = None
    embedding: Optional[List[float]] = None

class Document(BaseEntity):
    title: str
    content: str
    file_type: str
    size: int
    chunks: Optional[List[TextChunk]] = None
```

## 🧪 测试策略

### 单元测试
```python
# engines/text_splitter/tests/test_engine.py
import pytest
from engines.text_splitter import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy

class TestTextSplitterEngine:
    def test_split_text_basic(self):
        engine = TextSplitterEngine({})
        strategy = TokenBasedStrategy(max_tokens=100)

        text = "这是一个测试文档。" * 50
        chunks = engine.split_text(text, strategy)

        assert len(chunks) > 1
        assert all(chunk.token_count <= 100 for chunk in chunks)

    def test_batch_processing(self):
        # 批量处理测试
        pass
```

### 集成测试
```python
# tests/integration/test_document_processing.py
import pytest
from services.document.api import DocumentService
from engines.text_splitter import TextSplitterEngine

class TestDocumentProcessingFlow:
    def test_full_document_processing_pipeline(self):
        # 测试完整的文档处理流程
        pass
```

## � 实际实施计划

### Phase 1: 基础架构重构（1-2天）
1. **重构数据模型为模块化结构**
   - 将 `models.py` 拆分为 `models/` 目录
   - 保持所有现有 import 路径的兼容性
   - 添加文档、主题、对话相关模型

2. **重构 CRUD 操作为模块化结构**
   - 将 `crud.py` 拆分为 `crud/` 目录
   - 保持现有函数签名和调用方式
   - 添加新的 CRUD 操作

### Phase 2: 核心服务实现（2-3天）
3. **创建文档处理服务模块**
   - 在 `services/document/` 实现完整服务
   - 集成 `engines/text_splitter` 引擎
   - 与现有数据库和任务系统集成

4. **扩展依赖注入和 API 路由**
   - 在 `api/deps.py` 添加服务依赖注入
   - 创建新的 API 路由，遵循现有模式
   - 确保认证和权限检查一致

### Phase 3: 异步处理和测试（2-3天）
5. **扩展 Dramatiq 任务系统**
   - 复用现有 Dramatiq 配置
   - 添加文档处理相关异步任务
   - 确保任务的可靠执行和错误处理

6. **创建数据库迁移和测试**
   - 创建 Alembic 数据库迁移
   - 编写完整的测试套件
   - 确保向后兼容性

### 关键成功因素
- ✅ **零停机迁移**: 所有现有功能继续正常工作
- ✅ **配置不变**: Docker、部署、环境配置完全不变
- ✅ **API 兼容**: 现有 API 接口和响应格式保持一致
- ✅ **性能保持**: 不降低系统性能和响应速度

## 📈 监控和验证

### 迁移验证清单
- [ ] 所有现有 API 端点正常响应
- [ ] 数据库操作无错误
- [ ] 异步任务正常执行
- [ ] 测试覆盖率达到 80% 以上
- [ ] 性能指标无显著下降
- [ ] Docker 容器正常启动和运行

### 健康检查
```python
# shared/health/checker.py
from typing import Dict, Any
import asyncio

class HealthChecker:
    async def check_text_splitter_engine(self) -> Dict[str, Any]:
        try:
            # 测试 text-splitter 引擎
            return {"status": "healthy", "latency_ms": 10}
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    async def check_all_services(self) -> Dict[str, Any]:
        checks = await asyncio.gather(
            self.check_text_splitter_engine(),
            # 其他服务检查...
        )
        return {"services": checks}
```

## 🔄 迁移策略

### 从现有代码迁移
1. **保持向后兼容**: 现有 API 继续工作
2. **渐进式替换**: 逐步替换内部实现
3. **功能标志**: 使用特性开关控制新旧功能
4. **数据迁移**: 平滑迁移现有数据

### 迁移检查清单
- [ ] Text-Splitter Engine 独立化完成
- [ ] 现有 document_parser.py 功能迁移
- [ ] API 兼容性测试通过
- [ ] 性能基准测试通过
- [ ] 文档更新完成

---

**文档版本**: 2.0.0
**创建日期**: 2025-08-14
**维护者**: AI Assistant
