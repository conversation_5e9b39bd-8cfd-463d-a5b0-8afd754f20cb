# 架构设计文档 - v1.1

## 1. 概述

本文档基于 v1.0 的模块化重构基础，并结合对 `Mirix-AI/MIRIX` 项目的学习洞察，提出 v1.1 的架构演进方案。

核心目标：
1.  **引入“智能体层 (Agent Layer)”** 作为系统的智能协调核心。
2.  **明确服务化方向**，解决 v1.0 中 `services` 目录存在的结构混乱问题。
3.  为实现 PRD v1.1 中定义的功能需求提供清晰的架构指引。

## 2. 架构图 (v1.1)

v1.1 架构的核心变化是正式引入 **智能体层**，并明确所有业务逻辑都将以 **内部模块化服务** 的形式存在于 `backend/app/services/` 中，而根目录的 `services/` 将被废弃或重构。

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 Frontend Layer                    │
├─────────────────────────────────────────────────────────────┤
│                  API网关层 API Gateway Layer                │
├─────────────────────────────────────────────────────────────┤
│                  智能体层 Agent Layer (v1.1 核心)            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌──────────┐ │
│  │ Document    │ │  Episodic   │ │  Semantic   │ │  Intent  │ │
│  │   Agent     │ │   Agent     │ │   Agent     │ │   Agent  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └──────────┘ │
├─────────────────────────────────────────────────────────────┤
│             应用服务层 Application Services (内部模块化)       │
│  (位于 backend/app/services/)                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   User      │ │   Topic     │ │ Conversation│          │
│  │  Service    │ │  Service    │ │  Service    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                  核心引擎层 Core Engine Layer                │
│  (位于 engines/)                                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Text-Splitter│ │ Embedding   │ │ Context     │          │
│  │   Engine    │ │  Service    │ │  Engine     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                  数据持久层 Data Persistence Layer          │
└─────────────────────────────────────────────────────────────┘
```

## 3. 核心变更详解

### 3.1. 智能体层 (Agent Layer)

- **位置**: `backend/app/agents/`
- **核心组件**:
    - **`AgentCoordinator`**: 负责维护对话的整体流程，根据 **意图** 智能拼接上下文，并作为API层的统一入口路由任务。
    - **`DocumentAgent`**: 负责外部知识（文档）的上传、分割、向量化等，并为未来管理其**完整生命周期**提供接口。
    - **`EpisodicAgent`**: 负责记录和检索每一轮对话的 **原文**（短期情景记忆）。
    - **`SemanticAgent`**: (职责变更) 负责为 **每一轮对话** 生成用户和AI的 **双边摘要**，用于前端展示和快速回忆。
    - **`IntentAgent` (新增)**: 负责在对话开始时（特别是基于文档时）确立核心目标/意图，并在对话中动态维持这个“内心活动”的上下文，指导对话方向和工具调用。
- **工具系统**: 每个智能体将被赋予一套明确的工具（Tools），这些工具是对底层服务和引擎中原子能力的封装。LLM将决策调用哪个工具，而工具则负责执行。

### 3.2. 服务架构统一

- **明确方向**: 为了降低系统复杂度和运维成本，v1.1 阶段，我们决定**统一采用“内部模块化”**的方式来组织服务。
- **行动计划**:
    1.  所有新的业务逻辑都应在 `backend/app/services/` 目录下创建新的模块。
    2.  逐步将根目录 `services/` 下的现有逻辑（如 `topic` 服务）迁移整合到 `backend/app/services/` 中。
    3.  最终目标是**废弃根目录的 `services/`**，形成一个职责清晰的、以 `backend` 为核心的单体应用。微服务化改造将作为未来更长远的规划。

### 3.3. 异步处理机制

- **引入 `ProcessingBuffer`**: 借鉴 `MIRIX` 的 `TemporaryMessageAccumulator`，我们将实现一个基于 Redis 的 `ProcessingBuffer`。
- **应用**: `DocumentAgent` 将使用此机制来缓冲文档处理任务，配合 Dramatiq 实现高效的异步批量处理。

## 4. 实施计划

1.  **创建 `backend/app/agents/` 目录**，并实现 `AgentCoordinator` 和三个核心智能体的基本框架。
2.  **为智能体设计并实现第一批工具**，封装对 `DocumentService`, `ConversationService` 等的调用。
3.  **改造 API 路由**，将文档上传和对话处理的入口，从直接调用 `Service` 改为调用 `AgentCoordinator`。
4.  **实现 `ProcessingBuffer`**，并将其集成到 `DocumentAgent` 的工作流中。
5.  **启动 `services/` 目录的迁移工作**，首先从 `topic` 服务开始，将其逻辑整合进 `backend/app/services/topic/`。
6.  **更新所有相关测试**，确保在新的架构下，所有核心功能回归测试通过。
## 5. 核心流程：用户对话处理

为了更清晰地展示 v1.1 架构下的系统运作方式，以下是当用户发送一条消息时，系统内部处理的 Mermaid 流程图。

```mermaid
sequenceDiagram
    participant User as 用户
    participant Gateway as API网关
    participant Coordinator as AgentCoordinator
    participant I_Agent as IntentAgent
    participant Tools as 工具系统
    participant LLM as LLM
    participant DB as 数据库

    %% 0. (可选) 对话开始时确立意图 %%
    opt 基于新文档的对话
        Coordinator->>I_Agent: establish_intent(document)
        I_Agent->>LLM: 分析文档，提取核心知识与目标
        LLM-->>I_Agent: 返回初始意图
        I_Agent->>DB: 存储初始意图
    end

    %% 1. 用户发送消息 %%
    User->>Gateway: POST /api/v1/topics/{id}/messages
    Gateway->>Coordinator: handle_request(topic_id, request_data)

    %% 2. Coordinator 构建智能上下文 %%
    note over Coordinator: 以“意图”为核心构建上下文
    Coordinator->>I_Agent: get_current_intent()
    I_Agent-->>Coordinator: 返回当前对话意图/目标
    Coordinator->>Tools: get_recent_turns(last_n=3)
    Tools-->>Coordinator: 返回最近原文
    Coordinator->>Tools: (可选) search_knowledge_by_intent(intent)
    Tools-->>Coordinator: (可选) 返回相关知识片段

    %% 3. LLM 决策与执行 %%
    Coordinator->>LLM: 构造Prompt (意图 + 最近对话 + 知识 + 问题)
    LLM-->>Coordinator: 返回最终AI回答 (或工具调用决策)
    Coordinator-->>Gateway: 流式返回回答
    Gateway-->>User: 展示回答

    %% 4. 异步并行记忆与意图更新 %%
    par
        Coordinator->>Tools: save_episodic_memory(turn_data)
        Tools->>DB: 存储完整对话回合
    and
        Coordinator->>Tools: generate_turn_summary(turn_data)
        note right of Tools: 为本轮生成双边摘要
        Tools->>LLM: (内部调用) 生成用户和AI的摘要
        LLM-->>Tools: 返回双边摘要
        Tools->>DB: 将摘要与回合关联存储
    and
        Coordinator->>I_Agent: update_intent(latest_turn)
        I_Agent->>LLM: (内部调用) 根据最新对话，评估并更新意图
        LLM-->>I_Agent: 返回更新后的意图
        I_Agent->>DB: 存储新意图
    end
```
## 6. 借鉴 MIRIX 的实践与具体建议

在调研开源项目 MIRIX 的多智能体实现后，我们把可直接借鉴并适配到 v1.1 的实践要点整理如下，供实现阶段参考与落地。

### 6.1 核心设计启发（高层总结）
- **专家团队模式（Multi-Agent）**：用职责单一、内聚的“专家智能体”代替臃肿的单体 AI。保持每个 Agent 职责纯粹（Document、Episodic、Semantic），由统一协调器路由任务。
- **工具化能力（Tools）**：把底层能力（如写入记忆、检索、分片/向量化、外部 API 调用）封装成原子化工具函数，供 LLM 决策调用。工具函数应包含清晰的 docstring 与参数 schema，便于 LLM 的 function-calling。
- **动静分离与缓冲机制（Temporary/Processing Buffer）**：用临时队列先接收高频输入（截图、上传文档、用户消息），异步批量吸收到记忆，保证前端即时性与后端吞吐能力。
- **双轨并行记忆（Episodic & Semantic Parallelism）**：对同一事件并行写入情景记忆（可回溯）与语义记忆（摘要/知识卡），提高系统可回溯性与检索质量。

### 6.2 具体架构建议（如何在 v1.1 中落地）
- **AgentCoordinator（对应 MIRIX 的 AgentWrapper）**
  - **责任**：Agent 实例化、统一入口（API 路由）、上下文构建、向各 Agent 分发任务、并发触发记忆写入与工具调用。
  - **要点**：初始化时把各 Agent 的 system prompt 与工具集注册到后端元数据（agent_state）；暴露对外简洁接口（如 `handle_user_message(topic_id, message)`）。
- **工具系统（Tool System）**
  - **实现方式**：在 `backend/app/functions` 或 `backend/app/tools` 下定义“工具函数集”（按记忆类型、文档处理、检索等分组），每个工具为原子操作并带有 JSON schema。
  - **运行时**：Agent 在调用工具时将 `self`（或 `agent_id`）传入，工具内部通过 `services/engines` 调用 DB、向量引擎或触发异步任务。
  - **推荐**：采用“函数式工具 + JSON schema”的组合，兼容主流 LLM 的 function-calling/structured-output 特性。
- **临时消息累加器 / ProcessingBuffer**
  - **实现方式**：设计 `TemporaryMessageAccumulator`（内存/轻量持久队列）与 `ProcessingBuffer`（基于 Redis + Dramatiq 或其他队列）：
    - 接收上传与消息（低延迟）→ 存入临时队列 → 达到阈值或定时触发 → 批量异步处理（分割、向量化、入库、摘要）。
  - **好处**：前端体验平滑，后端通过批量任务提升吞吐与稳定性。
- **并行触发记忆更新**
  - **模式**：在一次逻辑回合结束时，Coordinator 广播事件到 `EpisodicAgent` 与 `SemanticAgent`，两者可并行执行并通过关联 id（`turn_id`）建立回溯链。
  - **工具**：使用消息队列（Dramatiq/Celery/ThreadPoolExecutor）来并行触发并处理结果（可同步等待或异步返回）。
- **Agent 元数据与工具注册**
  - 在数据库中为每个 Agent 保留 `agent_state`（system prompt、工具清单、llm_config、message_ids 等），支持运行时更新系统 prompt 与工具。
  - 提供 `tool_manager`（或 tools upsert API），便于在不改代码的前提下为 Agent 分配新能力。
- **报错与回退策略**
  - LLM 与工具执行需包含重试、分段重试与“最大链路步数”限制（借鉴 MIRIX 的 `contine_chaining`），并在函数失败时能发出心跳/提示，避免无限循环。
- **监控与审计**
  - 在 Coordinator 层记录每次调用的 `usage`（token、latency）、工具调用历史、memory 更新日志，便于后续优化与审计。

### 6.3 与我们现有 v1.1 文档的对齐（具体变更点）
- `backend/app/agents/`：
  - 明确实现 `AgentCoordinator`（或 `AgentWrapper`）负责“agent_state 注册、tools 初始化、统一入口”。
  - 为每个 Agent 增设 `tools` 列表与 `tool_rules`（支持 function calling 的约束）。
- `DocumentAgent`：
  - 引入 `ProcessingBuffer`（Redis + Dramatiq 建议）用于文档分片、向量化与异步插入。
  - 增加工具：`document.split`、`document.vectorize`、`document.upsert_chunks`、`document.get_processing_status`。
- `EpisodicAgent` 与 `SemanticAgent`：
  - 实现“并行写入”流程：Coordinator 广播事件到两者并保持关联 id（`turn_id`）。
- **工具定义规范**：
  - 所有工具函数应包含：清晰 docstring、参数 schema、返回类型说明（兼容 LLM 的 function schema）。
  - 在 Agent 初始化时把工具的 `json_schema` 注入 `agent_state.tools`（方便 LLM 在构造函数调用时使用）。
- **Persona 与 System Prompt 管理**：
  - 把 persona/核心记忆作为可编辑 block（`block manager`），允许在运行时用 `apply_persona_template` 更新 system prompt。

### 6.4 推荐的实施步骤（短期到中期）
- **短期（1–2 周）**
  1. 在 `backend/app/agents/` 新增 `AgentCoordinator` 框架（接口、初始化、工具注册）。
  2. 在 `backend/app/functions/` 建立 `tools` 模块，先实现核心工具（episodic insert、semantic insert、document split）。
  3. 实现 `TemporaryMessageAccumulator` 的最简版（内存队列 + 触发接口）。
  4. 编写集成测试：用户消息 → Coordinator → 并行触发 Episodic & Semantic 工具，验证关联 id 能回溯。
- **中期（3–6 周）**
  1. 引入 Redis + Dramatiq 实现 `ProcessingBuffer` 的批量异步处理。
  2. 为工具系统实现 `tool_manager`（管理 tool metadata 与 json_schema）。
  3. 将 `_legacy_services/` 中相关逻辑逐步迁移到 `backend/app/services/`，并把对外调用封装为工具。
  4. 增强监控（token 使用、工具失败率、异步任务耗时）。
- **长期（6–12 周）**
  1. 在需求明确时按需拆分更多专家 Agent（resource/procedural/knowledge_vault 等）。
  2. 优化并行体系（更智能的任务路由、优先级、回退机制）。
  3. 开放工具注册 API，使运维或产品团队能按需动态为 Agent 分配工具。

### 6.5 小结
- MIRIX 通过“工具化 + 协调器 + 异步缓冲”有效支撑复杂长期记忆系统。把这些实践适度引入 v1.1，不会使设计臃肿，反而在保证 MVP 的前提下，为未来扩展留下明确、安全的路径。
- **长期启发**: MIRIX 对记忆的超细分工（如程序记忆、核心记忆）和其前瞻性的`ReflexionAgent`（反思智能体），为我们 v1.2 及之后的版本演进提供了宝贵的思路。我们当前的设计为未来孵化这些更高级的专家智能体预留了清晰的扩展点。
---

## 附录：MIRIX 项目工作流程参考

为了更直观地对比我们的 v1.1 架构与 `MIRIX` 的实现差异，以下是由 `deepwiki` 生成的 `MIRIX` 项目在接收到文档和用户消息时的核心工作流程图。

```mermaid
sequenceDiagram
    participant User as 用户
    participant API as FastAPI Endpoint
    participant Wrapper as AgentWrapper
    participant TempBuffer as TemporaryMessageAccumulator
    participant ProcBuffer as ProcessingBuffer (Dramatiq)
    participant DocAgent as DocumentAgent
    participant EpisodicAgent as EpisodicMemoryAgent
    participant SemanticAgent as SemanticMemoryAgent
    participant LLM as LLM Integration
    participant DB as Vector/SQL Database

    alt 场景1: 用户上传文档
        User->>API: POST /v1/topics/{id}/upload
        API->>Wrapper: handle_document_upload(doc)
        Wrapper->>TempBuffer: add_document(doc)
        note right of TempBuffer: 快速接收, 存入内存/轻量队列
        TempBuffer-->>Wrapper: ack (立即响应)
        Wrapper-->>API: { "message": "Document received", "task_id": ... }
        API-->>User: 返回成功信息

        loop 异步批量处理
            TempBuffer->>ProcBuffer: flush_documents()
            ProcBuffer->>DocAgent: process_batch(docs)
            DocAgent->>LLM: (可选) 生成文档摘要
            DocAgent->>DB: 分片、向量化并存储
        end
    end

    alt 场景2: 用户发送消息
        User->>API: POST /v1/topics/{id}/message
        API->>Wrapper: handle_user_message(msg)
        Wrapper->>EpisodicAgent: get_context()
        EpisodicAgent-->>Wrapper: 返回对话历史
        
        Wrapper->>LLM: build_prompt_with_tools()
        LLM-->>Wrapper: 决策: 调用工具 `semantic_search`

        Wrapper->>SemanticAgent: execute_tool('semantic_search', query)
        SemanticAgent->>DB: 向量检索
        DB-->>SemanticAgent: 返回相关知识
        SemanticAgent-->>Wrapper: 返回工具结果

        Wrapper->>LLM: execute_prompt_with_context(tool_result)
        LLM-->>Wrapper: 生成最终回答
        Wrapper-->>API: 流式返回回答
        API-->>User: 实时展示回答

        par 异步记忆更新
            Wrapper->>EpisodicAgent: save_turn()
            EpisodicAgent->>DB: 存储完整对话
        and
            Wrapper->>SemanticAgent: summarize_and_save()
            SemanticAgent->>LLM: (内部) 生成摘要
            LLM-->>SemanticAgent: 返回摘要
            SemanticAgent->>DB: 存储摘要
        end
    end
```
---

## 附录：MIRIX 项目工作流程参考

为了更直观地对比我们的 v1.1 架构与 `MIRIX` 的实现差异，以下是由 `deepwiki` 生成的 `MIRIX` 项目在接收到文档和用户消息时的核心工作流程图。

```mermaid
sequenceDiagram
    participant User as 用户
    participant Wrapper as AgentWrapper
    participant TempBuffer as TemporaryMessageAccumulator
    participant ChatQueue as MessageQueue
    participant MemorySystem as 记忆系统
    participant ChatAgent as 聊天代理
    participant DB as 持久化层

    User->>Wrapper: send_message(data, memorizing=true/false)

    alt memorizing is True (存入记忆)
        Wrapper->>TempBuffer: add_message(data)
        note right of TempBuffer: 消息在此累积
        alt 达到吸收阈值
            TempBuffer->>MemorySystem: absorb_content_into_memory()
            MemorySystem->>DB: 各记忆代理并行处理并存储
        end
    else memorizing is False (即时聊天)
        Wrapper->>ChatQueue: send_message_in_queue(data)
        ChatQueue->>ChatAgent: step()
        ChatAgent->>DB: (可选) 检索上下文
        ChatAgent-->>Wrapper: 返回聊天回复
        Wrapper-->>User: 返回最终结果
    end
```