# LangExtract 工具评估与整合计划

## 1. 评估结论

经过对 `langextract` 工具的调研，并结合项目 v1.1 的 PRD 和架构文档进行分析，我们得出以下结论：

**`langextract` 与“知深学习导师”项目的需求高度契合，是一个能够显著加速核心功能开发、提升系统质量、并直接解决关键技术难题的战略性组件。强烈建议在 v1.1 的实现中引入该工具。**

## 2. 核心价值与项目需求映射

| `langextract` 特性 | "知深学习导师" v1.1 需求 | 价值分析 |
| :--- | :--- | :--- |
| **提取结构化信息** | `SemanticAgent` 生成**结构化的摘要对象** | **完美契合**。可以将对话转化为可查询、可分析的结构化数据，而非简单的文本摘要，为未来高级功能奠定基础。 |
| **精准的来源追溯** | 点击摘要**平滑滚动到原文** | **直接赋能**。该特性是实现此项前端交互的关键，将开发难度从“困难”降至“简单”。 |
| **可靠的 Schema 强制** | 生成稳定的**双边摘要** | **提升鲁棒性**。确保 LLM 输出格式的稳定性，避免服务端解析错误。 |
| **交互式可视化** | 开发与调试需求 | **提升开发效率**。提供强大的调试工具，快速验证和优化提取质量。 |

## 3. 整合计划 Todo List

为了将 `langextract` 有序地整合进我们的项目中，我制定了以下待办事项列表。

```mermaid
graph TD
    A[环境准备与原型验证] --> B[设计语义记忆 Schema];
    B --> C[SemanticAgent 核心逻辑实现];
    C --> D[数据库模型调整];
    D --> E[API 与前端对接];
    C --> F[编写集成测试];

    subgraph "Phase 1: 验证与设计"
        A("1. 环境准备与原型验证<br/>- 安装 langextract 库<br/>- 配置 API Key<br/>- 编写一个独立的 PoC 脚本，验证核心提取功能")
        B("2. 设计语义记忆 Schema<br/>- 定义 v1.0 版本的摘要对象 JSON Schema<br/>- 初步 schema 应包含 speaker, intent, key_concepts 等字段")
    end

    subgraph "Phase 2: 后端实现"
        C("3. SemanticAgent 核心逻辑实现<br/>- 将 PoC 逻辑封装到 SemanticAgent 中<br/>- 实现调用 langextract 的工具函数<br/>- 作为异步任务被 AgentCoordinator 调用")
        D("4. 数据库模型调整<br/>- 修改 Summary 表结构，用于存储结构化 JSON 数据<br/>- 增加字段存储 source_grounding 的位置信息")
        F("6. 编写集成测试<br/>- 针对 SemanticAgent 编写测试用例<br/>- 验证输入一个对话回合后，数据库能正确生成并存储结构化摘要")
    end

    subgraph "Phase 3: 前端与联调"
        E("5. API 与前端对接<br/>- 调整获取摘要的 API，使其能返回来源位置信息<br/>- 前端实现点击摘要，根据位置信息高亮并滚动到原文的功能")
    end
```

## 4. 后续步骤

1.  请团队成员审阅此评估报告和整合计划。
2.  若计划通过，我们将切换到 `code` 模式，开始执行 "Phase 1: 验证与设计" 中的任务。

我已将此评估报告和计划写入了 `docs/2_Architecture/07_langextract_evaluation_and_plan.md`。

您对这个计划满意吗？如果满意，我们就可以准备切换模式，开始着手实施了。