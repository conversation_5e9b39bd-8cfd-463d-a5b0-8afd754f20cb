# 架构优化提案 - v1.2: 引入 LangGraph 改造智能体层

## 1. 背景与动机

在 v1.1 架构设计中，我们借鉴 `MIRIX` 项目，提出了一个以 `AgentCoordinator` 为核心的多智能体协作模型。该设计思路清晰，职责分明。

然而，通过对 `MIRIX` 源码的深入分析，我们发现其智能体调度、工具调用和状态管理框架是**完全自研**的。这意味着，如果我们沿用 v1.1 的设计，将需要投入大量精力去开发和维护一个功能上类似于 LangChain 的“迷你框架”，这属于“重复造轮子”，会显著增加项目复杂度和风险，延缓核心业务功能的交付。

**核心问题**: 我们是在构建一个 AI 应用，还是在构建一个构建 AI 应用的框架？

**结论**: 我们的目标是前者。因此，我们应该积极拥抱社区成熟的框架来解决通用的“框架层”问题，将我们的精力聚焦于“应用层”的业务逻辑。

## 2. 提案：使用 LangGraph 作为智能体层的核心

我们提议，使用 LangChain 开源框架中的 **LangGraph** 组件，来完全替代我们原计划自研的 `AgentCoordinator` 和智能体协作逻辑。

**LangGraph** 是一个专门用于构建复杂、有状态的、多智能体应用的库。它以“图”的形式组织执行流程，完美契合我们“多专家智能体协作”的设计思想。

### 2.1 核心概念映射

| v1.1 自研概念 | LangGraph 对应概念 | 说明 |
| :--- | :--- | :--- |
| **AgentCoordinator** | **`StateGraph` + Router Node** | LangGraph 本身就是协调器。我们可以定义一个图，其中一个节点充当路由器，根据状态决定任务流向下一个哪个专家节点。 |
| **专家智能体 (Agent)** | **Graph Node (图节点)** | 我们的 `DocumentAgent`, `IntentAgent` 等，都将成为图中的一个可调用节点。节点可以是一个简单的函数，也可以是一个独立的 `AgentExecutor`。 |
| **工具系统 (Tools)** | **LangChain `Tool`** | 概念完全一致。底层服务被封装为标准的 `Tool`，供图节点调用。 |
| **对话记忆** | **`StateGraph` 的 State** | 使用 `TypedDict` 定义一个包含 `messages`, `summary`, `intent` 的状态对象，在图的流转中自动传递和更新。 |
| **记忆持久化** | **`MemorySaver` Checkpointer** | 只需在编译图时传入一个 `checkpointer`，LangGraph 就会自动处理状态的存取，我们无需关心数据库操作。 |
| **异步处理 (`ProcessingBuffer`)**| **Async Tool + 任务队列** | 我们可以将需要异步处理的任务（如文档入库）封装成一个异步的 `Tool`。这个 Tool 的作用就是将任务推送到我们现有的 `Redis + Dramatiq` 队列中，与 LangGraph 无缝集成。 |

### 2.2 新架构图 (基于 LangGraph)

新的工作流程将不再是 `Coordinator` 的中央集权式调度，而是数据在预定义的“图”中的流动。

```mermaid
graph TD
    A[用户请求 API Gateway] --> B[LangGraph App];
    
    subgraph LangGraph App
        direction LR
        
        C(START) --> D{Router};
        D -- 文档相关 --> E[Document Node];
        D -- 对话相关 --> F[Chat Node];
        D -- 其他意图 --> G[...其他专家节点...];
        
        E --> H[Tool: add_doc_to_queue];
        F --> I[Tool: get_history];
        F --> J[Tool: vector_search];
        I --> F;
        J --> F;
        
        H --> K(END);
        F --> K;
        G --> K;
    end

    B --> L[最终响应];

    M[Redis + Dramatiq]
    H -.-> M;
```

**流程解读**:

1.  所有请求进入 `LangGraph App`。
2.  `Router` 节点（我们的 `IntentAgent` 逻辑所在地）首先被调用，它分析当前状态（`State`）中的用户问题和历史，决定下一步该走哪条“边”。
3.  **如果是文档上传**：流程进入 `Document Node`，该节点调用 `add_doc_to_queue` 工具，将任务发给后端的 Dramatiq 异步处理，然后流程结束。
4.  **如果是对话**：流程进入 `Chat Node`。`Chat Node` 内部可能会多次调用 `get_history`, `vector_search` 等工具来构建上下文，最终调用 LLM 生成回答，更新状态中的 `messages`，然后流程结束。
5.  每一步操作，`StateGraph` 的状态都会被 `MemorySaver` 自动记录下来。

## 3. 优势分析

采用 LangGraph 方案将带来以下核心优势：

*   **大幅降低开发成本**: 我们无需再实现复杂的 Agent 调度、状态管理、错误重试逻辑。可以将 1-2 周的框架开发时间，缩短为几天甚至几小时的配置和集成工作。
*   **提升系统健壮性**: LangChain/LangGraph 经过了广泛的社区测试和生产环境验证，其稳定性和可靠性远超我们自研的“轮子”。
*   **更强的可观察性**: LangGraph 配合 LangSmith，可以提供开箱即用的、细粒度的可视化追踪和调试能力，这对优化复杂的 Agent 轨迹至关重要。
*   **更高的灵活性与扩展性**: LangGraph 的图结构天然支持复杂逻辑（如循环、条件分支），未来增加新的专家智能体，只需在图中增加新的节点和边，对现有逻辑**零侵入**。
*   **与社区生态保持同步**: 我们可以直接享受到 LangChain 社区在 `Tool`、`Agent`、`LLM` 对接等方面的最新成果，而无需自己维护。

## 4. 修订后的实施计划

原有的实施计划可以大幅简化：

1.  **阶段一: 环境与工具准备 (1-2天)**
    *   引入 `langchain`, `langgraph` 等相关依赖。
    *   将我们现有的 `DocumentService`, `ConversationService` 等核心逻辑，按照 LangChain 的规范封装成 `Tool`。

2.  **阶段二: 核心图谱构建 (2-3天)**
    *   定义 `StateGraph` 的 `State` schema（包含 `messages`, `intent`, `summary` 等）。
    *   编写图的各个 `Node` 函数（如 `route_action`, `handle_document`, `generate_response`）。
    *   定义节点之间的 `Conditional Edges`（条件边），实现路由逻辑。

3.  **阶段三: 集成与持久化 (1天)**
    *   配置 `MemorySaver`，使用 `SQLite` (开发) 或 `Postgres` (生产) 作为后端。
    *   将编译好的 `LangGraph App` 对接到 FastAPI 的一个接口上。

4.  **阶段四: 测试与迭代**
    *   编写集成测试，验证多智能体协作的流程是否符合预期。
    *   利用 LangSmith 进行调试和优化。

**预估对比**:
*   **原方案 (自研)**: 核心框架开发预计 **2-3周**。
*   **新方案 (LangGraph)**: 核心框架集成预计 **1周内**。

## 5. 结论

`MIRIX` 项目为我们提供了宝贵的**架构思想**，但不应成为我们技术实现的包袱。

通过引入 LangGraph，我们可以在不牺牲原有架构设计思想的前提下，用一种**更现代、更高效、更稳健**的方式来落地我们的智能体层。这能让我们把宝贵的开发资源从底层框架的构建中解放出来，真正聚焦于打磨产品的核心业务逻辑和用户体验。

建议团队采纳此优化方案，将 v1.1 架构升级为基于 LangGraph 的 v1.2 架构。

---
