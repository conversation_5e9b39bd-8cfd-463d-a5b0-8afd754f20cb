# LangGraph 实施计划 (v1.2)

## 1. 目标

本文档旨在将 `v1.2` 的 LangGraph 架构提案和 `deepwiki` 的技术建议，转化为一个可执行的、分阶段的开发计划。完成此计划后，我们将拥有一个基于 LangGraph 的、健壮且可扩展的智能体层。

## 2. 最终架构图 (基于 LangGraph)

```mermaid
graph TD
    A[API Gateway] --> B[LangGraph App];

    subgraph LangGraph App
        direction LR
        
        C(START) --> Router{Intent Router};
        Router -- 文档处理 --> DocNode[Document Node];
        Router -- 对话/问答 --> ChatNode[Chat Node];
        Router -- 其他意图 --> Others[...其他专家节点...];
        
        DocNode --> Tool_AsyncDoc[Tool: process_document_async];
        ChatNode --> Tool_GetHistory[Tool: get_history];
        ChatNode --> Tool_VectorSearch[Tool: vector_search];
        ChatNode --> Tool_Summarize[Tool: generate_turn_summary];
        
        Tool_GetHistory --> ChatNode;
        Tool_VectorSearch --> ChatNode;
        Tool_Summarize --> MemoryNode;

        subgraph Memory Persistence
            direction TB
            MemoryNode[Memory Write Node] --> ParallelWrites{RunnableParallel};
            ParallelWrites --> Tool_SaveEpisodic[Tool: save_episodic_memory];
            ParallelWrites --> Tool_SaveSemantic[Tool: save_semantic_memory];
        end
        
        ChatNode --> MemoryNode;
        DocNode --> END(END);
        MemoryNode --> END;
        Others --> END;
    end

    B --> Response[API Response];

    subgraph Async Backend
        Tool_AsyncDoc -.-> RedisQueue[Redis + Dramatiq];
    end
```

## 2.1 架构增强要点

- 状态寻址与多租户隔离
  - 统一引入 tenant_id, user_id, topic_id, thread_id 作为图应用的 configurable 维度键，用于 Checkpointer 寻址与精准回放。
  - 在图编译与调用时强制注入上述键，确保横向扩展与多租户隔离。
- State 最小化与上下文管理器
  - 在 State 中仅存放轻量引用（如 message_id、doc_id、chunk_ids），大对象存外部存储由节点按需加载。
  - 引入 ContextManager 节点：负责检索、排序、去重与裁剪，按 Token 预算组装上下文，避免上下文溢出。
- 路由器前置规则与短路策略
  - 在 Intent Router 前增加规则网关（关键词/模式/标志位），命中则短路路径，降低延迟与成本。
  - 对每条规则与路径输出可观测指标（命中率、误判率）用于后续调参。
- 并发记忆写入与最终一致性
  - 贯穿 turn_id，MemoryWriteNode 内使用并行执行（如 RunnableParallel）调用 save_episodic_memory 与 save_semantic_memory。
  - 落库层使用幂等键 turn_id 去重；失败进入补偿队列，采用最终一致性，避免阻塞主链路。
- 异步回调恢复与软对账
  - 文档处理采用 发起-等待-恢复 三段式：发起后记录 pending；Dramatiq 完成以 correlation_id 回调；恢复图继续执行。
  - 定时巡检长期 pending 任务（软对账），TTL 到期自动重试或降级。
- 工具契约与参数校验
  - 使用 @tool 类型提示生成 JSON Schema；对必填、枚举、范围进行参数预检，避免错误输入进入 LLM。
  - 为关键工具提供验证器工具与降级路径；定义稳定的错误码与错误消息结构（便于前后端与观测联动）。
- 可观测性与成本护栏
  - 统一追踪：节点耗时、工具失败率、Token 与成本、上下文长度、路由命中率、重试次数、异步滞留时长。
  - 双通道观测：LangSmith 跟踪链路与样本，Prometheus 采集系统指标；日志结构化并关联 request_id 与 turn_id。
  - 成本护栏：速率与并发限流、上下文预算器、摘要增量阈值、模式区分（快速 vs 深思）。
- 数据安全与合规
  - 在摘要与记忆写入前进行 PII/敏感信息掩码；向量入库支持可选脱敏；回放链路加权限检查。
  - 关键数据加密存储；审计日志记录敏感读写与导出操作。
- 文档生命周期与权限模型
  - 扩展 DocumentAgent 工具至全生命周期：查询、更新、删除、版本与权限控制；向量化内容哈希去重与版本戳。
- 迁移与回滚策略
  - 引入灰度开关逐步迁移（按 Topic/用户切流）；双写对账，周期核对一致性与延迟差；保留旧路径可随时回滚。

### 新增时序：异步回调恢复

```mermaid
sequenceDiagram
    participant API as API Gateway
    participant Graph as LangGraph App
    participant Queue as Redis Dramatiq
    participant Worker as Doc Worker
    participant Store as Checkpoint

    API->>Graph: 发起文档处理请求
    Graph->>Queue: 推送任务并记录 pending
    Queue->>Worker: 拉取任务执行
    Worker->>API: 回调携带 correlation_id 与结果
    API->>Graph: 触发恢复执行并注入结果
    Graph->>Store: 更新状态与对账
    Graph-->>API: 返回最终状态
```

### 执行清单增补

- Phase 0 新增（决策与基线）
  - 形成 ADR 与接口契约：状态 schema、寻址键、工具契约、错误码。
  - 搭建观测与限流骨架：LangSmith、Prometheus、结构化日志与 request_id/turn_id 关联。
- Phase 1 增强（环境与工具）
  - 为每个工具补齐参数校验与示例用例；输出稳定错误码与错误消息结构。
  - 扩展文档生命周期工具与内容哈希去重。
- Phase 2 增强（图谱与上下文）
  - 增加 ContextManager 节点与策略文件；路由前置规则网关；记忆并发写入节点的失败分支与补偿挂载。
  - 在路由层显式区分快速模式与深思模式，快速模式跳过昂贵流程。
- Phase 3 增强（持久化与恢复）
  - Checkpointer 建议 Redis 热态 + Postgres 冷态与审计；定义清理策略与尺寸阈值。
  - 定义回调 API、回放触发接口、软对账巡检任务与重试上限。
- Phase 4 增强（测试与发布）
  - 增加金丝雀对比测试与回放回归测试；引入故障演练（队列延迟、向量库限速、LLM 超时、工具异常）。
  - 灰度发布、双写对账与回滚预案。
## 3. 实施计划 Todo List

我们将分阶段实施，确保每个阶段都有明确的产出和验证点。

```mermaid
gantt
    title LangGraph 实施甘特图
    dateFormat  YYYY-MM-DD
    axisFormat %m-%d
    
    section Phase 1: 环境与基础工具 (预计 3 天)
    环境搭建与依赖引入 :done, p1_t1, 2025-08-21, 1d
    核心服务工具化封装 :p1_t2, after p1_t1, 2d
    
    section Phase 2: 核心图谱构建 (预计 4 天)
    定义 State Schema :p2_t1, after p1_t2, 1d
    构建核心图节点 (Nodes) :p2_t2, after p2_t1, 2d
    定义图的路由逻辑 (Edges) :p2_t3, after p2_t2, 1d

    section Phase 3: 持久化与集成 (预计 2 天)
    配置 Redis Checkpointer :p3_t1, after p2_t3, 1d
    API 接口对接 :p3_t2, after p3_t1, 1d

    section Phase 4: 测试与优化 (持续)
    编写集成测试 :p4_t1, after p3_t2, 3d
    集成 LangSmith 调试 :p4_t2, after p4_t1, 2d
```

### Phase 1: 环境与基础工具

*   [ ] **任务 1.1: 环境搭建与依赖引入**
    *   在 `pyproject.toml` 中添加 `langchain`, `langgraph`, `langchain-community`, `langchain-openai` 等核心依赖。
    *   配置好 LangSmith 的环境变量，用于后续的调试与追踪。

*   [ ] **任务 1.2: 核心服务工具化封装**
    *   **目标**: 将我们现有的服务逻辑，按照 `@tool` 规范封装成 LangChain Tools。
    *   **具体工具**:
        *   `document_processing.py`:
            *   `process_document_async(doc_content, doc_id)`: 将文档处理任务推送到 Dramatiq 队列。
        *   `memory_tools.py`:
            *   `get_history(topic_id)`: 获取对话历史。
            *   `vector_search(query, topic_id)`: 执行向量检索。
            *   `generate_turn_summary(turn_data)`: 调用 `langextract` 生成双边摘要。
            *   `save_episodic_memory(turn_id, data)`: 保存情景记忆。
            *   `save_semantic_memory(turn_id, data)`: 保存结构化语义记忆。
    *   **要求**: 每个工具必须有清晰的 `docstring` 和准确的 Python 类型提示。

### Phase 2: 核心图谱构建

*   [ ] **任务 2.1: 定义 State Schema**
    *   创建一个 `schemas/graph_state.py` 文件。
    *   在其中定义 `AgentState(TypedDict)`，至少包含以下字段：
        ```python
        class AgentState(TypedDict):
            messages: List[BaseMessage]
            summary: str
            intent: Optional[str]
            turn_id: str
            source_grounding: List[Document]
            # ... 其他必要字段
        ```

*   [ ] **任务 2.2: 构建核心图节点 (Nodes)**
    *   创建 `graph/nodes.py` 文件。
    *   实现以下核心节点函数，每个函数接收 `state: AgentState` 并返回一个字典用于更新 state。
        *   `intent_router(state)`: 对应 `IntentAgent`，分析 `state.messages`，返回下一步的路由决策（如 "document", "chat"）。
        *   `document_node(state)`: 调用 `process_document_async` 工具。
        *   `chat_node(state)`: 编排 `get_history`, `vector_search` 等工具调用，最终调用 LLM 生成回答。
        *   `memory_write_node(state)`: 使用 `RunnableParallel` 并行调用 `save_episodic_memory` 和 `save_semantic_memory` 工具。

*   [ ] **任务 2.3: 定义图的路由逻辑 (Edges)**
    *   创建 `graph/main.py` 文件。
    *   实例化 `StateGraph(AgentState)`。
    *   添加所有节点 (`add_node`)。
    *   设置入口点 (`set_entry_point`)。
    *   使用 `add_conditional_edges` 连接 `intent_router` 和其他业务节点。
    *   使用 `add_edge` 连接其他节点，形成完整的业务流程图。

### Phase 3: 持久化与集成

*   [ ] **任务 3.1: 配置 Redis Checkpointer**
    *   在 `graph/main.py` 中，实例化 `RedisSaver`。
    *   在编译图时 (`graph.compile(checkpointer=redis_saver)`)，传入 checkpointer。
    *   验证对话状态能够被正确地存入和读取。

*   [ ] **任务 3.2: API 接口对接**
    *   修改 FastAPI 的相关路由（如 `/topics/{id}/messages`）。
    *   接口内部调用编译好的 `LangGraph App` (`app.invoke(...)`)。
    *   确保 `topic_id` 等信息能作为 `configurable` 参数传入，用于 checkpointer 正确寻址。

### Phase 4: 测试与优化

*   [ ] **任务 4.1: 编写集成测试**
    *   针对核心流程编写集成测试，覆盖：
        *   文档异步处理流程。
        *   标准问答与记忆写入流程。
        *   多轮对话的上下文连续性。

*   [ ] **任务 4.2: 集成 LangSmith 调试**
    *   在开发和测试过程中，通过 LangSmith UI 监控图的执行轨迹。
    *   分析每个节点的输入输出、工具调用的参数和结果，优化 Prompt 和图的逻辑。

## 4. 结论

该计划将 `deepwiki` 的高级建议分解为具体的、可操作的开发任务。通过遵循此计划，我们可以系统地、低风险地将我们的架构升级到基于 LangGraph 的 v1.2 版本，从而将开发重心聚焦于业务创新，而非底层框架的重复构建。

您对这个实施计划满意吗？如果满意，我们可以准备切换到 `code` 模式，并从 **Phase 1** 开始着手实施。