```mermaid
flowchart TD
    %% 用户输入起点
    A["用户输入<br/>(模糊需求)"] --> L1_Start

    %% L1 产品/服务层 (产品视角)
    subgraph L1_Layer ["L1 产品/服务 (产品视角)"]
        L1_Start["解析愿景/场景"] --> L1_Generate["生成决策题列表<br/>(选择题)"]
        L1_Generate --> L1_Choice{"用户选择/确认"}
        L1_Generate --> L1_Manual["未选择/需人工"]
        L1_Choice -->|反馈| L1_Generate
    end

    %% 对抗式审查流程 (右侧)
    subgraph Review_Flow ["对抗式审查 & 审核流"]
        L1_Review["L1 对抗审查"]
        L2Q["L2Q"]
        L3Q["L3Q"]
        L7Q["L7Q"]
    end

    %% 连接到审查流程
    L1_Choice --> L1_Review
    L2Q -.->|质疑| L1_Generate
    L3Q -.->|质疑| L1_Generate
    L7Q -.->|质疑| L1_Generate

    %% L2 核心架构层 (模式视角)
    L1_Review -->|通过| L2_Layer
    subgraph L2_Layer ["L2 核心架构 (模式视角)"]
        L2_Plan["给出架构方案 & 约束"] --> L2_Review["L2 对抗审查"]
        L2_Review -->|反馈| L2_Plan
    end

    %% L3 模块/组件层 (功能视角)
    L2_Review -->|通过| L3_Layer
    subgraph L3_Layer ["L3 模块/组件 (功能视角)"]
        L3_Decomp["拆分模块并定义接口契约"] --> L3_Review["L3 对抗审查"]
        L3_Review -->|反馈| L3_Decomp
    end

    %% L4 文件/子模块层 (实现视角)
    L3_Review -->|通过| L4_Layer
    subgraph L4_Layer ["L4 文件/子模块 (实现视角)"]
        L4_Detail["DB 表 / 路由 / 组件清单"]
    end

    %% L5 类/接口层 (对象视角)
    L4_Detail --> L5_Layer
    subgraph L5_Layer ["L5 类/接口 (对象视角)"]
        L5_Define["类与接口定义"]
    end

    %% L6 函数/方法层 (操作视角)
    L5_Define --> L6_Layer
    subgraph L6_Layer ["L6 函数/方法 (操作视角)"]
        L6_Code["方法伪代码、边界条件"]
    end

    %% L7 代码块层 (测试驱动)
    L6_Code --> L7_Layer
    subgraph L7_Layer ["L7 代码块 (测试驱动)"]
        L7_Gen["生成单元/集成测试"] --> L7_Review["L7 对抗审查"]
        L7_Review -->|反馈| L7_Gen
        L7_Review -->|通过| L7_Output["输出: 测试 + 骨架实现"]
        L7_Output -->|运行测试| L7_Review
    end

    %% 最终输出
    L7_Output --> FinalStep["代码生成 agent / 开发者 / CI"]

    %% 样式定义
    classDef l1Style fill:#ff9ff3,stroke:#333,stroke-width:2px
    classDef l2Style fill:#ffd93d,stroke:#333,stroke-width:2px
    classDef l3Style fill:#6bcf7f,stroke:#333,stroke-width:2px
    classDef l4Style fill:#4d79a4,stroke:#333,stroke-width:2px,color:#fff
    classDef l5Style fill:#f28e2c,stroke:#333,stroke-width:2px
    classDef l6Style fill:#e15759,stroke:#333,stroke-width:2px,color:#fff
    classDef l7Style fill:#af7aa1,stroke:#333,stroke-width:2px,color:#fff
    classDef reviewStyle fill:#bab0ab,stroke:#333,stroke-width:2px

    class L1_Layer l1Style
    class L2_Layer l2Style
    class L3_Layer l3Style
    class L4_Layer l4Style
    class L5_Layer l5Style
    class L6_Layer l6Style
    class L7_Layer l7Style
    class Review_Flow reviewStyle
```