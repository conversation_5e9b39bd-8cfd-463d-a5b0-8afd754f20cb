```mermaid
graph TD
  A["用户输入 (模糊需求)"] --> B1

  subgraph UX [用户交互与需求定义]
    B1["L1 产品/服务 (产品视角)"] --> B2("解析愿景/场景")
    B2 --> B3("生成决策题列表 (选择题)")
    B3 --> B4("用户选择/确认")
    B3 --> B5("未选择/需人工")
    B4 -- "反馈" --> B3
  end

  subgraph C [对抗式审查 & 审核流]
    direction LR
    C1["L1 对抗审查"]
    C2["L2Q"]
    C3["L3Q"]
    C4["L7Q"]
  end

  B4 --> C1
  C2 -- " " --> B3
  C3 -- " " --> B3
  C4 -- " " --> B3

  subgraph L2_SubGraph [L2 核心架构 (模式视角)]
    L2_Plan["给出架构方案 & 约束"] --> L2_Review("L2 对抗审查")
    L2_Review -- "反馈" --> L2_Plan
  end

  C1 -->|通过| L2_Plan
  L2_Review -->|通过| L3_Decomp

  subgraph L3_SubGraph [L3 模块/组件 (功能视角)]
    L3_Decomp["拆分模块并定义接口契约"] --> L3_Review("L3 对抗审查")
    L3_Review -- "反馈" --> L3_Decomp
  end

  L3_Review -->|通过| L4_Detail

  subgraph L4_SubGraph [L4 文件/子模块 (实现视角)]
    L4_Detail["DB 表 / 路由 / 组件清单"]
  end

  L4_Detail --> L5_Define

  subgraph L5_SubGraph [L5 类/接口 (对象视角)]
    L5_Define["类与接口定义"]
  end

  L5_Define --> L6_Code

  subgraph L6_SubGraph [L6 函数/方法 (操作视角)]
    L6_Code["方法伪代码、边界条件"]
  end

  L6_Code --> L7_Gen

  subgraph L7_SubGraph [L7 代码块 (测试驱动)]
    L7_Gen["生成单元/集成测试"] --> L7_Review("L7 对抗审查")
    L7_Review -- "反馈" --> L7_Gen
    L7_Review -->|通过| L7_Output("输出: 测试 + 骨架实现")
    L7_Output -- "运行测试" --> L7_Review
  end

  L7_Output --> FinalStep["代码生成 agent / 开发者 / CI"]
```