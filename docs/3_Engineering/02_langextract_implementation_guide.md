# LangExtract 实施指南

## 1. 概述

本文档基于 `deepwiki` 提供的详细信息，为 `SemanticAgent` 集成 `langextract` 提供具体的技术实施指导。文档的目标是作为后续编码工作的直接蓝图，确保开发过程的准确性和高效性。

## 2. 核心实施原则与最佳实践

根据 `deepwiki` 的建议，`SemanticAgent` 在调用 `langextract` 时应遵循以下最佳实践：

*   **清晰的提示 (`prompt_description`)**: 这是决定提取质量的关键。Prompt 必须明确、详细地描述需要提取的信息、规则和输出格式。
*   **高质量的示例 (`examples`)**: 至少提供 1-2 个高质量的 `lx.data.ExampleData` 实例。这些示例是 LLM 理解任务的“脚手架”，直接影响提取的准确性。
*   **模型选择 (`model_id`)**: 对于兼顾速度、成本和质量的场景，推荐使用 `gemini-2.5-flash`。在需要更高精度的复杂任务中，可以切换到 `gemini-2.5-pro`。
*   **异步处理**: `langextract` 的调用（涉及 LLM）应在异步任务中执行（例如，通过 Dramatiq），以避免阻塞主对话流程，这与我们 v1.1 架构设计中的并行记忆更新思想完全一致。

## 3. 语义记忆提取 Schema 设计 (v1)

为了从对话中捕获丰富的语义信息，我们设计了以下多类别的 JSON Schema。每个提取结果都是一个 `Extraction` 对象。

```json
[
  {
    "extraction_class": "dialogue_turn",
    "extraction_text": "[用户或AI的单句完整发言]",
    "attributes": {
      "speaker": "user | ai",
      "intent": "提问 | 澄清 | 总结 | 提供信息 | ...",
      "topic": "[本轮对话的核心主题]",
      "sub_topic": "[可选：更细分的主题]"
    }
  },
  {
    "extraction_class": "key_concept",
    "extraction_text": "[对话中出现的关键名词或概念]",
    "attributes": {
      "category": "技术术语 | 核心理论 | 工具名称 | ...",
      "relevance": "high | medium | low"
    }
  },
  {
    "extraction_class": "user_sentiment",
    "extraction_text": "[能体现用户情绪的文本片段]",
    "attributes": {
      "sentiment": "positive | negative | neutral | confused",
      "intensity": "high | medium | low"
    }
  }
]
```

**Schema 说明**:

*   `dialogue_turn`: 捕获每一轮对话的基本单元信息。
*   `key_concept`: 提取对话中值得被记录和关联的核心知识点。
*   `user_sentiment`: 用于分析用户在学习过程中的情绪状态，为未来实现更主动、更具共情能力的 AI 导师提供数据基础。

## 4. 完整 Python 代码范例 (PoC)

以下代码是 `SemanticAgent` 内部调用 `langextract` 的核心逻辑原型，可用于编写初期的 PoC (Proof of Concept) 脚本。

```python
import langextract as lx
import textwrap
import os

# --- 配置 (应从环境变量或配置文件读取) ---
# 确保您已设置 GOOGLE_API_KEY 环境变量
# os.environ['GOOGLE_API_KEY'] = 'YOUR_API_KEY'

def extract_semantic_memory(dialogue_text: str):
    """
    使用 langextract 从一段对话文本中提取结构化的语义记忆。
    """
    # 1. 定义清晰的提示
    prompt = textwrap.dedent("""\
        你是一个分析学习对话的专家。请从以下用户与AI的对话中，提取出结构化的语义记忆。
        需要提取三种信息：'dialogue_turn'（对话回合）、'key_concept'（关键概念）和 'user_sentiment'（用户情绪）。
        - 对于 'dialogue_turn'，请识别发言者(speaker)、意图(intent)和主题(topic)。
        - 对于 'key_concept'，请识别其类别(category)和相关性(relevance)。
        - 对于 'user_sentiment'，请识别情绪类型(sentiment)和强度(intensity)。
        请确保 extraction_text 字段是原文的精确拷贝，不要修改或转述。
    """)

    # 2. 提供高质量的 few-shot 示例
    examples = [
        lx.data.ExampleData(
            text="用户: 我想学习Python编程，特别是数据分析方面。",
            extractions=[
                lx.data.Extraction(
                    extraction_class="dialogue_turn",
                    extraction_text="用户: 我想学习Python编程，特别是数据分析方面。",
                    attributes={ "speaker": "user", "intent": "学习编程", "topic": "Python编程", "sub_topic": "数据分析" }
                ),
                lx.data.Extraction(
                    extraction_class="key_concept", extraction_text="Python编程",
                    attributes={ "category": "编程语言", "relevance": "high" }
                ),
                lx.data.Extraction(
                    extraction_class="user_sentiment", extraction_text="我想学习",
                    attributes={ "sentiment": "positive", "intensity": "medium" }
                )
            ]
        ),
        lx.data.ExampleData(
            text="AI: 好的，Python数据分析通常会用到Pandas和NumPy库。你对哪个更感兴趣？",
            extractions=[
                lx.data.Extraction(
                    extraction_class="dialogue_turn",
                    extraction_text="AI: 好的，Python数据分析通常会用到Pandas和NumPy库。你对哪个更感兴趣？",
                    attributes={ "speaker": "ai", "intent": "提问", "topic": "Python数据分析" }
                ),
                lx.data.Extraction(
                    extraction_class="key_concept", extraction_text="Pandas",
                    attributes={ "category": "Python库", "relevance": "high" }
                ),
                 lx.data.Extraction(
                    extraction_class="key_concept", extraction_text="NumPy",
                    attributes={ "category": "Python库", "relevance": "high" }
                )
            ]
        )
    ]

    # 3. 运行提取
    print("正在调用 langextract 进行提取...")
    result_document = lx.extract(
        text_or_documents=dialogue_text,
        prompt_description=prompt,
        examples=examples,
        model_id="gemini-2.5-flash",
    )
    
    print("提取完成。")
    return result_document

# --- 主程序 ---
if __name__ == '__main__':
    # 准备待处理的对话样本
    input_dialogue_turn = """
用户: 我在学习机器学习，但是对神经网络的原理有些困惑。
AI: 神经网络是机器学习的一个重要分支，它模仿人脑的结构和功能。你具体对哪一部分感到困惑呢？是前向传播、反向传播还是激活函数？
"""
    
    annotated_doc = extract_semantic_memory(input_dialogue_turn)

    # 5. 处理和打印结果
    if annotated_doc and annotated_doc.extractions:
        print("\n--- 提取结果 ---")
        for extraction in annotated_doc.extractions:
            print(f"  类别: {extraction.extraction_class}")
            print(f"  文本: '{extraction.extraction_text}'")
            if extraction.attributes:
                print(f"  属性: {extraction.attributes}")
            if extraction.char_interval:
                print(f"  位置 (字符): start={extraction.char_interval.start_pos}, end={extraction.char_interval.end_pos}")
            print("-" * 20)
        
        # 可选：保存结果用于可视化调试
        # lx.io.save_annotated_documents([annotated_doc], "semantic_memory_results.jsonl")
        # html_content = lx.visualize("semantic_memory_results.jsonl")
        # with open("semantic_memory_visualization.html", "w", encoding='utf-8') as f:
        #     f.write(html_content)
        # print("\n已生成可视化文件: semantic_memory_visualization.html")

    else:
        print("未提取到任何信息。")
```

## 5. 数据持久化方案

为了支持前端的点击高亮和滚动定位功能，我们需要在数据库中精确存储 `source grounding` 信息。`deepwiki` 建议的表结构非常合理，我们将其正式确定下来。

**新增数据表: `semantic_extractions`**

| 字段名 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `UUID` | 主键 |
| `conversation_turn_id` | `UUID` | 外键，关联到存储原始对话回合的表 |
| `extraction_class` | `VARCHAR(255)` | 提取类别, e.g., 'dialogue_turn' |
| `extraction_text` | `TEXT` | 提取出的原文片段 |
| `char_start_pos` | `INTEGER` | 在 `conversation_turn` 文本中的起始字符位置 |
| `char_end_pos` | `INTEGER` | 在 `conversation_turn` 文本中的结束字符位置 |
| `attributes` | `JSONB` | 存储提取出的结构化属性 |
| `created_at` | `TIMESTAMP` | 创建时间 |

**存储流程**:

1.  `SemanticAgent` 收到 `AgentCoordinator` 分发的对话回合文本。
2.  调用 `extract_semantic_memory` 函数，获得 `AnnotatedDocument` 结果。
3.  遍历 `annotated_doc.extractions` 列表。
4.  对于每一个 `extraction` 对象，将其信息（`extraction_class`, `extraction_text`, `char_interval`, `attributes`）组装成一条记录，存入 `semantic_extractions` 表中。

## 6. 前端集成要点

1.  **API 变更**: 后端需要提供一个新的 API 接口，该接口能根据 `conversation_turn_id` 返回与之关联的所有 `semantic_extractions` 记录。
2.  **前端逻辑**:
    *   当用户点击右侧摘要区的某个摘要项时，前端获取该摘要项对应的 `extraction` 记录。
    *   从记录中读取 `char_start_pos` 和 `char_end_pos`。
    *   使用 JavaScript 定位到左侧原始对话区域，并利用 `substring` 或类似方法高亮从 `start` 到 `end` 的文本。
    *   调用元素的 `.scrollIntoView()` 方法，将高亮区域平滑滚动到用户视口。

---

这份文档为接下来的开发工作提供了清晰、可执行的步骤。请审阅，如果确认无误，我们就可以正式开始编码了。