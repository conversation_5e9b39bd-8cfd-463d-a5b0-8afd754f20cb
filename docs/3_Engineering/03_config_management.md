# 配置管理策略

## 概述

Master Know 项目采用混合配置注入方案，在不同环境中使用不同的配置管理策略：
- **开发环境**: 使用 `.env` 文件进行配置管理
- **生产环境**: 通过云平台环境变量注入配置

## 开发环境配置

### 1. 环境变量文件 (.env)

项目根目录的 `.env` 文件包含所有开发环境的配置：

```bash
# 域名配置
DOMAIN=localhost
FRONTEND_HOST=http://localhost:5173

# 项目信息
PROJECT_NAME=Master-Know
STACK_NAME=fastapi
ENVIRONMENT=local

# 后端配置
BACKEND_CORS_ORIGINS="http://localhost,http://localhost:5173,https://localhost,https://localhost:5173"
SECRET_KEY=9vsQkIoe_aFF4r7IaidHb0WDhV275nUOKXMjUPHG8YY

# 数据库配置
POSTGRES_SERVER=db
POSTGRES_PORT=5432
POSTGRES_DB=app
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres_secure_password_2024_xyz

# 用户配置
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=changethis

# Docker 镜像配置
DOCKER_IMAGE_BACKEND=backend
DOCKER_IMAGE_FRONTEND=frontend
```

### 2. Docker Compose 配置

#### 主配置文件 (docker-compose.yml)
- 定义生产环境的服务配置
- 使用 Traefik 进行反向代理和 HTTPS 终止
- 配置服务间的网络和依赖关系

#### 开发覆盖配置 (docker-compose.override.yml)
- 覆盖生产配置以适应本地开发
- 暴露服务端口到主机
- 启用热重载和开发模式
- 配置本地邮件捕获服务

### 3. 服务端口映射

开发环境中的服务端口：
- **前端**: http://localhost:5173
- **后端 API**: http://localhost:9000
- **数据库**: localhost:5432
- **Adminer**: http://localhost:8080
- **Mailcatcher**: http://localhost:1080
- **Traefik Dashboard**: http://localhost:8090

## 生产环境配置

### 1. 环境变量注入

生产环境通过云平台（如 AWS、Azure、GCP）的环境变量服务注入配置：

#### 必需的环境变量：
```bash
# 域名和安全
DOMAIN=your-production-domain.com
SECRET_KEY=your-production-secret-key
ENVIRONMENT=production

# 数据库
POSTGRES_SERVER=your-db-host
POSTGRES_PORT=5432
POSTGRES_DB=master_know_prod
POSTGRES_USER=master_know_user
POSTGRES_PASSWORD=your-secure-db-password

# 用户管理
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=your-secure-admin-password

# SMTP 配置
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_TLS=true
SMTP_USER=your-smtp-user
SMTP_PASSWORD=your-smtp-password
EMAILS_FROM_EMAIL=<EMAIL>

# 前端配置
FRONTEND_HOST=https://dashboard.your-domain.com
BACKEND_CORS_ORIGINS="https://dashboard.your-domain.com,https://api.your-domain.com"
```

### 2. 安全最佳实践

#### 密钥管理：
- 使用云平台的密钥管理服务（如 AWS Secrets Manager）
- 定期轮换密钥和密码
- 使用强随机密钥生成器

#### 数据库安全：
- 使用专用数据库用户，限制权限
- 启用数据库连接加密
- 配置数据库防火墙规则

#### 网络安全：
- 使用 HTTPS 终止
- 配置适当的 CORS 策略
- 实施速率限制

## 配置验证

### 1. 开发环境验证

验证开发环境配置是否正确：

```bash
# 检查所有服务状态
docker compose ps

# 测试后端 API
curl http://localhost:8000/api/v1/utils/health-check

# 测试前端
curl -I http://localhost:5173

# 测试数据库连接
docker compose exec backend python -c "from app.core.db import engine; print('DB connection OK')"
```

### 2. 生产环境验证

生产环境部署前的检查清单：

- [ ] 所有必需环境变量已设置
- [ ] 数据库连接测试通过
- [ ] SMTP 配置测试通过
- [ ] HTTPS 证书配置正确
- [ ] 域名 DNS 解析正确
- [ ] 防火墙规则配置正确

## 配置模板

### 开发环境 .env 模板

```bash
# 复制此模板到 .env 文件并填入实际值
DOMAIN=localhost
PROJECT_NAME=Master-Know
STACK_NAME=fastapi
ENVIRONMENT=local

# 生成新的密钥: python -c "import secrets; print(secrets.token_urlsafe(32))"
SECRET_KEY=your-secret-key-here

# 数据库配置
POSTGRES_PASSWORD=your-db-password-here

# 管理员用户
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=your-admin-password-here
```

### 生产环境变量清单

```bash
# 核心配置
DOMAIN=
SECRET_KEY=
ENVIRONMENT=production

# 数据库
POSTGRES_SERVER=
POSTGRES_DB=
POSTGRES_USER=
POSTGRES_PASSWORD=

# 用户管理
FIRST_SUPERUSER=
FIRST_SUPERUSER_PASSWORD=

# 邮件服务
SMTP_HOST=
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=

# 前端
FRONTEND_HOST=
BACKEND_CORS_ORIGINS=
```

## 故障排除

### 常见配置问题

1. **数据库连接失败**
   - 检查 POSTGRES_* 环境变量
   - 确认数据库服务正在运行
   - 验证网络连接

2. **CORS 错误**
   - 检查 BACKEND_CORS_ORIGINS 配置
   - 确认前端 URL 包含在允许列表中

3. **邮件发送失败**
   - 验证 SMTP 配置
   - 检查邮件服务提供商设置

4. **Traefik 路由问题**
   - 检查域名配置
   - 验证 Traefik 标签设置
   - 查看 Traefik 日志

### 调试命令

```bash
# 查看环境变量
docker compose config

# 查看服务日志
docker compose logs backend
docker compose logs frontend
docker compose logs proxy

# 进入容器调试
docker compose exec backend bash
docker compose exec frontend sh
```

## 配置更新流程

### 开发环境
1. 修改 `.env` 文件
2. 重启相关服务：`docker compose restart <service>`
3. 验证配置生效

### 生产环境
1. 更新云平台环境变量
2. 重新部署应用
3. 验证配置生效
4. 监控应用状态

## 安全注意事项

- **永远不要**将 `.env` 文件提交到版本控制
- 使用 `.env.example` 作为配置模板
- 定期审查和更新密钥
- 监控配置变更日志
- 实施最小权限原则
