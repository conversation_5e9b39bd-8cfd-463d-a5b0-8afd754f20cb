# 集成测试指南

本文档说明如何测试阶段一任务4和5的集成：Manticore Search 和 Dramatiq。

## 🎯 Topic Service 集成测试完成状态

**完成时间**: 2025-08-14
**状态**: ✅ 完成并验证

### Topic Service POC 验证结果
- **POC 位置**: `demo/topic_poc/`
- **测试结果**: 8/8 通过 (100% 成功率)
- **功能验证**: 主题 CRUD、文档关联、Manticore 集成
- **技术栈验证**: FastAPI + SQLite + Redis + Manticore

### Topic Service 完整实现验证结果
- **服务位置**: `services/topic/`
- **API 测试**: 7/7 通过 (100% 成功率)
- **集成测试**: 10/10 通过 (100% 成功率)
- **性能测试**: 并发和连续请求正常
- **服务地址**: http://localhost:9004
- **API 文档**: http://localhost:9004/docs

### 已验证功能
- ✅ 主题 CRUD API 端点
- ✅ 文档关联功能
- ✅ Manticore Search 集成
- ✅ 健康检查和监控
- ✅ API 文档自动生成
- ✅ 错误处理和 CORS 配置
- ✅ 性能和并发测试

## 概述

已完成的集成包括：

### 任务4: Manticore Search 集成
- ✅ 创建了 Manticore 配置文件 (`manticore/manticore.conf`)
- ✅ 修复了 docker-compose.yml 中的 Manticore 配置
- ✅ 添加了 Manticore Python 客户端依赖 (`manticoresearch`)
- ✅ 创建了异步 Manticore 客户端 (`backend/app/manticore_client.py`)
- ✅ 创建了测试脚本 (`backend/app/test_manticore.py`)

### 任务5: Dramatiq 集成
- ✅ 添加了 Dramatiq 依赖 (`dramatiq[redis]`)
- ✅ 创建了 Dramatiq 配置 (`backend/app/dramatiq_config.py`)
- ✅ 创建了示例异步任务 (`backend/app/tasks.py`)
- ✅ 添加了 Dramatiq Worker 服务到 docker-compose.yml
- ✅ 创建了测试脚本 (`backend/app/test_dramatiq.py`)

### 额外功能
- ✅ 创建了集成测试 API 端点 (`backend/app/api/routes/integration_test.py`)

## 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │ Dramatiq Worker │
│   (React)       │    │   (FastAPI)     │    │   (Python)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │   Manticore     │
│   (Database)    │    │   (Broker)      │    │   (Search)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 启动服务

1. 确保所有服务都在运行：
```bash
docker compose up -d
```

2. 检查服务状态：
```bash
docker compose ps
```

应该看到以下服务正在运行：
- `db` (PostgreSQL)
- `redis` (Redis)
- `manticore` (Manticore Search)
- `backend` (FastAPI 后端)
- `dramatiq-worker` (Dramatiq Worker)
- `frontend` (React 前端)

## 测试方法

### 方法1: 使用测试脚本

#### 测试 Manticore Search

```bash
# 进入后端容器
docker compose exec backend bash

# 运行 Manticore 连接测试
cd /app
python test_manticore.py --connection-only

# 运行完整的 Manticore 测试
python test_manticore.py
```

#### 测试 Dramatiq

```bash
# 在后端容器中
python test_dramatiq.py --connection-only

# 测试任务入队（需要 worker 运行）
python test_dramatiq.py --worker-test

# 运行完整测试
python test_dramatiq.py
```

### 方法2: 使用 API 端点

如果环境设置为 `local`，可以通过以下 API 端点测试集成：

#### Manticore Search API

```bash
# 健康检查
curl http://localhost:8000/api/v1/integration/manticore/health

# 创建测试表
curl -X POST http://localhost:8000/api/v1/integration/manticore/create-table/test_table

# 添加文档
curl -X POST http://localhost:8000/api/v1/integration/manticore/document \
  -H "Content-Type: application/json" \
  -d '{
    "table_name": "test_table",
    "document": {
      "id": 1,
      "title": "Test Document",
      "content": "This is a test document",
      "category_id": 1,
      "created_at": **********
    }
  }'

# 搜索文档
curl -X POST http://localhost:8000/api/v1/integration/manticore/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "test document",
    "table_name": "test_table",
    "limit": 10
  }'
```

#### Dramatiq API

```bash
# Hello World 任务
curl -X POST "http://localhost:8000/api/v1/integration/dramatiq/hello?name=TestUser"

# 慢任务
curl -X POST "http://localhost:8000/api/v1/integration/dramatiq/slow-task?duration=3"

# 重要任务
curl -X POST http://localhost:8000/api/v1/integration/dramatiq/important-task \
  -H "Content-Type: application/json" \
  -d '{"test": "data", "items": [1, 2, 3]}'

# 文档索引任务（结合 Manticore + Dramatiq）
curl -X POST http://localhost:8000/api/v1/integration/dramatiq/index-document \
  -H "Content-Type: application/json" \
  -d '{
    "table_name": "test_table",
    "document": {
      "id": 2,
      "title": "Async Indexed Document",
      "content": "This document was indexed via Dramatiq task",
      "category_id": 2,
      "created_at": 1640995300
    }
  }'
```

#### 完整集成测试

```bash
# 测试所有组件
curl http://localhost:8000/api/v1/integration/test/full-integration
```

### 方法3: 查看日志

监控各个服务的日志来验证功能：

```bash
# 查看后端日志
docker compose logs -f backend

# 查看 Dramatiq Worker 日志
docker compose logs -f dramatiq-worker

# 查看 Manticore 日志
docker compose logs -f manticore

# 查看 Redis 日志
docker compose logs -f redis
```

## 预期结果

### Manticore Search
- ✅ 健康检查返回成功
- ✅ 可以创建表
- ✅ 可以插入文档
- ✅ 可以搜索文档
- ✅ 可以执行 SQL 查询

### Dramatiq
- ✅ 可以连接到 Redis broker
- ✅ 可以入队任务
- ✅ Worker 可以处理任务
- ✅ 任务执行结果正确
- ✅ 失败任务会重试

### 集成功能
- ✅ Dramatiq 任务可以使用 Manticore 客户端
- ✅ 可以通过异步任务索引文档到 Manticore
- ✅ API 端点正常工作
- ✅ 所有服务可以相互通信

## 故障排除

### Manticore 连接问题
1. 检查 Manticore 服务是否运行：`docker compose ps manticore`
2. 检查端口是否正确暴露：9306 (MySQL), 9308 (HTTP)
3. 查看 Manticore 日志：`docker compose logs manticore`

### Dramatiq 任务不执行
1. 检查 Redis 服务：`docker compose ps redis`
2. 检查 Worker 服务：`docker compose ps dramatiq-worker`
3. 查看 Worker 日志：`docker compose logs dramatiq-worker`
4. 确认任务已入队：检查 Redis 中的队列

### API 端点不可用
1. 确认环境变量 `ENVIRONMENT=local`
2. 检查后端服务状态
3. 查看后端日志中的错误信息

## 下一步

集成测试成功后，可以：

1. 在实际业务逻辑中使用这些集成
2. 添加更多复杂的搜索功能
3. 实现更多类型的后台任务
4. 添加监控和指标收集
5. 优化性能和配置

## 文件结构

```
├── manticore/
│   └── manticore.conf              # Manticore 配置文件
├── backend/app/
│   ├── manticore_client.py         # Manticore 异步客户端
│   ├── dramatiq_config.py          # Dramatiq 配置
│   ├── tasks.py                    # 异步任务定义
│   ├── test_manticore.py           # Manticore 测试脚本
│   ├── test_dramatiq.py            # Dramatiq 测试脚本
│   └── api/routes/
│       └── integration_test.py     # 集成测试 API 端点
├── docker-compose.yml              # 更新的服务配置
└── INTEGRATION_TESTING.md          # 本文档
```
