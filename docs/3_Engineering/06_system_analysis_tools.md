# 系统分析工具使用指南

## 工具概述

本文档介绍Master-Know项目中开发的系统分析工具，这些工具专门用于防止AI误解系统状态，提供准确的系统分析结果。

## 工具清单

### 1. correct_system_discovery.py
**用途：** 标准化的系统发现脚本
**功能：** 正确识别架构和端点

### 2. ai_misunderstanding_prevention_toolkit.py
**用途：** 完整的分析工具包
**功能：** 生成详细的系统分析报告

### 3. prevent_ai_misunderstanding.py
**用途：** 误解因素分析
**功能：** 分析导致误解的原因

### 4. final_system_verification.py
**用途：** 最终系统验证
**功能：** 全面验证系统功能

## 详细使用指南

### correct_system_discovery.py

**运行方式：**
```bash
python correct_system_discovery.py
```

**输出示例：**
```
🔍 正确的系统发现和测试方法
============================================================
🏗️  发现系统架构...
   ✅ 检测到: 单体Backend + Gateway BFF架构

🔌 发现API端点...
   ✅ 从 http://localhost:8000/openapi.json 获取到 7 个端点

🔐 发现认证端点...
   ✅ 登录端点存在: /api/v1/login/access-token
   ✅ 注册端点存在: /api/v1/users/signup

🧪 使用正确端点测试...
   ✅ 认证成功，获取到token
   ✅ /api/v1/users/me: 端点存在
```

**关键功能：**
- 自动检测容器架构
- 发现认证端点
- 验证API可用性
- 测试认证流程

### ai_misunderstanding_prevention_toolkit.py

**运行方式：**
```bash
python ai_misunderstanding_prevention_toolkit.py
```

**输出示例：**
```
🔍 AI误解防护工具包
================================================================================
🐳 分析Docker容器...
   ✅ 检测到架构: monolith_with_bff
   📊 运行容器数: 11

🔌 发现API端点...
   ✅ Gateway: 7 个端点
   ✅ Backend Docs: 文档可访问

🔐 测试认证系统...
   ✅ Backend: 登录成功
   ✅ Gateway: 登录成功

📊 生成分析报告...
   ✅ 报告已保存: system_analysis_report_*.json
```

**生成文件：**
- `system_analysis_report_*.json` - 详细的JSON格式分析报告

**报告内容：**
```json
{
  "timestamp": "2025-08-18T16:07:39.821949",
  "analysis_results": {
    "containers": [...],
    "api_endpoints": {...},
    "authentication": {...},
    "functionalities": {...}
  },
  "summary": {
    "architecture": "monolith_with_bff",
    "container_count": 11,
    "api_services": 3,
    "auth_working": true,
    "functionalities_working": 4
  }
}
```

### prevent_ai_misunderstanding.py

**运行方式：**
```bash
python prevent_ai_misunderstanding.py
```

**功能：**
- 检查项目文档中的误导信息
- 分析配置文件中的问题
- 创建正确的发现方法
- 生成AI指导检查清单

**生成文件：**
- `AI_ANALYSIS_CHECKLIST.md` - 分析指导清单

### final_system_verification.py

**运行方式：**
```bash
uv run --project backend python final_system_verification.py
```

**功能：**
- 全面的系统功能验证
- 性能测试
- 集成测试
- 生成验证报告

## 工具使用流程

### 标准分析流程

```bash
# 1. 运行标准化发现
python correct_system_discovery.py

# 2. 运行完整分析工具包
python ai_misunderstanding_prevention_toolkit.py

# 3. 查看生成的分析报告
cat system_analysis_report_*.json | jq '.'

# 4. 运行最终验证（如果需要）
uv run --project backend python final_system_verification.py
```

### 问题诊断流程

```bash
# 1. 分析误解因素
python prevent_ai_misunderstanding.py

# 2. 检查生成的指导文档
cat AI_ANALYSIS_CHECKLIST.md

# 3. 运行标准化分析
python correct_system_discovery.py

# 4. 对比结果，确认问题
```

## 输出文件说明

### JSON分析报告

**文件名格式：** `system_analysis_report_[timestamp].json`

**主要字段：**
- `timestamp` - 分析时间
- `analysis_results` - 详细分析结果
  - `containers` - 容器信息
  - `api_endpoints` - API端点信息
  - `authentication` - 认证测试结果
  - `functionalities` - 功能测试结果
- `summary` - 分析摘要
- `recommendations` - 改进建议

### Markdown指导文档

**AI_ANALYSIS_CHECKLIST.md** - AI分析检查清单
**AI_ANALYSIS_QUICK_REFERENCE.md** - 快速参考指南

## 集成到开发流程

### 开发前检查

```bash
# 在开始新功能开发前
python correct_system_discovery.py > system_baseline.txt
```

### 测试前验证

```bash
# 在运行测试套件前
python ai_misunderstanding_prevention_toolkit.py
```

### 部署前确认

```bash
# 在部署到生产环境前
uv run --project backend python final_system_verification.py
```

## 自定义和扩展

### 添加新的检查项

在 `ai_misunderstanding_prevention_toolkit.py` 中添加新的分析方法：

```python
def test_new_functionality(self):
    """测试新功能"""
    print("\n🧪 测试新功能...")
    
    # 实现具体的测试逻辑
    results = {}
    
    # 返回测试结果
    return results
```

### 自定义报告格式

修改 `generate_analysis_report` 方法来自定义报告格式：

```python
def generate_analysis_report(self):
    """生成自定义分析报告"""
    # 自定义报告结构
    custom_report = {
        'project': 'master-know',
        'version': '1.0',
        # ... 其他字段
    }
    
    return custom_report
```

## 故障排除

### 常见问题

**1. 工具无法连接到服务**
```bash
# 检查服务状态
docker ps
curl http://localhost:9000/health
curl http://localhost:8000/health
```

**2. 认证失败**
```bash
# 检查默认用户凭据
curl -X POST http://localhost:9000/api/v1/login/access-token \
  -d "username=<EMAIL>&password=changethis"
```

**3. 权限错误**
```bash
# 确保脚本有执行权限
chmod +x *.py

# 检查Python环境
which python
python --version
```

### 调试模式

在脚本中添加调试输出：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 在关键位置添加日志
logging.debug(f"Testing endpoint: {url}")
logging.debug(f"Response: {response.status_code}")
```

## 最佳实践

### 1. 定期运行分析

建议每周运行一次完整分析，确保系统状态的准确记录。

### 2. 保存历史报告

保留历史分析报告，用于对比系统变化：

```bash
mkdir -p analysis_history
mv system_analysis_report_*.json analysis_history/
```

### 3. 团队共享

将分析结果共享给团队成员：

```bash
# 生成简化报告
jq '.summary' system_analysis_report_*.json > team_summary.json
```

### 4. 自动化集成

将工具集成到CI/CD流程中：

```yaml
# .github/workflows/system-analysis.yml
- name: Run System Analysis
  run: |
    python ai_misunderstanding_prevention_toolkit.py
    cat system_analysis_report_*.json | jq '.summary'
```

通过正确使用这些工具，可以确保系统分析的准确性，避免基于错误信息的开发决策。
