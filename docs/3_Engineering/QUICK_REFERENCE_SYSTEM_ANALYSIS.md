# 系统分析快速参考卡片

## 🚀 快速开始

```bash
# 标准化系统发现
python correct_system_discovery.py

# 完整分析工具包
python ai_misunderstanding_prevention_toolkit.py

# 查看分析报告
cat system_analysis_report_*.json | jq '.summary'
```

## ⚠️ 常见误区速查

| 误区 | 表现 | 正确做法 |
|------|------|----------|
| 路径假设 | 假设 `/auth/signup` | 查看 OpenAPI 规范 |
| 架构误判 | 看配置认为是微服务 | 检查实际容器 `docker ps` |
| 状态码误解 | 404=不存在, 403=错误 | 404=路径错, 403=权限不足 |
| 测试不当 | 不认证就测试 | 先获取 token 再测试 |
| 信息过时 | 相信文档注释 | 以实际运行状态为准 |

## 🔧 必备命令

### 系统架构检查
```bash
# 检查容器
docker ps --format "table {{.Names}}\t{{.Status}}"

# 分析架构模式
# gateway + backend = BFF架构
# user-service + document-service = 微服务架构
```

### API端点发现
```bash
# 获取API规范（最权威）
curl http://localhost:9000/openapi.json | jq '.paths | keys'

# 检查端点数量
curl http://localhost:9000/openapi.json | jq '.paths | keys | length'
```

### 认证流程
```bash
# 获取token
TOKEN=$(curl -s -X POST http://localhost:9000/api/v1/login/access-token \
  -d "username=<EMAIL>&password=changethis" | jq -r '.access_token')

# 测试认证
curl -H "Authorization: Bearer $TOKEN" http://localhost:9000/api/v1/users/me
```

### 功能验证
```bash
# 关键端点测试
endpoints=("/api/v1/users/me" "/api/v1/users/" "/api/v1/documents/" "/api/v1/items/")
for ep in "${endpoints[@]}"; do
  echo "Testing $ep"
  curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:9000$ep" | jq '.data // .detail // .'
done
```

## 📊 状态码速查

| 状态码 | 含义 | 处理方式 |
|--------|------|----------|
| 200 | 成功 | ✅ 功能正常 |
| 401 | 未认证 | 🔑 需要获取token |
| 403 | 权限不足 | 🔒 端点存在但需要权限 |
| 404 | 未找到 | 🔍 检查端点路径 |
| 422 | 参数错误 | 📝 检查请求格式 |
| 500 | 服务器错误 | 🚨 检查服务状态 |

## 🎯 分析原则

### 优先级顺序
1. **实际运行状态** > 配置文件 > 文档注释
2. **OpenAPI规范** > 手动测试 > 假设推测
3. **完整认证流程** > 单独端点测试

### 验证检查点
- ✅ 容器状态：`docker ps`
- ✅ API规范：`/openapi.json`
- ✅ 认证流程：登录 → 获取token → 测试
- ✅ 功能验证：端到端测试

## 🛠️ 工具速查

| 工具 | 用途 | 命令 |
|------|------|------|
| 标准发现 | 快速系统检查 | `python correct_system_discovery.py` |
| 完整分析 | 详细报告生成 | `python ai_misunderstanding_prevention_toolkit.py` |
| 误解分析 | 问题根因分析 | `python prevent_ai_misunderstanding.py` |
| 最终验证 | 全面功能测试 | `uv run --project backend python final_system_verification.py` |

## 📋 检查清单

### 开发前
- [ ] 运行系统发现脚本
- [ ] 确认架构类型
- [ ] 验证API端点
- [ ] 测试认证流程

### 测试前
- [ ] 检查服务状态
- [ ] 验证数据库连接
- [ ] 确认配置正确
- [ ] 运行完整分析

### 部署前
- [ ] 全面功能验证
- [ ] 性能测试
- [ ] 安全检查
- [ ] 生成分析报告

## 🚨 紧急诊断

### 服务无响应
```bash
# 1. 检查容器状态
docker ps
docker logs master-know-backend-1
docker logs master-know-gateway-1

# 2. 检查端口占用
netstat -tulpn | grep :9000
netstat -tulpn | grep :8000

# 3. 重启服务
docker-compose restart backend gateway
```

### 认证失败
```bash
# 1. 检查默认用户
curl -X POST http://localhost:9000/api/v1/login/access-token \
  -d "username=<EMAIL>&password=changethis" -v

# 2. 检查数据库
docker exec -it master-know-db-1 psql -U postgres -d app -c "SELECT email FROM user WHERE is_superuser=true;"

# 3. 重置用户（如果需要）
docker-compose exec backend python -c "from app.initial_data import main; main()"
```

### API端点404
```bash
# 1. 确认端点路径
curl http://localhost:9000/openapi.json | jq '.paths | keys | .[]' | grep -i signup

# 2. 检查路由注册
docker logs master-know-backend-1 | grep -i "route"

# 3. 验证服务健康
curl http://localhost:9000/api/v1/utils/health-check/
```

## 📈 性能基准

| 指标 | 正常范围 | 检查命令 |
|------|----------|----------|
| 响应时间 | < 100ms | `time curl http://localhost:9000/health` |
| 内存使用 | < 512MB | `docker stats --no-stream` |
| CPU使用 | < 50% | `docker stats --no-stream` |
| 数据库连接 | < 10个 | 检查数据库连接池 |

## 🔗 相关文档

- [AI系统分析指导手册](./05_ai_system_analysis_guide.md)
- [系统分析工具使用指南](./06_system_analysis_tools.md)
- [集成测试指南](./04_integration_testing_guide.md)
- [部署指南](./02_deployment_guide.md)

---

**💡 记住：始终以实际运行状态为准，不要相信假设！**
