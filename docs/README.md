# 项目文档中心

欢迎来到“知深学习导师”项目的文档中心。这里的文档被精心组织，以帮助您快速理解项目的各个方面。

## 文档结构

所有文档都存放在 `docs/` 目录下，并按照其性质分为三个主要类别：

- **[1_Product](./1_Product/)**: 关于“我们做什么”和“为什么做”。这里包含了项目的愿景、目标用户和详细的产品需求。
- **[2_Architecture](./2_Architecture/)**: 关于“我们如何构建”。这里包含了系统的高层设计、数据模型和未来的架构演进计划。
- **[3_Engineering](./3_Engineering/)**: 关于“开发者如何参与”。这里包含了开发环境搭建、部署流程和配置管理的具体指南。

## 建议阅读顺序

- 如果您想快速了解**项目概况**，请从 `1_Product/01_brief.md` 开始。
- 如果您是**产品经理或设计师**，请重点阅读 `1_Product/` 目录下的所有文档。
- 如果您是**架构师或后端开发者**，`2_Architecture/` 和 `3_Engineering/` 目录是您的主要参考。
- 如果您是**新加入的开发者**，建议按照 `1_Product` -> `2_Architecture` -> `3_Engineering` 的顺序阅读，以建立对项目的全面理解。