"""
Text-Splitter Engine 配置管理
"""

from pydantic import BaseModel, Field
from typing import Dict, Any, Optional

class TextSplitterConfig(BaseModel):
    """Text-Splitter Engine 配置类"""
    
    # 默认配置
    default_model: str = Field(default="gpt-3.5-turbo", description="默认的 tokenizer 模型")
    default_max_tokens: int = Field(default=1000, description="默认最大 token 数")
    default_max_chars: int = Field(default=2000, description="默认最大字符数")
    
    # Markdown 特定配置
    markdown_max_chars: int = Field(default=1500, description="Markdown 文档最大字符数")
    markdown_preserve_headers: bool = Field(default=True, description="是否保留 Markdown 标题结构")
    
    # 代码文档配置
    code_max_chars: int = Field(default=1200, description="代码文档最大字符数")
    code_preserve_functions: bool = Field(default=True, description="是否保留函数完整性")
    
    # 批量处理配置
    batch_size: int = Field(default=10, description="批量处理的文档数量")
    max_concurrent: int = Field(default=5, description="最大并发处理数")
    
    # 性能配置
    enable_caching: bool = Field(default=True, description="是否启用缓存")
    cache_ttl: int = Field(default=3600, description="缓存过期时间（秒）")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    enable_metrics: bool = Field(default=True, description="是否启用性能指标")
    
    model_config = {
        "env_prefix": "TEXT_SPLITTER_",
        "case_sensitive": False
    }
        
    def model_dump(self, **kwargs) -> Dict[str, Any]:
        """转换为字典格式"""
        return super().model_dump(**kwargs)
    
    @classmethod
    def from_env(cls) -> "TextSplitterConfig":
        """从环境变量创建配置"""
        return cls()
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "TextSplitterConfig":
        """从字典创建配置"""
        return cls(**config_dict)
    
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        if self.default_max_tokens <= 0:
            raise ValueError("default_max_tokens must be positive")
        
        if self.default_max_chars <= 0:
            raise ValueError("default_max_chars must be positive")
        
        if self.batch_size <= 0:
            raise ValueError("batch_size must be positive")
        
        return True
    
    def get_strategy_config(self, strategy_name: str) -> Dict[str, Any]:
        """获取特定策略的配置"""
        strategy_configs = {
            "token_based": {
                "max_tokens": self.default_max_tokens,
                "model": self.default_model
            },
            "character_based": {
                "max_chars": self.default_max_chars
            },
            "markdown": {
                "max_chars": self.markdown_max_chars,
                "preserve_headers": self.markdown_preserve_headers
            },
            "code": {
                "max_chars": self.code_max_chars,
                "preserve_functions": self.code_preserve_functions
            }
        }
        
        return strategy_configs.get(strategy_name, {})
