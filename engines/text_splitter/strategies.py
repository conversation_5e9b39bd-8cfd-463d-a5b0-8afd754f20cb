"""
分割策略定义
"""

from abc import ABC, abstractmethod
from typing import Any, Dict
from pydantic import BaseModel

class SplitStrategy(BaseModel, ABC):
    """分割策略基类"""
    name: str
    
    @abstractmethod
    def get_params(self) -> Dict[str, Any]:
        """获取策略参数"""
        pass

class TokenBasedStrategy(SplitStrategy):
    """基于 Token 的分割策略"""
    name: str = "token_based"
    max_tokens: int = 1000
    model_name: str = "gpt-3.5-turbo"

    class Config:
        protected_namespaces = ()
    
    def get_params(self) -> Dict[str, Any]:
        return {
            "max_tokens": self.max_tokens,
            "model_name": self.model_name
        }

class CharacterBasedStrategy(SplitStrategy):
    """基于字符的分割策略"""
    name: str = "character_based"
    max_chars: int = 2000
    
    def get_params(self) -> Dict[str, Any]:
        return {
            "max_chars": self.max_chars
        }

class MarkdownStrategy(SplitStrategy):
    """Markdown 文档分割策略"""
    name: str = "markdown"
    max_chars: int = 1500
    preserve_headers: bool = True
    
    def get_params(self) -> Dict[str, Any]:
        return {
            "max_chars": self.max_chars,
            "preserve_headers": self.preserve_headers
        }
