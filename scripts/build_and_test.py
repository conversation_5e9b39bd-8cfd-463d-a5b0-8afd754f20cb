#!/usr/bin/env python3
"""
构建和测试脚本
用于编译、测试和运行模块化架构
"""

import os
import sys
import subprocess
from pathlib import Path
import time

def run_command(cmd: str, cwd: str = None, check: bool = True) -> subprocess.CompletedProcess:
    """运行命令并返回结果"""
    print(f"🔧 执行命令: {cmd}")
    if cwd:
        print(f"📁 工作目录: {cwd}")
    
    result = subprocess.run(
        cmd, 
        shell=True, 
        cwd=cwd, 
        capture_output=True, 
        text=True,
        check=False
    )
    
    if result.stdout:
        print(f"✅ 输出:\n{result.stdout}")
    
    if result.stderr:
        print(f"⚠️ 错误:\n{result.stderr}")
    
    if check and result.returncode != 0:
        print(f"❌ 命令执行失败，退出码: {result.returncode}")
        sys.exit(1)
    
    return result

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")

    # 检查 uv 是否安装
    try:
        result = run_command("uv --version", check=False)
        if result.returncode == 0:
            print(f"✅ uv 已安装: {result.stdout.strip()}")
        else:
            print("❌ uv 未安装，请先安装 uv")
            sys.exit(1)
    except FileNotFoundError:
        print("❌ uv 未安装，请先安装 uv")
        sys.exit(1)

    # 检查 backend 目录中的 Python 版本
    backend_dir = Path("backend")
    if backend_dir.exists():
        result = run_command("uv run python --version", cwd=str(backend_dir), check=False)
        if result.returncode == 0:
            print(f"✅ Backend Python 版本: {result.stdout.strip()}")
        else:
            print("⚠️ 无法检查 backend Python 版本")
    else:
        print("⚠️ backend 目录不存在")

def install_dependencies():
    """安装依赖项"""
    print("📦 安装依赖项...")
    
    # 进入 backend 目录安装依赖
    backend_dir = Path("backend")
    if backend_dir.exists():
        run_command("uv sync", cwd=str(backend_dir))
        print("✅ 后端依赖安装完成")
    else:
        print("⚠️ backend 目录不存在，跳过后端依赖安装")

def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ backend 目录不存在")
        return False
    
    # 运行 Text-Splitter Engine 测试
    test_files = [
        "../tests/unit/test_text_splitter_engine.py"
    ]
    
    all_passed = True
    for test_file in test_files:
        if Path(test_file.replace("../", "")).exists():
            print(f"🧪 运行测试: {test_file}")
            result = run_command(
                f"uv run python -m pytest {test_file} -v", 
                cwd=str(backend_dir),
                check=False
            )
            if result.returncode != 0:
                print(f"❌ 测试失败: {test_file}")
                all_passed = False
            else:
                print(f"✅ 测试通过: {test_file}")
        else:
            print(f"⚠️ 测试文件不存在: {test_file}")
    
    return all_passed

def run_demo():
    """运行演示"""
    print("🚀 运行演示...")
    
    demo_code = '''
import sys
sys.path.insert(0, "..")

from engines.text_splitter.engine import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy
from engines.text_splitter.models import Document

# 创建引擎
engine = TextSplitterEngine()

# 创建测试文档
sample_text = """
人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，
并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、
语言识别、图像识别、自然语言处理和专家系统等。

机器学习是人工智能的一个重要分支，它是一种通过算法使机器能够从数据中学习并做出决策
或预测的技术。深度学习是机器学习的一个子集，它使用多层神经网络来模拟人脑的工作方式。

自然语言处理（NLP）是人工智能和语言学领域的分支学科。此领域探讨如何处理及运用自然语言；
自然语言处理包括多个方面和步骤，基本有认知、理解、生成等部分。
""".strip()

document = Document(
    title="人工智能简介",
    content=sample_text,
    file_type="txt",
    size=len(sample_text.encode('utf-8'))
)

print("📄 文档信息:")
print(f"  标题: {document.title}")
print(f"  大小: {document.size} 字节")
print(f"  ID: {document.id}")

# 使用不同策略分割
strategies = [
    ("Token-based (50 tokens)", TokenBasedStrategy(max_tokens=50)),
    ("Character-based (200 chars)", CharacterBasedStrategy(max_chars=200))
]

for strategy_name, strategy in strategies:
    print(f"\\n🔪 使用策略: {strategy_name}")
    result = engine.split_document(document)
    
    print(f"  总块数: {result.total_chunks}")
    print(f"  使用策略: {result.strategy_used}")
    
    for i, chunk in enumerate(result.chunks[:2]):  # 只显示前2块
        print(f"  块 {i+1}: {chunk.get_preview(80)}")
    
    if len(result.chunks) > 2:
        print(f"  ... 还有 {len(result.chunks) - 2} 个块")

print("\\n✅ 演示完成！")
'''
    
    # 将演示代码写入临时文件
    demo_file = Path("backend/temp_demo.py")
    demo_file.write_text(demo_code)

    try:
        # 运行演示
        result = run_command(
            "uv run python temp_demo.py",
            cwd="backend",
            check=False
        )
        
        if result.returncode == 0:
            print("✅ 演示运行成功")
            return True
        else:
            print("❌ 演示运行失败")
            return False
    finally:
        # 清理临时文件
        if demo_file.exists():
            demo_file.unlink()

def main():
    """主函数"""
    print("🚀 开始构建和测试模块化架构...")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # 1. 检查依赖
        check_dependencies()
        print()
        
        # 2. 安装依赖
        install_dependencies()
        print()
        
        # 3. 运行测试
        tests_passed = run_tests()
        print()
        
        # 4. 运行演示
        if tests_passed:
            demo_success = run_demo()
        else:
            print("⚠️ 测试未全部通过，跳过演示")
            demo_success = False
        
        # 5. 总结
        end_time = time.time()
        duration = end_time - start_time
        
        print("=" * 60)
        print("📊 构建和测试总结:")
        print(f"⏱️ 总耗时: {duration:.2f} 秒")
        print(f"🧪 测试结果: {'✅ 通过' if tests_passed else '❌ 失败'}")
        print(f"🚀 演示结果: {'✅ 成功' if demo_success else '❌ 失败'}")
        
        if tests_passed and demo_success:
            print("\n🎉 所有检查都通过了！模块化架构已准备就绪。")
            print("\n📋 下一步建议:")
            print("1. 查看架构文档: docs/MODULAR_ARCHITECTURE.md")
            print("2. 开始开发各个服务模块")
            print("3. 集成到现有的 FastAPI 应用中")
            return 0
        else:
            print("\n⚠️ 部分检查失败，请查看上面的错误信息。")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
