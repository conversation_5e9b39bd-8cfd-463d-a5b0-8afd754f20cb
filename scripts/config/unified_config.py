#!/usr/bin/env python3
"""
统一配置管理模块

这个模块提供了整个Master-Know系统的统一配置管理，
确保所有服务使用一致的环境变量命名和配置结构。
"""

import os
from functools import lru_cache
from typing import Optional, Dict, Any, List
from pydantic import Field, validator, computed_field
from pydantic_settings import BaseSettings


class UnifiedSettings(BaseSettings):
    """统一配置类 - 所有服务的基础配置"""
    
    # =============================================================================
    # 基础项目设置
    # =============================================================================
    project_name: str = Field(default="Master-Know", description="项目名称")
    stack_name: str = Field(default="master-know", description="技术栈名称")
    environment: str = Field(default="local", description="运行环境")
    
    # 域名和前端设置
    domain: str = Field(default="localhost", description="域名")
    frontend_host: str = Field(default="http://localhost:5173", description="前端主机地址")
    
    # =============================================================================
    # 安全配置
    # =============================================================================
    secret_key: str = Field(default="changethis", description="JWT密钥")
    access_token_expire_minutes: int = Field(default=11520, description="访问令牌过期时间(分钟)")
    jwt_algorithm: str = Field(default="HS256", description="JWT算法")
    jwt_refresh_expire_days: int = Field(default=30, description="刷新令牌过期天数")
    
    # CORS设置
    backend_cors_origins: str = Field(
        default="http://localhost,http://localhost:5173,https://localhost,https://localhost:5173",
        description="CORS允许的源"
    )
    
    # 超级用户
    first_superuser: str = Field(default="<EMAIL>", description="第一个超级用户邮箱")
    first_superuser_password: str = Field(default="changethis", description="第一个超级用户密码")
    
    # =============================================================================
    # 数据库配置
    # =============================================================================
    postgres_server: str = Field(default="db", description="PostgreSQL服务器")
    postgres_port: int = Field(default=5432, description="PostgreSQL端口")
    postgres_db: str = Field(default="master_know", description="数据库名")
    postgres_user: str = Field(default="master_know_user", description="数据库用户")
    postgres_password: str = Field(default="changethis", description="数据库密码")
    
    # 数据库连接池设置
    database_pool_size: int = Field(default=10, description="数据库连接池大小")
    database_pool_timeout: int = Field(default=30, description="数据库连接超时")
    database_echo: bool = Field(default=False, description="是否输出SQL日志")
    
    @computed_field
    @property
    def database_url(self) -> str:
        """计算数据库连接URL"""
        return f"postgresql+psycopg://{self.postgres_user}:{self.postgres_password}@{self.postgres_server}:{self.postgres_port}/{self.postgres_db}"
    
    # =============================================================================
    # Redis配置
    # =============================================================================
    redis_host: str = Field(default="redis", description="Redis主机")
    redis_port: int = Field(default=6379, description="Redis端口")
    redis_db: int = Field(default=0, description="Redis数据库")
    redis_password: str = Field(default="", description="Redis密码")
    
    @computed_field
    @property
    def redis_url(self) -> str:
        """计算Redis连接URL"""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    # =============================================================================
    # Manticore Search配置
    # =============================================================================
    manticore_host: str = Field(default="manticore", description="Manticore主机")
    manticore_mysql_port: int = Field(default=9306, description="Manticore MySQL端口")
    manticore_http_port: int = Field(default=9308, description="Manticore HTTP端口")
    manticore_sphinxapi_port: int = Field(default=9312, description="Manticore SphinxAPI端口")
    
    @computed_field
    @property
    def manticore_http_url(self) -> str:
        """计算Manticore HTTP URL"""
        return f"http://{self.manticore_host}:{self.manticore_http_port}"
    
    @computed_field
    @property
    def manticore_mysql_url(self) -> str:
        """计算Manticore MySQL URL"""
        return f"mysql://{self.manticore_host}:{self.manticore_mysql_port}"
    
    # =============================================================================
    # 服务端口配置
    # =============================================================================
    gateway_service_port: int = Field(default=9000, description="网关服务端口")
    embedding_service_port: int = Field(default=9001, description="向量化服务端口")
    user_service_port: int = Field(default=9002, description="用户服务端口")
    document_service_port: int = Field(default=9003, description="文档服务端口")
    topic_service_port: int = Field(default=9004, description="主题服务端口")
    llm_service_port: int = Field(default=9005, description="LLM服务端口")
    conversation_service_port: int = Field(default=9006, description="对话服务端口")
    summary_service_port: int = Field(default=9007, description="摘要服务端口")
    manticore_service_port: int = Field(default=9008, description="Manticore服务端口")
    
    # =============================================================================
    # 向量化配置
    # =============================================================================
    embedding_openai_api_key: str = Field(default="", description="OpenAI API密钥(向量化)")
    embedding_openai_base_url: str = Field(default="https://api.openai.com/v1", description="OpenAI API基础URL(向量化)")
    embedding_default_model: str = Field(default="text-embedding-3-small", description="默认向量化模型")
    embedding_dim: int = Field(default=1536, description="向量维度")
    embedding_batch_size: int = Field(default=64, description="批处理大小")
    embedding_request_timeout: int = Field(default=30, description="请求超时时间")
    embedding_max_retries: int = Field(default=3, description="最大重试次数")
    
    # 向量化缓存设置
    embedding_enable_cache: bool = Field(default=False, description="是否启用缓存")
    embedding_cache_ttl: int = Field(default=3600, description="缓存过期时间")
    
    # =============================================================================
    # LLM集成配置
    # =============================================================================
    llm_openai_api_key: str = Field(default="", description="OpenAI API密钥(LLM)")
    llm_openai_base_url: str = Field(default="https://api.openai.com/v1", description="OpenAI API基础URL(LLM)")
    llm_openai_model: str = Field(default="gpt-4o-mini", description="OpenAI模型")
    llm_max_tokens: int = Field(default=4096, description="最大令牌数")
    llm_temperature: float = Field(default=0.2, description="温度参数")
    
    # LLM服务设置
    llm_default_provider: str = Field(default="openai", description="默认LLM提供商")
    llm_request_timeout: int = Field(default=60, description="请求超时时间")
    llm_max_retries: int = Field(default=3, description="最大重试次数")
    
    # =============================================================================
    # 文档处理配置
    # =============================================================================
    document_upload_dir: str = Field(default="./storage/uploads", description="文件上传目录")
    document_processed_dir: str = Field(default="./storage/processed", description="处理后文件目录")
    document_max_file_size: int = Field(default=10485760, description="最大文件大小")
    document_allowed_extensions: List[str] = Field(
        default=[".txt", ".md", ".pdf", ".docx"], 
        description="允许的文件扩展名"
    )
    
    # 文本分割设置
    document_max_chunk_size: int = Field(default=1000, description="最大分块大小")
    document_overlap_size: int = Field(default=100, description="重叠大小")
    document_min_chunk_size: int = Field(default=200, description="最小分块大小")
    
    # 处理设置
    document_enable_async_processing: bool = Field(default=True, description="是否启用异步处理")
    document_processing_timeout: int = Field(default=300, description="处理超时时间")
    document_max_concurrent_jobs: int = Field(default=5, description="最大并发任务数")
    
    # =============================================================================
    # 邮件配置
    # =============================================================================
    smtp_host: Optional[str] = Field(default=None, description="SMTP主机")
    smtp_port: int = Field(default=587, description="SMTP端口")
    smtp_tls: bool = Field(default=True, description="是否使用TLS")
    smtp_ssl: bool = Field(default=False, description="是否使用SSL")
    smtp_user: Optional[str] = Field(default=None, description="SMTP用户")
    smtp_password: Optional[str] = Field(default=None, description="SMTP密码")
    emails_from_email: Optional[str] = Field(default="<EMAIL>", description="发件人邮箱")
    emails_from_name: Optional[str] = Field(default=None, description="发件人名称")
    
    # =============================================================================
    # 异步处理配置
    # =============================================================================
    dramatiq_broker: str = Field(default="redis", description="Dramatiq代理")
    dramatiq_workers: int = Field(default=4, description="工作进程数")
    task_timeout: int = Field(default=300, description="任务超时时间")
    
    # =============================================================================
    # 日志配置
    # =============================================================================
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(default="json", description="日志格式")
    
    # =============================================================================
    # Docker配置
    # =============================================================================
    docker_image_backend: str = Field(default="backend", description="后端Docker镜像")
    docker_image_frontend: str = Field(default="frontend", description="前端Docker镜像")
    tag: str = Field(default="latest", description="镜像标签")
    
    # =============================================================================
    # 验证器
    # =============================================================================
    @validator('environment')
    def validate_environment(cls, v):
        valid_envs = ['local', 'staging', 'production']
        if v not in valid_envs:
            raise ValueError(f'环境必须是以下之一: {valid_envs}')
        return v
    
    @validator('embedding_openai_base_url', 'llm_openai_base_url')
    def validate_openai_urls(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("OpenAI API URL必须以http://或https://开头")
        return v.rstrip('/')
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"


@lru_cache()
def get_unified_settings() -> UnifiedSettings:
    """获取统一配置实例（单例模式）"""
    return UnifiedSettings()


def get_service_config(service_name: str, additional_config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    为特定服务获取配置
    
    Args:
        service_name: 服务名称 (embedding, llm, document, etc.)
        additional_config: 额外的服务特定配置
    
    Returns:
        服务配置字典
    """
    settings = get_unified_settings()
    
    # 基础配置
    config = {
        "project_name": settings.project_name,
        "environment": settings.environment,
        "database_url": settings.database_url,
        "redis_url": settings.redis_url,
        "log_level": settings.log_level,
    }
    
    # 服务特定配置
    if service_name == "embedding":
        config.update({
            "api_port": settings.embedding_service_port,
            "openai_api_key": settings.embedding_openai_api_key,
            "openai_base_url": settings.embedding_openai_base_url,
            "default_model": settings.embedding_default_model,
            "embedding_dim": settings.embedding_dim,
            "batch_size": settings.embedding_batch_size,
            "request_timeout": settings.embedding_request_timeout,
            "max_retries": settings.embedding_max_retries,
            "enable_cache": settings.embedding_enable_cache,
            "cache_ttl": settings.embedding_cache_ttl,
        })
    elif service_name == "llm":
        config.update({
            "api_port": settings.llm_service_port,
            "openai_api_key": settings.llm_openai_api_key,
            "openai_base_url": settings.llm_openai_base_url,
            "openai_model": settings.llm_openai_model,
            "max_tokens": settings.llm_max_tokens,
            "temperature": settings.llm_temperature,
            "default_provider": settings.llm_default_provider,
            "request_timeout": settings.llm_request_timeout,
            "max_retries": settings.llm_max_retries,
        })
    elif service_name == "document":
        config.update({
            "api_port": settings.document_service_port,
            "upload_dir": settings.document_upload_dir,
            "processed_dir": settings.document_processed_dir,
            "max_file_size": settings.document_max_file_size,
            "allowed_extensions": settings.document_allowed_extensions,
            "max_chunk_size": settings.document_max_chunk_size,
            "overlap_size": settings.document_overlap_size,
            "min_chunk_size": settings.document_min_chunk_size,
            "enable_async_processing": settings.document_enable_async_processing,
            "processing_timeout": settings.document_processing_timeout,
            "max_concurrent_jobs": settings.document_max_concurrent_jobs,
        })
    
    # 添加额外配置
    if additional_config:
        config.update(additional_config)
    
    return config


if __name__ == "__main__":
    # 测试配置加载
    try:
        settings = get_unified_settings()
        print("✅ 统一配置加载成功")
        print(f"项目名称: {settings.project_name}")
        print(f"环境: {settings.environment}")
        print(f"数据库URL: {settings.database_url}")
        print(f"Redis URL: {settings.redis_url}")
        print(f"Manticore HTTP URL: {settings.manticore_http_url}")
        
        # 测试服务配置
        embedding_config = get_service_config("embedding")
        print(f"向量化服务端口: {embedding_config['api_port']}")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
