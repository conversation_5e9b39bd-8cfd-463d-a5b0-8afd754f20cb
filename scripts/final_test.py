#!/usr/bin/env python3
"""
最终测试脚本 - 验证Text-Splitter Engine完整功能
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from engines.text_splitter.engine import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy
from engines.text_splitter.models import Document

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    engine = TextSplitterEngine()
    
    # 测试文本
    text = "这是一个测试。" * 20  # 创建重复文本
    document = Document(
        title="测试文档",
        content=text,
        file_type="txt",
        size=len(text.encode('utf-8'))
    )
    
    # 测试Token-based分割
    strategy = TokenBasedStrategy(max_tokens=10)
    result = engine.split_document(document, strategy)
    
    print(f"  ✅ Token-based分割: {result.total_chunks} 块")
    assert result.total_chunks > 1, "应该分割成多个块"
    
    # 测试Character-based分割
    strategy = CharacterBasedStrategy(max_chars=20)
    result = engine.split_document(document, strategy)
    
    print(f"  ✅ Character-based分割: {result.total_chunks} 块")
    assert result.total_chunks > 1, "应该分割成多个块"
    
    print("  ✅ 基本功能测试通过")

def test_batch_processing():
    """测试批量处理"""
    print("🧪 测试批量处理...")
    
    engine = TextSplitterEngine()
    
    # 创建多个文档
    documents = []
    for i in range(3):
        text = f"这是第{i+1}个测试文档。" * 15
        doc = Document(
            title=f"文档{i+1}",
            content=text,
            file_type="txt",
            size=len(text.encode('utf-8'))
        )
        documents.append(doc)
    
    # 批量处理
    results = engine.batch_split(documents)
    
    print(f"  ✅ 批量处理: {len(results)} 个结果")
    assert len(results) == 3, "应该有3个结果"
    assert all(r.total_chunks > 0 for r in results), "所有文档都应该被分割"
    
    print("  ✅ 批量处理测试通过")

def test_different_strategies():
    """测试不同策略的效果"""
    print("🧪 测试不同策略效果...")
    
    engine = TextSplitterEngine()
    
    # 长文本
    long_text = """
    人工智能是计算机科学的一个重要分支。机器学习是其核心技术。
    深度学习使用神经网络。自然语言处理处理人类语言。
    计算机视觉分析图像。强化学习通过交互学习。
    这些技术正在改变世界。
    """ * 5
    
    document = Document(
        title="AI概述",
        content=long_text.strip(),
        file_type="txt",
        size=len(long_text.encode('utf-8'))
    )
    
    strategies = [
        ("小块Token", TokenBasedStrategy(max_tokens=20)),
        ("大块Token", TokenBasedStrategy(max_tokens=100)),
        ("小块字符", CharacterBasedStrategy(max_chars=50)),
        ("大块字符", CharacterBasedStrategy(max_chars=200)),
    ]
    
    results = {}
    for name, strategy in strategies:
        result = engine.split_document(document, strategy)
        results[name] = result.total_chunks
        print(f"  ✅ {name}: {result.total_chunks} 块")
    
    # 验证策略效果
    assert results["小块Token"] >= results["大块Token"], "小块应该产生更多分割"
    assert results["小块字符"] >= results["大块字符"], "小块应该产生更多分割"
    
    print("  ✅ 不同策略测试通过")

def test_edge_cases():
    """测试边界情况"""
    print("🧪 测试边界情况...")
    
    engine = TextSplitterEngine()
    
    # 空文档
    try:
        empty_doc = Document(
            title="空文档",
            content="",
            file_type="txt",
            size=0
        )
        print("  ❌ 空文档应该抛出异常")
    except ValueError:
        print("  ✅ 空文档正确抛出异常")
    
    # 很短的文档
    short_doc = Document(
        title="短文档",
        content="短",
        file_type="txt",
        size=len("短".encode('utf-8'))
    )
    
    result = engine.split_document(short_doc, TokenBasedStrategy(max_tokens=100))
    print(f"  ✅ 短文档处理: {result.total_chunks} 块")
    assert result.total_chunks == 1, "短文档应该只有1块"
    
    print("  ✅ 边界情况测试通过")

def main():
    """主测试函数"""
    print("🚀 开始最终测试...")
    print("=" * 60)
    
    try:
        test_basic_functionality()
        print()
        
        test_batch_processing()
        print()
        
        test_different_strategies()
        print()
        
        test_edge_cases()
        print()
        
        print("=" * 60)
        print("🎉 所有测试通过！Text-Splitter Engine 工作正常")
        print()
        print("📋 功能验证完成:")
        print("  ✅ 基本文本分割功能")
        print("  ✅ 多种分割策略")
        print("  ✅ 批量处理能力")
        print("  ✅ 边界情况处理")
        print("  ✅ 错误处理机制")
        print()
        print("🚀 可以开始使用以下命令测试:")
        print("  python scripts/quick_demo.py        # 快速演示")
        print("  python scripts/interactive_demo.py  # 交互式演示")
        print("  python scripts/build_and_test.py    # 完整构建测试")
        
        return 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
