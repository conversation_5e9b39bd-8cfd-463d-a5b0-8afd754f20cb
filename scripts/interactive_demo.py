#!/usr/bin/env python3
"""
Text-Splitter Engine 交互式演示
用户可以输入文本，选择分割策略，查看分割效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from engines.text_splitter.engine import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy, MarkdownStrategy
from engines.text_splitter.models import Document
from engines.text_splitter.config import TextSplitterConfig

def print_banner():
    """打印横幅"""
    print("=" * 80)
    print("🔪 Text-Splitter Engine 交互式演示")
    print("=" * 80)
    print("这个演示让您可以测试不同的文本分割策略")
    print("支持的策略：Token-based, Character-based, Markdown")
    print("=" * 80)

def get_sample_texts():
    """获取示例文本"""
    return {
        "1": {
            "title": "人工智能简介",
            "content": """人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。

机器学习是人工智能的一个重要分支，它是一种通过算法使机器能够从数据中学习并做出决策或预测的技术。深度学习是机器学习的一个子集，它使用多层神经网络来模拟人脑的工作方式。

自然语言处理（NLP）是人工智能和语言学领域的分支学科。此领域探讨如何处理及运用自然语言；自然语言处理包括多个方面和步骤，基本有认知、理解、生成等部分。

计算机视觉是人工智能的另一个重要分支，它致力于使计算机能够从数字图像或视频中获取高层次的理解。这个领域包括图像识别、物体检测、人脸识别、图像分割等技术。"""
        },
        "2": {
            "title": "Python 编程示例",
            "content": """# Python 函数定义示例
def fibonacci(n):
    \"\"\"计算斐波那契数列的第n项\"\"\"
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)

# 类定义示例
class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def multiply(self, a, b):
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result

# 使用示例
calc = Calculator()
print(calc.add(5, 3))
print(calc.multiply(4, 7))
print("计算历史:", calc.history)"""
        },
        "3": {
            "title": "Markdown 文档示例",
            "content": """# 项目文档

## 概述
这是一个示例项目，展示了如何使用 Markdown 编写文档。

## 功能特性
- **文本处理**: 支持多种文本格式
- **数据分析**: 提供强大的分析工具
- **可视化**: 生成美观的图表

### 安装指南
1. 克隆仓库
2. 安装依赖
3. 运行测试

### 使用方法
```python
from myproject import TextProcessor

processor = TextProcessor()
result = processor.process("Hello World")
print(result)
```

## API 参考
### TextProcessor 类
- `process(text)`: 处理输入文本
- `analyze(data)`: 分析数据
- `visualize(results)`: 生成可视化

## 贡献指南
欢迎提交 Pull Request 和 Issue！"""
        }
    }

def choose_text():
    """选择或输入文本"""
    samples = get_sample_texts()
    
    print("\n📝 请选择文本来源：")
    print("1. 人工智能简介（中文长文本）")
    print("2. Python 编程示例（代码文本）")
    print("3. Markdown 文档示例（结构化文本）")
    print("4. 自定义输入")
    
    while True:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice in ["1", "2", "3"]:
            sample = samples[choice]
            print(f"\n✅ 已选择: {sample['title']}")
            print(f"📊 文本长度: {len(sample['content'])} 字符")
            return sample['title'], sample['content']
        
        elif choice == "4":
            print("\n📝 请输入您的文本（输入 'END' 结束）：")
            lines = []
            while True:
                line = input()
                if line.strip() == "END":
                    break
                lines.append(line)
            
            content = "\n".join(lines)
            if not content.strip():
                print("❌ 文本不能为空，请重新选择")
                continue
            
            title = input("请输入文档标题: ").strip() or "自定义文档"
            return title, content
        
        else:
            print("❌ 无效选择，请输入 1-4")

def choose_strategy():
    """选择分割策略"""
    print("\n🔪 请选择分割策略：")
    print("1. Token-based (基于 Token 分割)")
    print("2. Character-based (基于字符分割)")
    print("3. Markdown (Markdown 感知分割)")
    print("4. 比较所有策略")
    
    while True:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            max_tokens = input("请输入最大 Token 数 (默认 100): ").strip()
            max_tokens = int(max_tokens) if max_tokens.isdigit() else 100
            return [("Token-based", TokenBasedStrategy(max_tokens=max_tokens))]
        
        elif choice == "2":
            max_chars = input("请输入最大字符数 (默认 200): ").strip()
            max_chars = int(max_chars) if max_chars.isdigit() else 200
            return [("Character-based", CharacterBasedStrategy(max_chars=max_chars))]
        
        elif choice == "3":
            max_chars = input("请输入最大字符数 (默认 300): ").strip()
            max_chars = int(max_chars) if max_chars.isdigit() else 300
            return [("Markdown", MarkdownStrategy(max_chars=max_chars))]
        
        elif choice == "4":
            return [
                ("Token-based (100 tokens)", TokenBasedStrategy(max_tokens=100)),
                ("Character-based (200 chars)", CharacterBasedStrategy(max_chars=200)),
                ("Markdown (300 chars)", MarkdownStrategy(max_chars=300))
            ]
        
        else:
            print("❌ 无效选择，请输入 1-4")

def display_results(title, content, results_list):
    """显示分割结果"""
    print("\n" + "=" * 80)
    print(f"📄 文档: {title}")
    print(f"📊 原始长度: {len(content)} 字符")
    print("=" * 80)
    
    for strategy_name, result in results_list:
        print(f"\n🔪 策略: {strategy_name}")
        print(f"📦 总块数: {result.total_chunks}")
        print(f"⏱️ 处理时间: {result.processing_time:.3f}s" if result.processing_time else "⏱️ 处理时间: N/A")
        
        # 显示统计信息
        stats = result.get_statistics()
        if stats:
            print(f"📈 平均块大小: {stats.get('avg_chunk_size', 0):.1f} 字符")
            print(f"📏 块大小范围: {stats.get('min_chunk_size', 0)} - {stats.get('max_chunk_size', 0)} 字符")
        
        print("\n📝 分割结果:")
        for i, chunk in enumerate(result.chunks):
            print(f"\n  块 {i+1} ({chunk.get_length()} 字符):")
            print(f"  ┌─ 位置: {chunk.start_char}-{chunk.end_char}")
            print(f"  │ Token 数: {chunk.token_count or 'N/A'}")
            print(f"  └─ 内容预览:")
            
            # 显示内容，每行缩进
            preview = chunk.get_preview(150)
            for line in preview.split('\n'):
                print(f"     {line}")
        
        print("\n" + "-" * 60)

def main():
    """主函数"""
    print_banner()
    
    try:
        # 初始化引擎
        print("🔧 初始化 Text-Splitter Engine...")
        config = TextSplitterConfig()
        engine = TextSplitterEngine(config)
        print("✅ 引擎初始化完成")
        
        while True:
            # 选择文本
            title, content = choose_text()
            
            # 创建文档
            document = Document(
                title=title,
                content=content,
                file_type="txt",
                size=len(content.encode('utf-8'))
            )
            
            # 选择策略
            strategies = choose_strategy()
            
            # 执行分割
            print("\n🚀 开始分割...")
            results_list = []
            
            for strategy_name, strategy in strategies:
                print(f"  处理策略: {strategy_name}")
                import time
                start_time = time.time()
                
                result = engine.split_document(document, strategy)
                result.processing_time = time.time() - start_time
                
                results_list.append((strategy_name, result))
            
            # 显示结果
            display_results(title, content, results_list)
            
            # 询问是否继续
            print("\n🔄 是否继续测试？")
            continue_choice = input("输入 'y' 继续，其他键退出: ").strip().lower()
            if continue_choice != 'y':
                break
        
        print("\n👋 感谢使用 Text-Splitter Engine 演示！")
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出演示")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
