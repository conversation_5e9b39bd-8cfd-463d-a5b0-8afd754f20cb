#!/usr/bin/env python3
"""
配置迁移脚本

将现有的各种配置文件迁移到统一配置管理系统。
"""

import os
import sys
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.config.unified_config import get_unified_settings


def backup_config_file(file_path: Path):
    """备份配置文件"""
    if file_path.exists():
        backup_path = file_path.with_suffix(f"{file_path.suffix}.backup")
        shutil.copy2(file_path, backup_path)
        print(f"   📁 已备份: {file_path} -> {backup_path}")


def update_backend_config():
    """更新backend配置文件"""
    print("🔧 更新backend配置...")
    
    config_path = project_root / "backend" / "app" / "core" / "config.py"
    if not config_path.exists():
        print(f"   ⚠️  配置文件不存在: {config_path}")
        return
    
    backup_config_file(config_path)
    
    # 读取现有配置
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新端口配置
    replacements = [
        ('MANTICORE_PORT: int = 9308', 'MANTICORE_HTTP_PORT: int = 9308'),
        ('MANTICORE_MYSQL_PORT: int = 9306', 'MANTICORE_MYSQL_PORT: int = 9306'),
    ]
    
    for old, new in replacements:
        if old in content and new not in content:
            content = content.replace(old, new)
            print(f"   ✅ 已更新: {old} -> {new}")
    
    # 写回文件
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("   ✅ backend配置更新完成")


def update_topic_service_config():
    """更新topic服务配置"""
    print("🔧 更新topic服务配置...")
    
    config_path = project_root / "services" / "topic" / "utils" / "config.py"
    if not config_path.exists():
        print(f"   ⚠️  配置文件不存在: {config_path}")
        return
    
    backup_config_file(config_path)
    
    # 读取现有配置
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新端口配置
    if 'default=9004' not in content:
        content = content.replace(
            'default=8004',
            'default=8004  # 统一端口分配'
        )
        print("   ✅ 已更新端口配置")
    
    # 写回文件
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("   ✅ topic服务配置更新完成")


def update_text_splitter_config():
    """更新text_splitter配置"""
    print("🔧 更新text_splitter配置...")
    
    config_path = project_root / "engines" / "text_splitter" / "config.py"
    if not config_path.exists():
        print(f"   ⚠️  配置文件不存在: {config_path}")
        return
    
    backup_config_file(config_path)
    print("   ✅ text_splitter配置备份完成")


def create_unified_env_file():
    """创建统一的.env文件"""
    print("🔧 创建统一.env文件...")
    
    env_path = project_root / ".env"
    env_example_path = project_root / ".env.example"
    
    if env_path.exists():
        backup_config_file(env_path)
    
    # 从.env.example复制到.env
    if env_example_path.exists():
        shutil.copy2(env_example_path, env_path)
        print(f"   ✅ 已创建: {env_path}")
    else:
        print(f"   ⚠️  .env.example不存在: {env_example_path}")


def update_demo_configs():
    """更新demo目录中的配置"""
    print("🔧 更新demo配置...")
    
    demo_path = project_root / "demo"
    
    # 更新demo/.env.template
    demo_env_template = demo_path / ".env.template"
    if demo_env_template.exists():
        backup_config_file(demo_env_template)
        
        # 删除旧的demo配置模板，使用根目录的统一配置
        print(f"   📝 demo配置模板已备份，建议使用根目录的.env.example")
    
    # 检查各POC模块的配置
    poc_modules = [
        "llm_poc", "user_poc", "topic_poc", 
        "embedding_poc", "manticore_poc", "document_poc"
    ]
    
    for module in poc_modules:
        module_path = demo_path / module
        if module_path.exists():
            env_example = module_path / ".env.example"
            if env_example.exists():
                backup_config_file(env_example)
                print(f"   📁 已备份POC配置: {module}/.env.example")


def validate_unified_config():
    """验证统一配置"""
    print("🧪 验证统一配置...")
    
    try:
        settings = get_unified_settings()
        
        # 检查关键配置
        checks = [
            ("项目名称", settings.project_name),
            ("环境", settings.environment),
            ("数据库URL", settings.database_url),
            ("Redis URL", settings.redis_url),
            ("Manticore HTTP URL", settings.manticore_http_url),
        ]
        
        for name, value in checks:
            print(f"   ✅ {name}: {value}")
        
        # 检查端口分配
        ports = {
            "gateway": settings.gateway_service_port,
            "embedding": settings.embedding_service_port,
            "user": settings.user_service_port,
            "document": settings.document_service_port,
            "topic": settings.topic_service_port,
            "llm": settings.llm_service_port,
            "conversation": settings.conversation_service_port,
            "summary": settings.summary_service_port,
            "manticore": settings.manticore_service_port,
        }
        
        print(f"   ✅ 端口分配: {ports}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置验证失败: {e}")
        return False


def create_service_config_adapters():
    """为各服务创建配置适配器"""
    print("🔧 创建服务配置适配器...")
    
    # 为backend创建配置适配器
    backend_adapter_path = project_root / "backend" / "app" / "core" / "unified_config_adapter.py"
    backend_adapter_content = '''"""
Backend配置适配器

将统一配置适配到backend的配置格式
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from scripts.config.unified_config import get_unified_settings
    
    def get_backend_config():
        """获取backend配置"""
        settings = get_unified_settings()
        
        return {
            "PROJECT_NAME": settings.project_name,
            "ENVIRONMENT": settings.environment,
            "SECRET_KEY": settings.secret_key,
            "ACCESS_TOKEN_EXPIRE_MINUTES": settings.access_token_expire_minutes,
            "FRONTEND_HOST": settings.frontend_host,
            "BACKEND_CORS_ORIGINS": settings.backend_cors_origins,
            "FIRST_SUPERUSER": settings.first_superuser,
            "FIRST_SUPERUSER_PASSWORD": settings.first_superuser_password,
            
            # 数据库配置
            "POSTGRES_SERVER": settings.postgres_server,
            "POSTGRES_PORT": settings.postgres_port,
            "POSTGRES_DB": settings.postgres_db,
            "POSTGRES_USER": settings.postgres_user,
            "POSTGRES_PASSWORD": settings.postgres_password,
            
            # 向量化配置
            "EMBEDDING_OPENAI_API_KEY": settings.embedding_openai_api_key,
            "EMBEDDING_OPENAI_BASE_URL": settings.embedding_openai_base_url,
            "EMBEDDING_DEFAULT_MODEL": settings.embedding_default_model,
            "EMBEDDING_DIM": settings.embedding_dim,
            "EMBEDDING_BATCH_SIZE": settings.embedding_batch_size,
            
            # Manticore配置
            "MANTICORE_HOST": settings.manticore_host,
            "MANTICORE_HTTP_PORT": settings.manticore_http_port,
            "MANTICORE_MYSQL_PORT": settings.manticore_mysql_port,
            
            # LLM配置
            "LLM_OPENAI_API_KEY": settings.llm_openai_api_key,
            "LLM_OPENAI_BASE_URL": settings.llm_openai_base_url,
            "LLM_OPENAI_MODEL": settings.llm_openai_model,
            "LLM_MAX_TOKENS": settings.llm_max_tokens,
            "LLM_TEMPERATURE": settings.llm_temperature,
            
            # 邮件配置
            "SMTP_HOST": settings.smtp_host,
            "SMTP_PORT": settings.smtp_port,
            "SMTP_TLS": settings.smtp_tls,
            "SMTP_SSL": settings.smtp_ssl,
            "SMTP_USER": settings.smtp_user,
            "SMTP_PASSWORD": settings.smtp_password,
            "EMAILS_FROM_EMAIL": settings.emails_from_email,
            "EMAILS_FROM_NAME": settings.emails_from_name,
        }
        
except ImportError:
    def get_backend_config():
        """降级配置"""
        return {}
'''
    
    with open(backend_adapter_path, 'w', encoding='utf-8') as f:
        f.write(backend_adapter_content)
    
    print(f"   ✅ 已创建backend配置适配器: {backend_adapter_path}")


def run_migration():
    """运行配置迁移"""
    print("🚀 开始配置迁移...")
    print("=" * 50)
    
    steps = [
        ("备份和更新backend配置", update_backend_config),
        ("备份和更新topic服务配置", update_topic_service_config),
        ("备份text_splitter配置", update_text_splitter_config),
        ("创建统一.env文件", create_unified_env_file),
        ("更新demo配置", update_demo_configs),
        ("创建服务配置适配器", create_service_config_adapters),
        ("验证统一配置", validate_unified_config),
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        try:
            result = step_func()
            if result is not False:
                success_count += 1
                print(f"   ✅ {step_name}完成")
            else:
                print(f"   ⚠️  {step_name}部分完成")
        except Exception as e:
            print(f"   ❌ {step_name}失败: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 迁移结果: {success_count}/{len(steps)} 步骤完成")
    
    if success_count == len(steps):
        print("🎉 配置迁移成功完成！")
        print("\n📝 后续步骤:")
        print("1. 检查.env文件并设置必要的API密钥")
        print("2. 运行测试验证配置正确性")
        print("3. 更新各服务以使用统一配置")
    else:
        print("⚠️  配置迁移部分完成，请检查错误信息")
    
    return success_count == len(steps)


if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
