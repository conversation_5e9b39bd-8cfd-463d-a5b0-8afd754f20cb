#!/usr/bin/env python3
"""
Text-Splitter Engine 快速演示
展示文本分割功能的简单示例
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from engines.text_splitter.engine import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy
from engines.text_splitter.models import Document

def main():
    print("🔪 Text-Splitter Engine 快速演示")
    print("=" * 60)
    
    # 创建引擎
    engine = TextSplitterEngine()
    
    # 测试文本
    long_text = """
人工智能（Artificial Intelligence，AI）是计算机科学的一个重要分支，它致力于创建能够执行通常需要人类智能的任务的系统。这个领域包括多个子领域，每个都有其独特的挑战和应用。

机器学习是人工智能的核心技术之一，它使计算机能够从数据中学习模式，而无需明确编程每个可能的情况。通过分析大量数据，机器学习算法可以识别趋势、做出预测，并随着时间的推移改进其性能。

深度学习是机器学习的一个子集，它使用多层神经网络来模拟人脑的工作方式。这种方法在图像识别、语音识别和自然语言处理等领域取得了突破性进展。深度学习模型可以自动学习数据的层次化表示，从简单的特征到复杂的概念。

自然语言处理（NLP）是人工智能的另一个重要领域，它专注于使计算机能够理解、解释和生成人类语言。NLP技术被广泛应用于机器翻译、情感分析、聊天机器人和文本摘要等应用中。

计算机视觉使机器能够从图像和视频中提取有意义的信息。这个领域的应用包括人脸识别、物体检测、医学图像分析和自动驾驶汽车的视觉系统。

强化学习是一种机器学习方法，其中智能体通过与环境交互来学习最优行为。这种方法在游戏AI、机器人控制和资源管理等领域显示出巨大潜力。

这些技术的结合正在推动人工智能在各个领域的应用，包括医疗保健、金融、交通、教育和娱乐等。随着技术的不断发展，人工智能将继续改变我们的生活和工作方式，为人类社会带来前所未有的机遇和挑战。
    """.strip()
    
    # 创建文档
    document = Document(
        title="人工智能技术概述",
        content=long_text,
        file_type="txt",
        size=len(long_text.encode('utf-8'))
    )
    
    print(f"📄 文档: {document.title}")
    print(f"📊 文档长度: {len(long_text)} 字符")
    print(f"📦 文档大小: {document.size} 字节")
    print()
    
    # 测试不同的分割策略
    strategies = [
        ("Token-based (50 tokens)", TokenBasedStrategy(max_tokens=50)),
        ("Token-based (100 tokens)", TokenBasedStrategy(max_tokens=100)),
        ("Character-based (300 chars)", CharacterBasedStrategy(max_chars=300)),
        ("Character-based (500 chars)", CharacterBasedStrategy(max_chars=500)),
    ]
    
    for strategy_name, strategy in strategies:
        print(f"🔪 策略: {strategy_name}")
        print("-" * 40)
        
        # 执行分割
        result = engine.split_document(document, strategy)
        
        print(f"📦 总块数: {result.total_chunks}")
        stats = result.get_statistics()
        if stats:
            print(f"📈 平均块大小: {stats.get('avg_chunk_size', 0):.1f} 字符")
            print(f"📏 块大小范围: {stats.get('min_chunk_size', 0)} - {stats.get('max_chunk_size', 0)} 字符")
        
        print("\n📝 分割结果:")
        for i, chunk in enumerate(result.chunks):
            print(f"\n  块 {i+1} ({chunk.get_length()} 字符, {chunk.token_count} tokens):")
            print(f"  位置: {chunk.start_char}-{chunk.end_char}")
            
            # 显示内容预览
            preview = chunk.get_preview(120)
            lines = preview.split('\n')
            for line in lines[:3]:  # 只显示前3行
                if line.strip():
                    print(f"  > {line.strip()}")
            if len(lines) > 3:
                print("  > ...")
        
        print("\n" + "=" * 60)
    
    print("✅ 演示完成！")
    print("\n💡 提示:")
    print("- Token-based 策略适合控制 LLM 输入长度")
    print("- Character-based 策略适合固定长度的文本块")
    print("- 可以根据具体需求调整参数")
    print("\n🚀 运行交互式演示: python scripts/interactive_demo.py")

if __name__ == "__main__":
    main()
