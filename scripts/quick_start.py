#!/usr/bin/env python3
"""
Master-Know 快速启动脚本

提供便捷的方式启动和测试各个服务。
"""

import os
import sys
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.config.unified_config import get_unified_settings


def print_banner():
    """打印启动横幅"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                    Master-Know 快速启动                      ║
║                  统一配置管理系统 v1.0                       ║
╚══════════════════════════════════════════════════════════════╝
""")


def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or python_version.minor < 8:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("   需要Python 3.8+")
        return False
    else:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查配置文件
    env_file = project_root / ".env"
    if not env_file.exists():
        print("❌ .env文件不存在")
        print("   请运行: cp .env.example .env")
        return False
    else:
        print("✅ .env文件存在")
    
    # 检查统一配置
    try:
        settings = get_unified_settings()
        print(f"✅ 统一配置加载成功: {settings.project_name}")
    except Exception as e:
        print(f"❌ 统一配置加载失败: {e}")
        return False
    
    return True


def show_service_status():
    """显示服务状态"""
    print("\n📊 服务配置状态:")
    
    try:
        settings = get_unified_settings()
        
        services = [
            ("API网关", settings.gateway_service_port, "gateway"),
            ("向量化服务", settings.embedding_service_port, "embedding"),
            ("用户服务", settings.user_service_port, "user"),
            ("文档服务", settings.document_service_port, "document"),
            ("主题服务", settings.topic_service_port, "topic"),
            ("LLM服务", settings.llm_service_port, "llm"),
            ("对话服务", settings.conversation_service_port, "conversation"),
            ("摘要服务", settings.summary_service_port, "summary"),
            ("Manticore服务", settings.manticore_service_port, "manticore"),
        ]
        
        for name, port, service_id in services:
            print(f"   {name}: 端口 {port}")
        
        print(f"\n🔧 基础设施:")
        print(f"   数据库: {settings.postgres_server}:{settings.postgres_port}")
        print(f"   缓存: {settings.redis_host}:{settings.redis_port}")
        print(f"   搜索: {settings.manticore_host}:{settings.manticore_http_port}")
        
        # API密钥状态
        print(f"\n🔑 API密钥状态:")
        print(f"   向量化API: {'✅ 已设置' if settings.embedding_openai_api_key else '❌ 未设置'}")
        print(f"   LLM API: {'✅ 已设置' if settings.llm_openai_api_key else '❌ 未设置'}")
        
    except Exception as e:
        print(f"❌ 获取服务状态失败: {e}")


def start_backend():
    """启动backend服务"""
    print("\n🚀 启动Backend服务...")
    
    backend_dir = project_root / "backend"
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return False
    
    try:
        os.chdir(backend_dir)
        print(f"   📁 切换到目录: {backend_dir}")
        
        # 检查虚拟环境
        venv_dir = backend_dir / ".venv"
        if venv_dir.exists():
            print("   ✅ 找到虚拟环境")
            if sys.platform == "win32":
                python_cmd = str(venv_dir / "Scripts" / "python.exe")
            else:
                python_cmd = str(venv_dir / "bin" / "python")
        else:
            python_cmd = sys.executable
            print("   ⚠️  未找到虚拟环境，使用系统Python")
        
        # 启动命令
        cmd = [python_cmd, "-m", "uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"]
        print(f"   🔧 启动命令: {' '.join(cmd)}")
        
        # 启动服务
        process = subprocess.Popen(cmd)
        print(f"   ✅ Backend服务已启动 (PID: {process.pid})")
        print(f"   🌐 访问地址: http://localhost:8000")
        print(f"   📚 API文档: http://localhost:8000/docs")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Backend启动失败: {e}")
        return False
    finally:
        os.chdir(project_root)


def start_topic_poc():
    """启动Topic POC"""
    print("\n🚀 启动Topic POC...")
    
    topic_poc_dir = project_root / "demo" / "topic_poc"
    if not topic_poc_dir.exists():
        print("❌ topic_poc目录不存在")
        return False
    
    try:
        os.chdir(topic_poc_dir)
        print(f"   📁 切换到目录: {topic_poc_dir}")
        
        # 启动命令
        cmd = [sys.executable, "main.py"]
        print(f"   🔧 启动命令: {' '.join(cmd)}")
        
        # 启动服务
        process = subprocess.Popen(cmd)
        print(f"   ✅ Topic POC已启动 (PID: {process.pid})")
        print(f"   🌐 访问地址: http://localhost:8004")
        print(f"   📚 API文档: http://localhost:8004/docs")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Topic POC启动失败: {e}")
        return False
    finally:
        os.chdir(project_root)


def start_user_poc():
    """启动User POC"""
    print("\n🚀 启动User POC...")
    
    user_poc_dir = project_root / "demo" / "user_poc"
    if not user_poc_dir.exists():
        print("❌ user_poc目录不存在")
        return False
    
    try:
        os.chdir(user_poc_dir)
        print(f"   📁 切换到目录: {user_poc_dir}")
        
        # 启动命令
        cmd = [sys.executable, "main.py"]
        print(f"   🔧 启动命令: {' '.join(cmd)}")
        
        # 启动服务
        process = subprocess.Popen(cmd)
        print(f"   ✅ User POC已启动 (PID: {process.pid})")
        print(f"   🌐 访问地址: http://localhost:8002")
        print(f"   📚 API文档: http://localhost:8002/docs")
        
        return True
        
    except Exception as e:
        print(f"   ❌ User POC启动失败: {e}")
        return False
    finally:
        os.chdir(project_root)


def run_tests():
    """运行配置测试"""
    print("\n🧪 运行配置测试...")
    
    test_scripts = [
        ("统一配置测试", "scripts/test_unified_config.py"),
        ("系统配置测试", "scripts/test_system_config.py"),
        ("Backend配置测试", "scripts/test_backend_config.py"),
    ]
    
    for test_name, script_path in test_scripts:
        print(f"\n   📋 {test_name}...")
        try:
            result = subprocess.run([sys.executable, script_path], 
                                  capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                print(f"   ✅ {test_name}通过")
            else:
                print(f"   ❌ {test_name}失败")
                print(f"      错误: {result.stderr}")
        except subprocess.TimeoutExpired:
            print(f"   ⏰ {test_name}超时")
        except Exception as e:
            print(f"   ❌ {test_name}异常: {e}")


def show_menu():
    """显示菜单"""
    print("""
📋 可用操作:
   1. 检查系统状态
   2. 启动Backend服务
   3. 启动Topic POC
   4. 启动User POC
   5. 运行配置测试
   6. 显示配置信息
   7. 启动Docker Compose
   8. 退出

请选择操作 (1-8): """, end="")


def start_docker_compose():
    """启动Docker Compose"""
    print("\n🐳 启动Docker Compose...")
    
    docker_compose_file = project_root / "docker-compose.yml"
    if not docker_compose_file.exists():
        print("❌ docker-compose.yml不存在")
        return False
    
    try:
        cmd = ["docker-compose", "up", "-d"]
        print(f"   🔧 启动命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ Docker Compose启动成功")
            print("   🌐 服务地址:")
            print("      - Backend: http://localhost:8000")
            print("      - Frontend: http://localhost:5173")
            print("      - 数据库: localhost:5432")
            return True
        else:
            print(f"   ❌ Docker Compose启动失败: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("   ❌ docker-compose命令不可用")
        return False
    except Exception as e:
        print(f"   ❌ Docker Compose启动异常: {e}")
        return False


def main():
    """主函数"""
    print_banner()
    
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，请解决问题后重试")
        sys.exit(1)
    
    show_service_status()
    
    while True:
        show_menu()
        
        try:
            choice = input().strip()
            
            if choice == "1":
                show_service_status()
            elif choice == "2":
                start_backend()
            elif choice == "3":
                start_topic_poc()
            elif choice == "4":
                start_user_poc()
            elif choice == "5":
                run_tests()
            elif choice == "6":
                show_service_status()
            elif choice == "7":
                start_docker_compose()
            elif choice == "8":
                print("\n👋 再见！")
                break
            else:
                print("\n❌ 无效选择，请输入1-8")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 操作异常: {e}")


if __name__ == "__main__":
    main()
