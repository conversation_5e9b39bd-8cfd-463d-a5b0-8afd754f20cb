#!/usr/bin/env python3
"""
模块化架构设置脚本
用于创建新的模块化目录结构和基础文件
"""

import os
import sys
from pathlib import Path
from typing import Dict, List

def create_directory_structure():
    """创建新的模块化目录结构"""
    
    directories = [
        # 服务模块
        "services/gateway",
        "services/user", 
        "services/topic",
        "services/document",
        "services/embedding",
        "services/context",
        "services/llm",
        "services/conversation",
        "services/summary",
        
        # 共享组件
        "shared/models",
        "shared/utils", 
        "shared/config",
        "shared/exceptions",
        "shared/monitoring",
        "shared/health",
        
        # 核心引擎
        "engines/text_splitter",
        "engines/search",
        
        # 测试
        "tests/unit",
        "tests/integration",
        "tests/e2e",
        
        # 部署
        "deployment/docker",
        "deployment/k8s",
        "deployment/scripts"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        # 创建 __init__.py 文件
        init_file = Path(directory) / "__init__.py"
        if not init_file.exists():
            init_file.write_text("")
    
    print("✅ 目录结构创建完成")

def create_text_splitter_engine():
    """创建 Text-Splitter Engine 核心文件"""
    
    engine_dir = Path("engines/text_splitter")
    
    # engine.py - 主引擎类
    engine_code = '''"""
Text-Splitter Engine - 语义化文档分割引擎
基于 semantic-text-splitter 库的高级封装
"""

from typing import List, Dict, Any, Optional
from semantic_text_splitter import TextSplitter, MarkdownSplitter
from .strategies import SplitStrategy, TokenBasedStrategy, CharacterBasedStrategy
from .models import TextChunk, Document, SplitResult
from .config import TextSplitterConfig
import logging

logger = logging.getLogger(__name__)

class TextSplitterEngine:
    """文本分割引擎主类"""
    
    def __init__(self, config: Optional[TextSplitterConfig] = None):
        self.config = config or TextSplitterConfig()
        self._splitters: Dict[str, Any] = {}
        self._initialize_splitters()
    
    def _initialize_splitters(self):
        """初始化各种分割器"""
        try:
            # Token-based splitter
            self._splitters['token'] = TextSplitter.from_tiktoken_model(
                self.config.default_model, 
                self.config.default_max_tokens
            )
            
            # Character-based splitter  
            self._splitters['char'] = TextSplitter(self.config.default_max_chars)
            
            # Markdown splitter
            self._splitters['markdown'] = MarkdownSplitter(self.config.default_max_chars)
            
            logger.info("Text splitters initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize splitters: {e}")
            raise
    
    def split_text(self, text: str, strategy: SplitStrategy) -> List[TextChunk]:
        """分割文本的主方法"""
        try:
            splitter = self._get_splitter(strategy)
            raw_chunks = list(splitter.chunks(text))
            
            chunks = []
            char_offset = 0
            
            for i, chunk_text in enumerate(raw_chunks):
                chunk = TextChunk(
                    content=chunk_text,
                    chunk_index=i,
                    start_char=char_offset,
                    end_char=char_offset + len(chunk_text),
                    token_count=self._estimate_tokens(chunk_text)
                )
                chunks.append(chunk)
                char_offset += len(chunk_text)
            
            logger.info(f"Split text into {len(chunks)} chunks")
            return chunks
            
        except Exception as e:
            logger.error(f"Text splitting failed: {e}")
            raise
    
    def split_document(self, document: Document) -> SplitResult:
        """分割文档"""
        strategy = self._determine_strategy(document)
        chunks = self.split_text(document.content, strategy)
        
        return SplitResult(
            document_id=document.id,
            chunks=chunks,
            strategy_used=strategy.name,
            total_chunks=len(chunks)
        )
    
    def batch_split(self, documents: List[Document]) -> List[SplitResult]:
        """批量分割文档"""
        results = []
        for doc in documents:
            try:
                result = self.split_document(doc)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to split document {doc.id}: {e}")
                # 创建错误结果
                error_result = SplitResult(
                    document_id=doc.id,
                    chunks=[],
                    strategy_used="error",
                    total_chunks=0,
                    error=str(e)
                )
                results.append(error_result)
        
        return results
    
    def _get_splitter(self, strategy: SplitStrategy):
        """根据策略获取对应的分割器"""
        if isinstance(strategy, TokenBasedStrategy):
            return self._splitters['token']
        elif isinstance(strategy, CharacterBasedStrategy):
            return self._splitters['char']
        else:
            return self._splitters['token']  # 默认使用 token-based
    
    def _determine_strategy(self, document: Document) -> SplitStrategy:
        """根据文档类型确定分割策略"""
        if document.file_type.lower() in ['md', 'markdown']:
            return CharacterBasedStrategy(max_chars=self.config.markdown_max_chars)
        else:
            return TokenBasedStrategy(max_tokens=self.config.default_max_tokens)
    
    def _estimate_tokens(self, text: str) -> int:
        """估算文本的 token 数量"""
        # 简单估算：英文约 4 字符/token，中文约 1.5 字符/token
        return len(text) // 3
    
    def get_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        return {
            "splitters_loaded": len(self._splitters),
            "config": self.config.dict()
        }
'''
    
    (engine_dir / "engine.py").write_text(engine_code)
    
    # strategies.py - 分割策略
    strategies_code = '''"""
分割策略定义
"""

from abc import ABC, abstractmethod
from typing import Any, Dict
from pydantic import BaseModel

class SplitStrategy(BaseModel, ABC):
    """分割策略基类"""
    name: str
    
    @abstractmethod
    def get_params(self) -> Dict[str, Any]:
        """获取策略参数"""
        pass

class TokenBasedStrategy(SplitStrategy):
    """基于 Token 的分割策略"""
    name: str = "token_based"
    max_tokens: int = 1000
    model_name: str = "gpt-3.5-turbo"
    
    def get_params(self) -> Dict[str, Any]:
        return {
            "max_tokens": self.max_tokens,
            "model_name": self.model_name
        }

class CharacterBasedStrategy(SplitStrategy):
    """基于字符的分割策略"""
    name: str = "character_based"
    max_chars: int = 2000
    
    def get_params(self) -> Dict[str, Any]:
        return {
            "max_chars": self.max_chars
        }

class MarkdownStrategy(SplitStrategy):
    """Markdown 文档分割策略"""
    name: str = "markdown"
    max_chars: int = 1500
    preserve_headers: bool = True
    
    def get_params(self) -> Dict[str, Any]:
        return {
            "max_chars": self.max_chars,
            "preserve_headers": self.preserve_headers
        }
'''
    
    (engine_dir / "strategies.py").write_text(strategies_code)
    
    print("✅ Text-Splitter Engine 核心文件创建完成")

def create_shared_models():
    """创建共享数据模型"""
    
    models_dir = Path("shared/models")
    
    base_models = '''"""
共享数据模型
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class BaseEntity(BaseModel):
    """基础实体类"""
    id: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

class TextChunk(BaseEntity):
    """文本块模型"""
    content: str
    chunk_index: int
    start_char: int
    end_char: int
    token_count: Optional[int] = None
    embedding: Optional[List[float]] = None

class Document(BaseEntity):
    """文档模型"""
    title: str
    content: str
    file_type: str
    size: int
    chunks: Optional[List[TextChunk]] = None
    topic_id: Optional[str] = None

class SplitResult(BaseModel):
    """分割结果模型"""
    document_id: str
    chunks: List[TextChunk]
    strategy_used: str
    total_chunks: int
    error: Optional[str] = None

class ProcessingStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
'''
    
    (models_dir / "base.py").write_text(base_models)
    
    print("✅ 共享数据模型创建完成")

def create_test_scripts():
    """创建测试脚本"""
    
    test_engine = '''"""
Text-Splitter Engine 测试
"""

import pytest
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from engines.text_splitter.engine import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy
from shared.models.base import Document

class TestTextSplitterEngine:
    
    def setup_method(self):
        """测试前设置"""
        self.engine = TextSplitterEngine()
        self.sample_text = "这是一个测试文档。" * 100  # 创建较长的测试文本
    
    def test_engine_initialization(self):
        """测试引擎初始化"""
        assert self.engine is not None
        stats = self.engine.get_stats()
        assert stats["splitters_loaded"] > 0
    
    def test_token_based_splitting(self):
        """测试基于 Token 的分割"""
        strategy = TokenBasedStrategy(max_tokens=50)
        chunks = self.engine.split_text(self.sample_text, strategy)
        
        assert len(chunks) > 1
        assert all(chunk.token_count <= 60 for chunk in chunks)  # 允许一些误差
        assert all(chunk.content for chunk in chunks)  # 确保内容不为空
    
    def test_character_based_splitting(self):
        """测试基于字符的分割"""
        strategy = CharacterBasedStrategy(max_chars=100)
        chunks = self.engine.split_text(self.sample_text, strategy)
        
        assert len(chunks) > 1
        assert all(len(chunk.content) <= 120 for chunk in chunks)  # 允许一些误差
    
    def test_document_splitting(self):
        """测试文档分割"""
        document = Document(
            title="测试文档",
            content=self.sample_text,
            file_type="txt",
            size=len(self.sample_text)
        )
        
        result = self.engine.split_document(document)
        
        assert result.document_id == document.id
        assert result.total_chunks > 0
        assert len(result.chunks) == result.total_chunks
    
    def test_batch_splitting(self):
        """测试批量分割"""
        documents = [
            Document(title=f"文档{i}", content=self.sample_text, file_type="txt", size=len(self.sample_text))
            for i in range(3)
        ]
        
        results = self.engine.batch_split(documents)
        
        assert len(results) == 3
        assert all(result.total_chunks > 0 for result in results)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
'''
    
    (Path("tests/unit") / "test_text_splitter_engine.py").write_text(test_engine)
    
    print("✅ 测试脚本创建完成")

def main():
    """主函数"""
    print("🚀 开始设置模块化架构...")
    
    try:
        create_directory_structure()
        create_text_splitter_engine()
        create_shared_models()
        create_test_scripts()
        
        print("\n✅ 模块化架构设置完成！")
        print("\n📋 下一步操作：")
        print("1. 运行测试：python -m pytest tests/unit/test_text_splitter_engine.py -v")
        print("2. 查看架构文档：docs/MODULAR_ARCHITECTURE.md")
        print("3. 开始开发各个服务模块")
        
    except Exception as e:
        print(f"❌ 设置过程中出现错误：{e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
