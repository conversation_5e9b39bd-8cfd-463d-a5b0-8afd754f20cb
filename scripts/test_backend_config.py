#!/usr/bin/env python3
"""
Backend配置测试脚本

专门测试backend配置的加载和验证。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault("PROJECT_NAME", "Master-Know")
os.environ.setdefault("POSTGRES_SERVER", "db")
os.environ.setdefault("POSTGRES_USER", "master_know_user")
os.environ.setdefault("POSTGRES_PASSWORD", "changethis")
os.environ.setdefault("POSTGRES_DB", "master_know")
os.environ.setdefault("FIRST_SUPERUSER", "<EMAIL>")
os.environ.setdefault("FIRST_SUPERUSER_PASSWORD", "changethis")

def test_backend_config():
    """测试backend配置"""
    print("🧪 测试backend配置...")
    
    try:
        # 添加backend路径
        backend_path = project_root / "backend"
        sys.path.insert(0, str(backend_path))
        
        # 导入backend配置
        from app.core.config import Settings, settings
        
        print("   ✅ backend配置模块导入成功")
        
        # 测试配置实例
        print(f"   ✅ 项目名称: {settings.PROJECT_NAME}")
        print(f"   ✅ 环境: {settings.ENVIRONMENT}")
        print(f"   ✅ 数据库服务器: {settings.POSTGRES_SERVER}")
        print(f"   ✅ 数据库用户: {settings.POSTGRES_USER}")
        print(f"   ✅ 数据库名: {settings.POSTGRES_DB}")
        print(f"   ✅ 数据库URL: {settings.SQLALCHEMY_DATABASE_URI}")
        print(f"   ✅ 前端主机: {settings.FRONTEND_HOST}")
        print(f"   ✅ CORS源: {len(settings.all_cors_origins)}个")
        
        # 测试向量化配置
        print(f"   ✅ 向量化模型: {settings.EMBEDDING_DEFAULT_MODEL}")
        print(f"   ✅ 向量维度: {settings.EMBEDDING_DIM}")
        
        # 测试Manticore配置
        print(f"   ✅ Manticore HTTP URL: {settings.MANTICORE_HTTP_URL}")
        print(f"   ✅ Manticore MySQL URL: {settings.MANTICORE_MYSQL_URL}")
        
        # 测试LLM配置
        print(f"   ✅ LLM模型: {settings.LLM_OPENAI_MODEL}")
        print(f"   ✅ LLM最大令牌: {settings.LLM_MAX_TOKENS}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ backend配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_env_file_loading():
    """测试.env文件加载"""
    print("🧪 测试.env文件加载...")
    
    try:
        env_file = project_root / ".env"
        if not env_file.exists():
            print(f"   ⚠️  .env文件不存在: {env_file}")
            return False
        
        # 读取.env文件
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键配置
        required_vars = [
            "PROJECT_NAME=Master-Know",
            "POSTGRES_SERVER=db",
            "POSTGRES_USER=master_know_user",
            "POSTGRES_DB=master_know",
            "FIRST_SUPERUSER=<EMAIL>",
        ]
        
        for var in required_vars:
            if var in content:
                print(f"   ✅ 找到配置: {var}")
            else:
                print(f"   ❌ 缺少配置: {var}")
                return False
        
        print(f"   ✅ .env文件包含{len(content.splitlines())}行配置")
        return True
        
    except Exception as e:
        print(f"   ❌ .env文件加载测试失败: {e}")
        return False


def test_unified_config_integration():
    """测试统一配置集成"""
    print("🧪 测试统一配置集成...")
    
    try:
        from scripts.config.unified_config import get_unified_settings
        
        unified_settings = get_unified_settings()
        
        print(f"   ✅ 统一配置项目名称: {unified_settings.project_name}")
        print(f"   ✅ 统一配置数据库URL: {unified_settings.database_url}")
        print(f"   ✅ 统一配置Redis URL: {unified_settings.redis_url}")
        
        # 检查backend适配器
        adapter_path = project_root / "backend" / "app" / "core" / "unified_config_adapter.py"
        if adapter_path.exists():
            print(f"   ✅ backend配置适配器存在: {adapter_path}")
        else:
            print(f"   ⚠️  backend配置适配器不存在: {adapter_path}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 统一配置集成测试失败: {e}")
        return False


def run_backend_tests():
    """运行backend测试"""
    print("🚀 开始backend配置测试...")
    print("=" * 50)
    
    tests = [
        ("环境变量设置", lambda: True),  # 已在开头设置
        (".env文件加载", test_env_file_loading),
        ("统一配置集成", test_unified_config_integration),
        ("backend配置", test_backend_config),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"   ✅ {test_name}通过")
            else:
                failed += 1
                print(f"   ❌ {test_name}失败")
        except Exception as e:
            failed += 1
            print(f"   ❌ {test_name}异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 backend配置测试全部通过！")
    else:
        print("⚠️  部分backend配置测试失败")
    
    return failed == 0


if __name__ == "__main__":
    success = run_backend_tests()
    sys.exit(0 if success else 1)
