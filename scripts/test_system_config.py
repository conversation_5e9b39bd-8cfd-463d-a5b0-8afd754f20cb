#!/usr/bin/env python3
"""
全系统配置测试脚本

测试整个Master-Know系统的配置一致性和正确性。
"""

import os
import sys
import importlib.util
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.config.unified_config import get_unified_settings, get_service_config


def test_backend_config():
    """测试backend配置"""
    print("🧪 测试backend配置...")
    
    try:
        # 导入backend配置
        backend_config_path = project_root / "backend" / "app" / "core" / "config.py"
        if not backend_config_path.exists():
            print("   ⚠️  backend配置文件不存在")
            return False
        
        # 动态导入backend配置
        spec = importlib.util.spec_from_file_location("backend_config", backend_config_path)
        backend_config = importlib.util.module_from_spec(spec)
        
        # 临时设置环境变量以避免导入错误
        os.environ.setdefault("PROJECT_NAME", "Master-Know")
        os.environ.setdefault("FIRST_SUPERUSER", "<EMAIL>")
        
        spec.loader.exec_module(backend_config)
        
        settings = backend_config.Settings()
        
        # 验证关键配置
        assert settings.PROJECT_NAME == "Master-Know"
        assert settings.ENVIRONMENT in ["local", "staging", "production"]
        assert settings.POSTGRES_PORT == 5432
        
        print("   ✅ backend配置加载成功")
        print(f"      项目名称: {settings.PROJECT_NAME}")
        print(f"      环境: {settings.ENVIRONMENT}")
        print(f"      数据库: {settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ backend配置测试失败: {e}")
        return False


def test_topic_service_config():
    """测试topic服务配置"""
    print("🧪 测试topic服务配置...")
    
    try:
        # 导入topic服务配置
        topic_config_path = project_root / "services" / "topic" / "utils" / "config.py"
        if not topic_config_path.exists():
            print("   ⚠️  topic服务配置文件不存在")
            return False
        
        # 添加services路径
        services_path = project_root / "services"
        if str(services_path) not in sys.path:
            sys.path.insert(0, str(services_path))
        
        # 动态导入topic配置
        spec = importlib.util.spec_from_file_location("topic_config", topic_config_path)
        topic_config = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(topic_config)
        
        print("   ✅ topic服务配置加载成功")
        return True
        
    except Exception as e:
        print(f"   ❌ topic服务配置测试失败: {e}")
        return False


def test_port_consistency():
    """测试端口一致性"""
    print("🧪 测试端口一致性...")
    
    try:
        unified_settings = get_unified_settings()
        
        # 定义期望的端口分配
        expected_ports = {
            "gateway": 9000,
            "embedding": 9001,
            "user": 9002,
            "document": 9003,
            "topic": 9004,
            "llm": 9005,
            "conversation": 9006,
            "summary": 9007,
            "manticore": 9008,
        }
        
        # 验证端口分配
        actual_ports = {
            "gateway": unified_settings.gateway_service_port,
            "embedding": unified_settings.embedding_service_port,
            "user": unified_settings.user_service_port,
            "document": unified_settings.document_service_port,
            "topic": unified_settings.topic_service_port,
            "llm": unified_settings.llm_service_port,
            "conversation": unified_settings.conversation_service_port,
            "summary": unified_settings.summary_service_port,
            "manticore": unified_settings.manticore_service_port,
        }
        
        for service, expected_port in expected_ports.items():
            actual_port = actual_ports[service]
            if actual_port != expected_port:
                print(f"   ❌ {service}服务端口不一致: 期望{expected_port}, 实际{actual_port}")
                return False
            else:
                print(f"   ✅ {service}服务端口: {actual_port}")
        
        # 检查端口唯一性
        port_values = list(actual_ports.values())
        if len(port_values) != len(set(port_values)):
            print("   ❌ 发现重复端口分配")
            return False
        
        print("   ✅ 端口分配一致性验证通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 端口一致性测试失败: {e}")
        return False


def test_service_config_generation():
    """测试服务配置生成"""
    print("🧪 测试服务配置生成...")
    
    try:
        services = ["embedding", "llm", "document"]
        
        for service in services:
            config = get_service_config(service)
            
            # 验证基础配置
            required_keys = ["project_name", "environment", "database_url", "redis_url", "api_port"]
            for key in required_keys:
                if key not in config:
                    print(f"   ❌ {service}服务配置缺少{key}")
                    return False
            
            print(f"   ✅ {service}服务配置生成成功 (端口: {config['api_port']})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 服务配置生成测试失败: {e}")
        return False


def test_environment_variables():
    """测试环境变量"""
    print("🧪 测试环境变量...")
    
    try:
        unified_settings = get_unified_settings()
        
        # 测试关键环境变量
        env_tests = [
            ("PROJECT_NAME", unified_settings.project_name, "Master-Know"),
            ("ENVIRONMENT", unified_settings.environment, "local"),
            ("POSTGRES_PORT", str(unified_settings.postgres_port), "5432"),
            ("REDIS_PORT", str(unified_settings.redis_port), "6379"),
        ]
        
        for env_var, actual, expected in env_tests:
            if actual != expected:
                print(f"   ⚠️  {env_var}: 期望'{expected}', 实际'{actual}'")
            else:
                print(f"   ✅ {env_var}: {actual}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 环境变量测试失败: {e}")
        return False


def test_demo_poc_compatibility():
    """测试demo POC兼容性"""
    print("🧪 测试demo POC兼容性...")
    
    try:
        unified_settings = get_unified_settings()
        
        # 检查POC模块需要的配置
        poc_configs = {
            "llm_poc": {
                "port": unified_settings.llm_service_port,
                "openai_key": unified_settings.llm_openai_api_key,
                "model": unified_settings.llm_openai_model,
            },
            "embedding_poc": {
                "port": unified_settings.embedding_service_port,
                "openai_key": unified_settings.embedding_openai_api_key,
                "model": unified_settings.embedding_default_model,
            },
            "topic_poc": {
                "port": unified_settings.topic_service_port,
                "database_url": unified_settings.database_url,
            },
            "user_poc": {
                "port": unified_settings.user_service_port,
                "secret_key": unified_settings.secret_key,
            }
        }
        
        for poc, config in poc_configs.items():
            print(f"   ✅ {poc}配置兼容性: 端口{config['port']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ demo POC兼容性测试失败: {e}")
        return False


def test_docker_compose_compatibility():
    """测试Docker Compose兼容性"""
    print("🧪 测试Docker Compose兼容性...")
    
    try:
        unified_settings = get_unified_settings()
        
        # 检查Docker相关配置
        docker_configs = {
            "postgres_server": unified_settings.postgres_server,
            "redis_host": unified_settings.redis_host,
            "manticore_host": unified_settings.manticore_host,
        }
        
        for service, host in docker_configs.items():
            print(f"   ✅ {service}: {host}")
        
        # 验证服务主机名符合Docker Compose约定
        expected_hosts = ["db", "redis", "manticore"]
        actual_hosts = [
            unified_settings.postgres_server,
            unified_settings.redis_host,
            unified_settings.manticore_host,
        ]
        
        for expected, actual in zip(expected_hosts, actual_hosts):
            if actual != expected:
                print(f"   ⚠️  主机名不匹配: 期望'{expected}', 实际'{actual}'")
            else:
                print(f"   ✅ 主机名匹配: {actual}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Docker Compose兼容性测试失败: {e}")
        return False


def run_system_tests():
    """运行全系统测试"""
    print("🚀 开始全系统配置测试...")
    print("=" * 60)
    
    tests = [
        ("统一配置基础功能", lambda: get_unified_settings() is not None),
        ("backend配置", test_backend_config),
        ("topic服务配置", test_topic_service_config),
        ("端口一致性", test_port_consistency),
        ("服务配置生成", test_service_config_generation),
        ("环境变量", test_environment_variables),
        ("demo POC兼容性", test_demo_poc_compatibility),
        ("Docker Compose兼容性", test_docker_compose_compatibility),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"   ✅ {test_name}通过")
            else:
                failed += 1
                print(f"   ❌ {test_name}失败")
        except Exception as e:
            failed += 1
            print(f"   ❌ {test_name}异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 全系统配置测试通过！")
        print("\n📝 系统配置状态:")
        
        settings = get_unified_settings()
        print(f"   • 项目: {settings.project_name}")
        print(f"   • 环境: {settings.environment}")
        print(f"   • 服务端口: 8000-8008")
        print(f"   • 数据库: {settings.postgres_server}:{settings.postgres_port}")
        print(f"   • 缓存: {settings.redis_host}:{settings.redis_port}")
        print(f"   • 搜索: {settings.manticore_host}:{settings.manticore_http_port}")
        
        print("\n🚀 可以开始启动服务进行集成测试！")
        
    else:
        print("⚠️  部分测试失败，请检查配置问题")
    
    return failed == 0


if __name__ == "__main__":
    success = run_system_tests()
    sys.exit(0 if success else 1)
