#!/usr/bin/env python3
"""
系统功能测试脚本

测试Master-Know系统各个功能模块的实际工作情况。
"""

import os
import sys
import time
import asyncio
import subprocess
import requests
import json
from pathlib import Path
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.config.unified_config import get_unified_settings


class SystemTester:
    """系统功能测试器"""
    
    def __init__(self):
        self.settings = get_unified_settings()
        self.test_results = {}
        self.running_services = {}
        
    def test_openai_api_connection(self) -> bool:
        """测试OpenAI API连接"""
        print("🧪 测试OpenAI API连接...")
        
        try:
            import openai
            
            # 测试向量化API
            if self.settings.embedding_openai_api_key:
                client = openai.OpenAI(
                    api_key=self.settings.embedding_openai_api_key,
                    base_url=self.settings.embedding_openai_base_url
                )
                
                response = client.embeddings.create(
                    model=self.settings.embedding_default_model,
                    input="测试文本"
                )
                
                if response.data and len(response.data) > 0:
                    embedding_dim = len(response.data[0].embedding)
                    print(f"   ✅ 向量化API连接成功，维度: {embedding_dim}")
                else:
                    print("   ❌ 向量化API响应异常")
                    return False
            else:
                print("   ⚠️  向量化API密钥未设置")
                return False
            
            # 测试LLM API
            if self.settings.llm_openai_api_key:
                client = openai.OpenAI(
                    api_key=self.settings.llm_openai_api_key,
                    base_url=self.settings.llm_openai_base_url
                )
                
                response = client.chat.completions.create(
                    model=self.settings.llm_openai_model,
                    messages=[{"role": "user", "content": "Hello"}],
                    max_tokens=10
                )
                
                if response.choices and len(response.choices) > 0:
                    print(f"   ✅ LLM API连接成功，模型: {self.settings.llm_openai_model}")
                else:
                    print("   ❌ LLM API响应异常")
                    return False
            else:
                print("   ⚠️  LLM API密钥未设置")
                return False
            
            return True
            
        except ImportError:
            print("   ❌ OpenAI库未安装: pip install openai")
            return False
        except Exception as e:
            print(f"   ❌ OpenAI API测试失败: {e}")
            return False
    
    def start_service(self, service_name: str, service_dir: Path, start_command: List[str]) -> bool:
        """启动服务"""
        print(f"🚀 启动{service_name}...")
        
        try:
            if not service_dir.exists():
                print(f"   ❌ 服务目录不存在: {service_dir}")
                return False
            
            # 切换到服务目录
            original_cwd = os.getcwd()
            os.chdir(service_dir)
            
            # 启动服务
            process = subprocess.Popen(
                start_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待服务启动
            time.sleep(3)
            
            if process.poll() is None:
                self.running_services[service_name] = process
                print(f"   ✅ {service_name}启动成功 (PID: {process.pid})")
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"   ❌ {service_name}启动失败")
                print(f"      错误: {stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ {service_name}启动异常: {e}")
            return False
        finally:
            os.chdir(original_cwd)
    
    def test_service_health(self, service_name: str, port: int, path: str = "/") -> bool:
        """测试服务健康状态"""
        print(f"🏥 测试{service_name}健康状态...")
        
        try:
            url = f"http://localhost:{port}{path}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"   ✅ {service_name}健康检查通过")
                return True
            else:
                print(f"   ❌ {service_name}健康检查失败: HTTP {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ {service_name}连接失败 (端口 {port})")
            return False
        except Exception as e:
            print(f"   ❌ {service_name}健康检查异常: {e}")
            return False
    
    def test_topic_poc_functionality(self) -> bool:
        """测试Topic POC功能"""
        print("🧪 测试Topic POC功能...")
        
        try:
            base_url = f"http://localhost:{self.settings.topic_service_port}"
            
            # 测试创建主题
            create_data = {
                "title": "测试主题",
                "description": "这是一个测试主题",
                "user_id": "test_user"
            }
            
            response = requests.post(
                f"{base_url}/api/v1/topics",
                json=create_data,
                timeout=10
            )
            
            if response.status_code == 200:
                topic_data = response.json()
                topic_id = topic_data.get("id")
                print(f"   ✅ 创建主题成功: {topic_id}")
                
                # 测试获取主题
                response = requests.get(f"{base_url}/api/v1/topics/{topic_id}", timeout=10)
                if response.status_code == 200:
                    print("   ✅ 获取主题成功")
                    return True
                else:
                    print(f"   ❌ 获取主题失败: HTTP {response.status_code}")
                    return False
            else:
                print(f"   ❌ 创建主题失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Topic POC功能测试失败: {e}")
            return False
    
    def test_user_poc_functionality(self) -> bool:
        """测试User POC功能"""
        print("🧪 测试User POC功能...")
        
        try:
            base_url = f"http://localhost:{self.settings.user_service_port}"
            
            # 测试用户注册
            register_data = {
                "username": "testuser",
                "email": "<EMAIL>",
                "password": "testpass123"
            }
            
            response = requests.post(
                f"{base_url}/api/v1/auth/register",
                json=register_data,
                timeout=10
            )
            
            if response.status_code == 200:
                print("   ✅ 用户注册成功")
                
                # 测试用户登录
                login_data = {
                    "username": "testuser",
                    "password": "testpass123"
                }
                
                response = requests.post(
                    f"{base_url}/api/v1/auth/login",
                    json=login_data,
                    timeout=10
                )
                
                if response.status_code == 200:
                    token_data = response.json()
                    print("   ✅ 用户登录成功")
                    return True
                else:
                    print(f"   ❌ 用户登录失败: HTTP {response.status_code}")
                    return False
            else:
                print(f"   ❌ 用户注册失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ User POC功能测试失败: {e}")
            return False
    
    def test_llm_poc_functionality(self) -> bool:
        """测试LLM POC功能"""
        print("🧪 测试LLM POC功能...")
        
        try:
            base_url = f"http://localhost:{self.settings.llm_service_port}"
            
            # 测试LLM生成
            generate_data = {
                "prompt": "请简单介绍一下人工智能",
                "max_tokens": 100
            }
            
            response = requests.post(
                f"{base_url}/api/v1/generate",
                json=generate_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result.get("text", "")
                if generated_text:
                    print(f"   ✅ LLM生成成功: {generated_text[:50]}...")
                    return True
                else:
                    print("   ❌ LLM生成结果为空")
                    return False
            else:
                print(f"   ❌ LLM生成失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ LLM POC功能测试失败: {e}")
            return False
    
    def cleanup_services(self):
        """清理服务进程"""
        print("\n🧹 清理服务进程...")
        
        for service_name, process in self.running_services.items():
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"   ✅ {service_name}已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"   ⚠️  {service_name}强制停止")
            except Exception as e:
                print(f"   ❌ 停止{service_name}失败: {e}")
    
    def run_comprehensive_test(self) -> bool:
        """运行综合测试"""
        print("🚀 开始系统功能综合测试...")
        print("=" * 60)
        
        test_results = {}
        
        # 1. 测试OpenAI API连接
        test_results["openai_api"] = self.test_openai_api_connection()
        
        # 2. 启动并测试Topic POC
        if self.start_service(
            "Topic POC",
            project_root / "demo" / "topic_poc",
            [sys.executable, "main.py"]
        ):
            time.sleep(2)
            test_results["topic_health"] = self.test_service_health("Topic POC", self.settings.topic_service_port, "/docs")
            if test_results["topic_health"]:
                test_results["topic_functionality"] = self.test_topic_poc_functionality()
        
        # 3. 启动并测试User POC
        if self.start_service(
            "User POC",
            project_root / "demo" / "user_poc",
            [sys.executable, "main.py"]
        ):
            time.sleep(2)
            test_results["user_health"] = self.test_service_health("User POC", self.settings.user_service_port, "/docs")
            if test_results["user_health"]:
                test_results["user_functionality"] = self.test_user_poc_functionality()
        
        # 4. 启动并测试LLM POC
        if self.start_service(
            "LLM POC",
            project_root / "demo" / "llm_poc",
            [sys.executable, "main.py"]
        ):
            time.sleep(2)
            test_results["llm_health"] = self.test_service_health("LLM POC", self.settings.llm_service_port, "/docs")
            if test_results["llm_health"]:
                test_results["llm_functionality"] = self.test_llm_poc_functionality()
        
        # 统计结果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        # 显示详细结果
        print("\n📋 详细结果:")
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
        
        if passed == total:
            print("\n🎉 所有功能测试通过！系统工作正常。")
        else:
            print(f"\n⚠️  {total - passed}个测试失败，请检查相关功能。")
        
        # 清理服务
        self.cleanup_services()
        
        return passed == total


def main():
    """主函数"""
    tester = SystemTester()
    
    try:
        success = tester.run_comprehensive_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        tester.cleanup_services()
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        tester.cleanup_services()
        sys.exit(1)


if __name__ == "__main__":
    main()
