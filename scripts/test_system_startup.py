#!/usr/bin/env python3
"""
系统启动测试脚本

测试各个服务的启动能力和配置正确性。
"""

import os
import sys
import time
import subprocess
import signal
from pathlib import Path
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.config.unified_config import get_unified_settings


class ServiceTester:
    """服务测试器"""
    
    def __init__(self):
        self.settings = get_unified_settings()
        self.processes: Dict[str, subprocess.Popen] = {}
        
    def test_backend_startup(self) -> bool:
        """测试backend启动"""
        print("🧪 测试backend启动...")
        
        try:
            backend_dir = project_root / "backend"
            if not backend_dir.exists():
                print("   ⚠️  backend目录不存在")
                return False
            
            # 检查backend依赖
            requirements_file = backend_dir / "requirements.txt"
            if requirements_file.exists():
                print(f"   ✅ 找到requirements.txt")
            
            # 检查main.py
            main_file = backend_dir / "app" / "main.py"
            if main_file.exists():
                print(f"   ✅ 找到main.py")
            else:
                print(f"   ⚠️  main.py不存在")
                return False
            
            # 测试配置导入
            os.chdir(backend_dir)
            result = subprocess.run([
                sys.executable, "-c", 
                "from app.core.config import settings; print(f'Backend配置加载成功: {settings.PROJECT_NAME}')"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"   ✅ backend配置测试: {result.stdout.strip()}")
                return True
            else:
                print(f"   ❌ backend配置测试失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ backend启动测试失败: {e}")
            return False
        finally:
            os.chdir(project_root)
    
    def test_topic_service_startup(self) -> bool:
        """测试topic服务启动"""
        print("🧪 测试topic服务启动...")
        
        try:
            topic_dir = project_root / "services" / "topic"
            if not topic_dir.exists():
                print("   ⚠️  topic服务目录不存在")
                return False
            
            # 检查topic服务文件
            main_files = [
                topic_dir / "start_api.py",
                topic_dir / "run_service.py",
            ]
            
            main_file = None
            for file in main_files:
                if file.exists():
                    main_file = file
                    print(f"   ✅ 找到启动文件: {file.name}")
                    break
            
            if not main_file:
                print("   ⚠️  未找到topic服务启动文件")
                return False
            
            # 测试配置导入
            os.chdir(topic_dir)
            result = subprocess.run([
                sys.executable, "-c", 
                "from utils.config import get_settings; s = get_settings(); print(f'Topic服务配置加载成功: 端口{s.api_port}')"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"   ✅ topic服务配置测试: {result.stdout.strip()}")
                return True
            else:
                print(f"   ❌ topic服务配置测试失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ topic服务启动测试失败: {e}")
            return False
        finally:
            os.chdir(project_root)
    
    def test_demo_poc_startup(self) -> bool:
        """测试demo POC启动"""
        print("🧪 测试demo POC启动...")
        
        try:
            demo_dir = project_root / "demo"
            poc_modules = ["topic_poc", "user_poc", "llm_poc"]
            
            success_count = 0
            
            for module in poc_modules:
                module_dir = demo_dir / module
                if not module_dir.exists():
                    print(f"   ⚠️  {module}目录不存在")
                    continue
                
                main_file = module_dir / "main.py"
                if not main_file.exists():
                    print(f"   ⚠️  {module}/main.py不存在")
                    continue
                
                # 测试POC配置导入
                os.chdir(module_dir)
                result = subprocess.run([
                    sys.executable, "-c", 
                    f"print('{module}配置测试通过')"
                ], capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    print(f"   ✅ {module}: {result.stdout.strip()}")
                    success_count += 1
                else:
                    print(f"   ❌ {module}测试失败: {result.stderr}")
            
            return success_count >= 2  # 至少2个POC能启动
            
        except Exception as e:
            print(f"   ❌ demo POC启动测试失败: {e}")
            return False
        finally:
            os.chdir(project_root)
    
    def test_docker_compose_config(self) -> bool:
        """测试Docker Compose配置"""
        print("🧪 测试Docker Compose配置...")
        
        try:
            docker_compose_file = project_root / "docker-compose.yml"
            if not docker_compose_file.exists():
                print("   ⚠️  docker-compose.yml不存在")
                return False
            
            # 检查Docker Compose文件语法
            result = subprocess.run([
                "docker-compose", "config"
            ], capture_output=True, text=True, timeout=30, cwd=project_root)
            
            if result.returncode == 0:
                print("   ✅ Docker Compose配置语法正确")
                
                # 检查服务定义
                config_output = result.stdout
                services = ["db", "redis", "backend"]
                
                for service in services:
                    if service in config_output:
                        print(f"   ✅ 找到服务定义: {service}")
                    else:
                        print(f"   ⚠️  缺少服务定义: {service}")
                
                return True
            else:
                print(f"   ❌ Docker Compose配置错误: {result.stderr}")
                return False
                
        except FileNotFoundError:
            print("   ⚠️  docker-compose命令不可用")
            return False
        except Exception as e:
            print(f"   ❌ Docker Compose配置测试失败: {e}")
            return False
    
    def test_environment_consistency(self) -> bool:
        """测试环境一致性"""
        print("🧪 测试环境一致性...")
        
        try:
            # 检查.env文件
            env_file = project_root / ".env"
            if not env_file.exists():
                print("   ❌ .env文件不存在")
                return False
            
            # 检查统一配置
            settings = self.settings
            
            # 验证关键配置
            checks = [
                ("项目名称", settings.project_name, "Master-Know"),
                ("环境", settings.environment, "local"),
                ("数据库端口", settings.postgres_port, 5432),
                ("Redis端口", settings.redis_port, 6379),
                ("Manticore HTTP端口", settings.manticore_http_port, 9308),
            ]
            
            for name, actual, expected in checks:
                if actual == expected:
                    print(f"   ✅ {name}: {actual}")
                else:
                    print(f"   ⚠️  {name}不匹配: 期望{expected}, 实际{actual}")
            
            # 检查端口分配
            ports = [
                settings.gateway_service_port,
                settings.embedding_service_port,
                settings.user_service_port,
                settings.document_service_port,
                settings.topic_service_port,
                settings.llm_service_port,
                settings.conversation_service_port,
                settings.summary_service_port,
                settings.manticore_service_port,
            ]
            
            if len(ports) == len(set(ports)):
                print(f"   ✅ 端口分配无冲突: {min(ports)}-{max(ports)}")
            else:
                print(f"   ❌ 端口分配有冲突: {ports}")
                return False
            
            return True
            
        except Exception as e:
            print(f"   ❌ 环境一致性测试失败: {e}")
            return False
    
    def run_startup_tests(self) -> bool:
        """运行启动测试"""
        print("🚀 开始系统启动测试...")
        print("=" * 60)
        
        tests = [
            ("环境一致性", self.test_environment_consistency),
            ("Docker Compose配置", self.test_docker_compose_config),
            ("backend启动", self.test_backend_startup),
            ("topic服务启动", self.test_topic_service_startup),
            ("demo POC启动", self.test_demo_poc_startup),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            print(f"\n📋 {test_name}...")
            try:
                if test_func():
                    passed += 1
                    print(f"   ✅ {test_name}通过")
                else:
                    failed += 1
                    print(f"   ❌ {test_name}失败")
            except Exception as e:
                failed += 1
                print(f"   ❌ {test_name}异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed} 通过, {failed} 失败")
        
        if failed == 0:
            print("🎉 系统启动测试全部通过！")
            print("\n🚀 系统已准备就绪，可以启动服务：")
            print("   • Backend: cd backend && uvicorn app.main:app --reload")
            print("   • Topic服务: cd services/topic && python start_api.py")
            print("   • Demo POC: cd demo/topic_poc && python main.py")
            print("   • Docker: docker-compose up")
            
        else:
            print("⚠️  部分启动测试失败，请检查配置")
        
        return failed == 0


def main():
    """主函数"""
    tester = ServiceTester()
    success = tester.run_startup_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
