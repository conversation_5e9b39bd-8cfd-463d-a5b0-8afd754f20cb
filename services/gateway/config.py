from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # BFF Configuration
    bff_host: str = "0.0.0.0"
    bff_port: int = 9000
    bff_debug: bool = False
    
    # JWT Authentication
    auth_jwt_secret: str = "your-super-secret-jwt-key-change-in-production"
    auth_jwt_algorithm: str = "HS256"
    auth_jwt_expire_minutes: int = 30
    
    # Rate Limiting
    rate_limit_per_min: int = 120
    rate_limit_burst: int = 10
    
    # Backend Services URLs
    backend_service_url: str = "http://backend:9000"
    user_service_url: str = "http://backend:9000"
    document_service_url: str = "http://backend:9000"
    topic_service_url: str = "http://backend:9000"
    llm_service_url: str = "http://backend:9000"
    conversation_service_url: str = "http://backend:9000"
    embedding_service_url: str = "http://backend:9000"
    
    # Redis Configuration
    redis_url: str = "redis://redis:6379/0"
    
    # Traefik Configuration
    domain: str = "localhost"
    traefik_public_network: str = "traefik-public"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()
