#!/usr/bin/env python3
"""
Gateway 健康检查脚本
使用 Python 而不是 curl 进行健康检查
"""

import urllib.request
import sys

def check_health():
    try:
        response = urllib.request.urlopen('http://localhost:8000/health', timeout=5)
        if response.getcode() == 200:
            return True
        else:
            return False
    except Exception:
        return False

if __name__ == "__main__":
    if check_health():
        sys.exit(0)
    else:
        sys.exit(1)
