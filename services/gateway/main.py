from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from slowapi.errors import RateLimitExceeded
from typing import Dict, Any, List, Optional
import asyncio
import logging
import json
import httpx

from config import settings
from auth import get_current_user, get_optional_user, create_access_token
from rate_limiter import limiter, custom_rate_limit_handler
from http_client import service_client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Master-Know BFF Gateway",
    description="Backend for Frontend Gateway for Master-Know AI Learning System",
    version="1.0.0",
    debug=settings.bff_debug
)

# Add rate limiter to app
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, custom_rate_limit_handler)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",  # Frontend dev server
        "http://localhost:3000",  # Alternative frontend port
        "http://localhost:8080",  # Adminer
    ],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "service": "Master-Know BFF Gateway",
        "status": "healthy",
        "version": "1.0.0"
    }


@app.get("/health")
@limiter.exempt
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "services": {
            "bff": "up",
            "redis": "up" if limiter else "down",
        },
        "timestamp": "2025-08-15T00:00:00Z"
    }


# Authentication endpoints
@app.post("/api/v1/auth/login")
@limiter.limit("10/minute")
async def login(request: Request, credentials: Dict[str, str]):
    """Login endpoint - creates JWT token"""
    username = credentials.get("username")
    password = credentials.get("password")
    
    if not username or not password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username and password required"
        )
    
    # For POC, use simple validation (in production, validate against User Service)
    if username == "demo" and password == "demo123":
        token_data = {"sub": "demo-user", "username": username}
        access_token = create_access_token(token_data)
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {"id": "demo-user", "username": username}
        }
    
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid credentials"
    )


@app.get("/api/v1/auth/me")
@limiter.limit("60/minute")
async def get_current_user_info(request: Request, current_user: Dict[str, Any] = Depends(get_current_user)):
    """Get current user information"""
    return {"user": current_user}


# Service aggregation endpoints
@app.get("/api/v1/aggregate/topic/{topic_id}")
@limiter.limit("30/minute")
async def aggregate_topic_data(
    request: Request,
    topic_id: str,
    current_user: Optional[Dict[str, Any]] = Depends(get_optional_user)
):
    """Aggregate data from multiple services for a specific topic"""
    
    # Prepare concurrent requests to different services
    requests = [
        {
            "method": "GET",
            "url": f"{settings.topic_service_url}/api/v1/topics/{topic_id}",
            "service": "topic"
        },
        {
            "method": "GET", 
            "url": f"{settings.document_service_url}/api/v1/topics/{topic_id}/documents",
            "service": "document"
        },
        {
            "method": "GET",
            "url": f"{settings.conversation_service_url}/api/v1/topics/{topic_id}/conversations",
            "service": "conversation"
        }
    ]
    
    # Add auth headers if user is authenticated
    if current_user:
        auth_header = {"Authorization": f"Bearer {request.headers.get('Authorization', '').replace('Bearer ', '')}"}
        for req in requests:
            req["headers"] = auth_header
    
    try:
        results = await service_client.aggregate_requests(requests)
        
        # Process and structure the aggregated response
        aggregated_data = {
            "topic_id": topic_id,
            "timestamp": "2025-08-15T00:00:00Z",
            "services": {}
        }
        
        for i, result in enumerate(results):
            service_name = requests[i]["service"]
            if result.get("error"):
                aggregated_data["services"][service_name] = {
                    "status": "error",
                    "message": result.get("message", "Unknown error")
                }
            else:
                aggregated_data["services"][service_name] = {
                    "status": "success",
                    "data": result.get("data", {}),
                    "status_code": result.get("status_code", 200)
                }
        
        return aggregated_data
        
    except Exception as e:
        logger.error(f"Aggregation failed for topic {topic_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service aggregation failed: {str(e)}"
        )


@app.post("/api/v1/aggregate/llm/generate")
@limiter.limit("20/minute")
async def aggregate_llm_generation(
    request: Request,
    payload: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Aggregate LLM generation with context from multiple services"""
    
    topic_id = payload.get("topic_id")
    prompt = payload.get("prompt")
    
    if not topic_id or not prompt:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="topic_id and prompt are required"
        )
    
    # First, get context from multiple services
    context_requests = [
        {
            "method": "GET",
            "url": f"{settings.topic_service_url}/api/v1/topics/{topic_id}/context",
            "service": "topic_context"
        },
        {
            "method": "GET",
            "url": f"{settings.document_service_url}/api/v1/topics/{topic_id}/relevant_chunks",
            "params": {"query": prompt[:100]},  # Use first 100 chars as query
            "service": "document_context"
        }
    ]
    
    auth_header = {"Authorization": f"Bearer {request.headers.get('Authorization', '').replace('Bearer ', '')}"}
    for req in context_requests:
        req["headers"] = auth_header
    
    try:
        # Get context from services
        context_results = await service_client.aggregate_requests(context_requests)
        
        # Prepare enhanced prompt with context
        enhanced_payload = payload.copy()
        enhanced_payload["context"] = {}
        
        for i, result in enumerate(context_results):
            service_name = context_requests[i]["service"]
            if not result.get("error"):
                enhanced_payload["context"][service_name] = result.get("data", {})
        
        # Send to LLM service
        llm_response = await service_client.post(
            f"{settings.llm_service_url}/api/v1/generate",
            json_data=enhanced_payload,
            headers=auth_header
        )
        
        return {
            "response": llm_response,
            "context_used": enhanced_payload["context"],
            "timestamp": "2025-08-15T00:00:00Z"
        }
        
    except Exception as e:
        logger.error(f"LLM aggregation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"LLM service aggregation failed: {str(e)}"
        )


# WebSocket proxy for real-time conversations
@app.websocket("/api/v1/ws/conversation/{topic_id}")
async def websocket_conversation_proxy(websocket: WebSocket, topic_id: str):
    """WebSocket proxy to Conversation Service"""
    await websocket.accept()
    
    try:
        # In a real implementation, you would establish a WebSocket connection
        # to the backend Conversation Service and proxy messages bidirectionally
        
        # For POC, simulate conversation flow
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "topic_id": topic_id,
            "message": f"Connected to conversation for topic {topic_id}"
        }))
        
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Echo back with simulated processing
            response = {
                "type": "message_response",
                "topic_id": topic_id,
                "original_message": message,
                "response": f"Processed: {message.get('content', 'No content')}",
                "timestamp": "2025-08-15T00:00:00Z"
            }
            
            await websocket.send_text(json.dumps(response))
            
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for topic {topic_id}")
    except Exception as e:
        logger.error(f"WebSocket error for topic {topic_id}: {str(e)}")
        await websocket.close(code=1011, reason=str(e))


# Generic Backend Proxy - catch-all route for Backend API
@app.api_route("/api/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"])
@limiter.limit("60/minute")
async def proxy_to_backend(request: Request, path: str):
    """Generic proxy to Backend service for all API calls"""
    try:
        # Construct the target URL
        target_url = f"{settings.backend_service_url}/api/{path}"

        # Get request data
        query_params = dict(request.query_params)
        headers = dict(request.headers)

        # Remove host header to avoid conflicts
        headers.pop("host", None)

        # Get request body if present
        body = None
        if request.method in ["POST", "PUT", "PATCH"]:
            body = await request.body()

        # Make the proxy request
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.request(
                method=request.method,
                url=target_url,
                params=query_params,
                headers=headers,
                content=body
            )

            # Return the response
            return JSONResponse(
                content=response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text,
                status_code=response.status_code,
                headers=dict(response.headers)
            )

    except httpx.TimeoutException:
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Backend service timeout"
        )
    except httpx.ConnectError:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Backend service unavailable"
        )
    except Exception as e:
        logger.error(f"Proxy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Proxy error: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.bff_host,
        port=settings.bff_port,
        reload=settings.bff_debug
    )
