import redis
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from fastapi import Request, HTTPException, status
from config import settings
from typing import Optional
import logging

logger = logging.getLogger(__name__)

# Initialize Redis connection
try:
    redis_client = redis.from_url(settings.redis_url, decode_responses=True)
    redis_client.ping()  # Test connection
    logger.info("Redis connection established successfully")
except Exception as e:
    logger.warning(f"Redis connection failed: {e}. Rate limiting will be disabled.")
    redis_client = None


def get_user_id_or_ip(request: Request) -> str:
    """Get user ID from JWT token or fall back to IP address"""
    # Try to get user ID from JWT token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        try:
            from auth import verify_token
            token = auth_header.split(" ")[1]
            payload = verify_token(token)
            user_id = payload.get("sub")
            if user_id:
                return f"user:{user_id}"
        except Exception:
            pass  # Fall back to IP address
    
    # Fall back to IP address
    return f"ip:{get_remote_address(request)}"


# Initialize rate limiter
if redis_client:
    limiter = Limiter(
        key_func=get_user_id_or_ip,
        storage_uri=settings.redis_url,
        default_limits=[f"{settings.rate_limit_per_min}/minute"]
    )
else:
    # Dummy limiter when Redis is not available
    class DummyLimiter:
        def limit(self, rate_limit: str):
            def decorator(func):
                return func
            return decorator
        
        def exempt(self, func):
            return func
    
    limiter = DummyLimiter()


def custom_rate_limit_handler(request: Request, exc: RateLimitExceeded):
    """Custom rate limit exceeded handler"""
    response = HTTPException(
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        detail={
            "error": "Rate limit exceeded",
            "message": f"Too many requests. Limit: {exc.detail}",
            "retry_after": exc.retry_after
        }
    )
    return response
