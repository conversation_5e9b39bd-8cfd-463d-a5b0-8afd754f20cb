#!/usr/bin/env python3
"""
Gateway Integration Tests
Tests the gateway service integration with the main Master-Know system
"""

import asyncio
import aiohttp
import pytest
from typing import Dict, Any

BASE_URL = "http://localhost"

class TestGatewayIntegration:
    
    async def test_health_check(self):
        """Test gateway health check"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/health") as response:
                assert response.status == 200
                data = await response.json()
                assert data["status"] == "healthy"
    
    async def test_topic_service_integration(self):
        """Test gateway integration with topic service"""
        async with aiohttp.ClientSession() as session:
            # Test topic aggregation
            async with session.get(f"{BASE_URL}/api/v1/aggregate/topic/test-topic") as response:
                assert response.status in [200, 503]  # 503 if services not ready
                data = await response.json()
                assert "topic_id" in data
    
    async def test_authentication_flow(self):
        """Test complete authentication flow"""
        async with aiohttp.ClientSession() as session:
            # Login
            login_data = {"username": "demo", "password": "demo123"}
            async with session.post(f"{BASE_URL}/api/v1/auth/login", json=login_data) as response:
                assert response.status == 200
                data = await response.json()
                assert "access_token" in data
                
                # Test protected endpoint
                headers = {"Authorization": f"Bearer {data['access_token']}"}
                async with session.get(f"{BASE_URL}/api/v1/auth/me", headers=headers) as response:
                    assert response.status == 200

if __name__ == "__main__":
    # Run basic tests
    async def run_tests():
        test = TestGatewayIntegration()
        await test.test_health_check()
        await test.test_topic_service_integration()
        await test.test_authentication_flow()
        print("✅ All integration tests passed!")
    
    asyncio.run(run_tests())
