"""
Topic Service

主题管理服务，提供主题CRUD、文档关联和Manticore搜索集成功能
"""

from .models.topic import (
    TopicCreate,
    TopicUpdate,
    TopicResponse,
    TopicListResponse,
    AttachDocument,
    DocumentLink,
    MemorySearchResult,
    TopicStats,
    UserTopicStats
)

from .services.topic_service import TopicService
from .services.manticore_service import ManticoreService

from .utils.config import get_settings, TopicServiceSettings
from .utils.database import (
    init_database,
    get_db_session,
    get_db_session_dependency,
    check_database_connection
)
from .utils.exceptions import (
    TopicServiceError,
    TopicNotFoundError,
    TopicValidationError,
    TopicPermissionError,
    DocumentLinkError,
    ManticoreSearchError
)
from .utils.health import HealthChecker

__version__ = "1.0.0"
__author__ = "Master Know Team"
__description__ = "主题管理和文档关联服务"

# 导出主要类和函数
__all__ = [
    # Models
    "TopicCreate",
    "TopicUpdate",
    "TopicResponse",
    "TopicListResponse",
    "AttachDocument",
    "DocumentLink",
    "MemorySearchResult",
    "TopicStats",
    "UserTopicStats",

    # Services
    "TopicService",
    "ManticoreService",

    # Utils
    "get_settings",
    "TopicServiceSettings",
    "init_database",
    "get_db_session",
    "get_db_session_dependency",
    "check_database_connection",
    "HealthChecker",

    # Exceptions
    "TopicServiceError",
    "TopicNotFoundError",
    "TopicValidationError",
    "TopicPermissionError",
    "DocumentLinkError",
    "ManticoreSearchError",
]