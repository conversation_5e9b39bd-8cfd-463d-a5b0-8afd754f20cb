"""
Topic Service API

基于 PROJECT_BLUEPRINT.md 的技术栈实现主题管理服务
"""

import asyncio
from datetime import datetime
from typing import Optional, List, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware

from ..models.topic import TopicCreate, TopicResponse, AttachDocument, MemorySearchResult
from ..services.topic_service import TopicService
from ..services.manticore_service import ManticoreService
from ..utils.config import get_settings
from ..utils.database import get_db_session_dependency
from ..utils.health import HealthChecker


# 获取配置
settings = get_settings()

# 全局服务实例
manticore_service = None
health_checker = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global manticore_service, health_checker
    
    print("🚀 启动 Topic Service...")
    
    # 初始化服务
    manticore_service = ManticoreService(settings)
    health_checker = HealthChecker(settings)
    
    print("✅ Topic Service 初始化完成")
    
    yield
    
    print("🛑 关闭 Topic Service...")


# 创建 FastAPI 应用
app = FastAPI(
    title=settings.api_title,
    description=settings.api_description,
    version="1.0.0",
    lifespan=lifespan
)

# 添加 CORS 中间件
if settings.enable_cors:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )


# ===== 健康检查 =====
@app.get("/health")
async def health_check():
    """健康检查"""
    return await health_checker.check_health()


@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "Topic Service",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.utcnow().isoformat()
    }


# ===== 主题管理 API =====
@app.post("/api/v1/topics", response_model=TopicResponse)
async def create_topic(
    topic_data: TopicCreate,
    db_session = Depends(get_db_session_dependency)
):
    """创建主题"""
    try:
        service = TopicService(db_session)
        topic = await service.create_topic(topic_data)
        return topic
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/topics/{topic_id}", response_model=TopicResponse)
async def get_topic(
    topic_id: int,
    db_session = Depends(get_db_session_dependency)
):
    """获取主题详情"""
    service = TopicService(db_session)
    topic = await service.get_topic(topic_id)

    if not topic:
        raise HTTPException(status_code=404, detail="主题不存在")

    return topic


@app.get("/api/v1/topics")
async def list_topics(
    user_id: int = 1,
    limit: int = 20,
    offset: int = 0,
    db_session = Depends(get_db_session_dependency)
):
    """获取用户主题列表"""
    service = TopicService(db_session)
    topics = await service.get_user_topics(user_id, limit, offset)
    
    return {
        "topics": topics,
        "total": len(topics),
        "limit": limit,
        "offset": offset
    }


@app.put("/api/v1/topics/{topic_id}", response_model=TopicResponse)
async def update_topic(
    topic_id: int,
    topic_data: TopicCreate,
    db_session = Depends(get_db_session_dependency)
):
    """更新主题"""
    service = TopicService(db_session)
    topic = await service.update_topic(topic_id, topic_data)
    
    if not topic:
        raise HTTPException(status_code=404, detail="主题不存在")
    
    return topic


@app.delete("/api/v1/topics/{topic_id}")
async def delete_topic(
    topic_id: int,
    db_session = Depends(get_db_session_dependency)
):
    """删除主题"""
    service = TopicService(db_session)
    success = await service.delete_topic(topic_id)

    if not success:
        raise HTTPException(status_code=404, detail="主题不存在")

    return {"ok": True, "message": "主题删除成功"}


# ===== 文档关联 API =====
@app.post("/api/v1/topics/{topic_id}/documents")
async def attach_document(
    topic_id: int,
    doc_data: AttachDocument,
    db_session = Depends(get_db_session_dependency)
):
    """关联文档到主题"""
    service = TopicService(db_session)
    success = await service.attach_document(topic_id, doc_data.document_id)

    if not success:
        raise HTTPException(status_code=404, detail="主题不存在")

    return {"ok": True, "message": "文档关联成功"}


@app.get("/api/v1/topics/{topic_id}/documents")
async def get_topic_documents(
    topic_id: int,
    db_session = Depends(get_db_session_dependency)
):
    """获取主题关联的文档列表"""
    service = TopicService(db_session)
    documents = await service.get_topic_documents(topic_id)

    return {
        "topic_id": topic_id,
        "documents": documents,
        "total": len(documents)
    }


@app.delete("/api/v1/topics/{topic_id}/documents/{document_id}")
async def detach_document(
    topic_id: int,
    document_id: int,
    db_session = Depends(get_db_session_dependency)
):
    """取消文档关联"""
    service = TopicService(db_session)
    success = await service.detach_document(topic_id, document_id)

    if not success:
        raise HTTPException(status_code=404, detail="关联不存在")

    return {"ok": True, "message": "文档关联已取消"}


# ===== Manticore 集成 API =====
@app.get("/api/v1/topics/{topic_id}/memory")
async def get_topic_memory(
    topic_id: int,
    limit: int = 5,
    db_session = Depends(get_db_session_dependency)
):
    """获取主题长期记忆锚点"""
    try:
        # 验证主题存在
        service = TopicService(db_session)
        topic = await service.get_topic(topic_id)
        if not topic:
            raise HTTPException(status_code=404, detail="主题不存在")

        # 从 Manticore 检索记忆锚点
        memory_results = await manticore_service.search_topic_memory(topic_id, limit)

        return {
            "topic_id": topic_id,
            "memory_anchors": memory_results,
            "total": len(memory_results),
            "limit": limit
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检索记忆锚点失败: {str(e)}")


# ===== 统计 API =====
@app.get("/api/v1/topics/{topic_id}/stats")
async def get_topic_stats(
    topic_id: int,
    db_session = Depends(get_db_session_dependency)
):
    """获取主题统计信息"""
    service = TopicService(db_session)
    stats = await service.get_topic_stats(topic_id)

    if not stats:
        raise HTTPException(status_code=404, detail="主题不存在")

    return stats


@app.get("/api/v1/users/{user_id}/topic-stats")
async def get_user_topic_stats(
    user_id: int,
    db_session = Depends(get_db_session_dependency)
):
    """获取用户主题统计"""
    service = TopicService(db_session)
    stats = await service.get_user_topic_stats(user_id)

    return stats


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
