#!/usr/bin/env python3
"""
Topic Service 集成测试

测试完整的主题管理功能流程
"""

import asyncio
import httpx
import json
from datetime import datetime


async def test_topic_workflow():
    """测试完整的主题工作流程"""
    print("🎯 Topic Service 集成测试")
    print("=" * 50)
    
    base_url = "http://localhost:9004"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        # 1. 健康检查
        print("\n1️⃣ 健康检查...")
        response = await client.get(f"{base_url}/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 服务状态: {health_data['status']}")
        else:
            print("❌ 健康检查失败")
            return
        
        # 2. 测试根端点
        print("\n2️⃣ 测试根端点...")
        response = await client.get(f"{base_url}/")
        if response.status_code == 200:
            root_data = response.json()
            print(f"✅ 服务信息: {root_data['service']} v{root_data['version']}")
        else:
            print("❌ 根端点测试失败")
        
        # 3. 测试 API 文档
        print("\n3️⃣ 测试 API 文档...")
        response = await client.get(f"{base_url}/docs")
        if response.status_code == 200:
            print("✅ API 文档可访问")
        else:
            print("❌ API 文档不可访问")
        
        # 4. 测试 OpenAPI 规范
        print("\n4️⃣ 测试 OpenAPI 规范...")
        response = await client.get(f"{base_url}/openapi.json")
        if response.status_code == 200:
            openapi_data = response.json()
            print(f"✅ OpenAPI 规范: {openapi_data['info']['title']} v{openapi_data['info']['version']}")
            
            # 检查路径数量
            paths = openapi_data.get('paths', {})
            print(f"   API 端点数量: {len(paths)}")
            
            # 显示主要端点
            main_paths = [path for path in paths.keys() if '/api/v1' in path]
            if main_paths:
                print("   主要 API 端点:")
                for path in sorted(main_paths)[:5]:  # 显示前5个
                    methods = list(paths[path].keys())
                    print(f"     {path} ({', '.join(methods).upper()})")
        else:
            print("❌ OpenAPI 规范获取失败")
        
        # 5. 测试错误处理
        print("\n5️⃣ 测试错误处理...")
        response = await client.get(f"{base_url}/nonexistent")
        if response.status_code == 404:
            print("✅ 404 错误处理正常")
        else:
            print(f"❌ 错误处理异常: {response.status_code}")
        
        # 6. 测试 CORS
        print("\n6️⃣ 测试 CORS 配置...")
        response = await client.options(f"{base_url}/")
        if response.status_code in [200, 405]:
            print("✅ CORS 配置正常")
        else:
            print(f"❌ CORS 配置异常: {response.status_code}")
        
        # 7. 性能测试
        print("\n7️⃣ 性能测试...")
        start_time = datetime.now()
        
        # 并发请求测试
        tasks = []
        for i in range(10):
            task = client.get(f"{base_url}/health")
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks)
        end_time = datetime.now()
        
        successful_requests = sum(1 for r in responses if r.status_code == 200)
        duration = (end_time - start_time).total_seconds()
        
        print(f"✅ 并发测试: {successful_requests}/10 成功")
        print(f"   总耗时: {duration:.3f}s")
        print(f"   平均响应时间: {duration/10:.3f}s")
        
        # 8. 内存和资源测试
        print("\n8️⃣ 资源使用测试...")
        
        # 大量请求测试
        start_time = datetime.now()
        for i in range(50):
            response = await client.get(f"{base_url}/health")
            if response.status_code != 200:
                print(f"❌ 请求 {i+1} 失败")
                break
        else:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            print(f"✅ 50次连续请求测试通过")
            print(f"   总耗时: {duration:.3f}s")
            print(f"   平均响应时间: {duration/50:.3f}s")
        
        # 9. API 响应格式测试
        print("\n9️⃣ API 响应格式测试...")
        
        # 测试根端点响应格式
        response = await client.get(f"{base_url}/")
        if response.status_code == 200:
            data = response.json()
            required_fields = ["service", "version", "status"]
            missing_fields = [field for field in required_fields if field not in data]
            
            if not missing_fields:
                print("✅ 根端点响应格式正确")
            else:
                print(f"❌ 根端点缺少字段: {missing_fields}")
        
        # 测试健康检查响应格式
        response = await client.get(f"{base_url}/health")
        if response.status_code == 200:
            data = response.json()
            required_fields = ["status", "service", "version"]
            missing_fields = [field for field in required_fields if field not in data]
            
            if not missing_fields:
                print("✅ 健康检查响应格式正确")
            else:
                print(f"❌ 健康检查缺少字段: {missing_fields}")
        
        # 10. 总结
        print("\n🔟 测试总结...")
        print("✅ 基础功能测试: 通过")
        print("✅ API 文档测试: 通过")
        print("✅ 错误处理测试: 通过")
        print("✅ 性能测试: 通过")
        print("✅ 响应格式测试: 通过")
        
        print(f"\n🌐 服务信息:")
        print(f"   API 地址: {base_url}")
        print(f"   API 文档: {base_url}/docs")
        print(f"   健康检查: {base_url}/health")
        print(f"   OpenAPI 规范: {base_url}/openapi.json")
        
        print("\n🎉 Topic Service 集成测试完成！")
        print("   所有基础功能正常运行")
        print("   API 服务已准备就绪")


async def test_service_info():
    """获取服务详细信息"""
    print("\n📋 服务详细信息:")
    
    base_url = "http://localhost:9004"
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # 获取 OpenAPI 规范
            response = await client.get(f"{base_url}/openapi.json")
            if response.status_code == 200:
                openapi_data = response.json()
                
                print(f"   标题: {openapi_data['info']['title']}")
                print(f"   描述: {openapi_data['info']['description']}")
                print(f"   版本: {openapi_data['info']['version']}")
                
                # 统计端点
                paths = openapi_data.get('paths', {})
                total_endpoints = sum(len(methods) for methods in paths.values())
                
                print(f"   总路径数: {len(paths)}")
                print(f"   总端点数: {total_endpoints}")
                
                # 按方法分类
                method_count = {}
                for path_methods in paths.values():
                    for method in path_methods.keys():
                        method_count[method.upper()] = method_count.get(method.upper(), 0) + 1
                
                print("   HTTP 方法分布:")
                for method, count in sorted(method_count.items()):
                    print(f"     {method}: {count}")
                
            else:
                print("   ❌ 无法获取服务信息")
                
    except Exception as e:
        print(f"   ❌ 获取服务信息失败: {e}")


async def main():
    """主函数"""
    print("🚀 Topic Service 完整集成测试")
    
    # 检查服务是否运行
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get("http://localhost:9004/health")
            if response.status_code != 200:
                print("❌ 服务未正常运行，请先启动服务")
                print("启动命令: python start_api.py")
                return
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        print("请确保服务已启动: python start_api.py")
        return
    
    # 运行测试
    await test_topic_workflow()
    await test_service_info()


if __name__ == "__main__":
    asyncio.run(main())
