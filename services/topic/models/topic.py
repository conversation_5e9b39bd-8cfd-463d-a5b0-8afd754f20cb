"""
Topic Service 数据模型

定义主题相关的数据结构和验证规则
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


# ===== SQLAlchemy 数据库模型 =====
class TopicModel(Base):
    """主题数据库模型"""
    __tablename__ = "topics"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=True)
    user_id = Column(Integer, nullable=False, index=True)
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    document_links = relationship("TopicDocumentLinkModel", back_populates="topic", cascade="all, delete-orphan")


class TopicDocumentLinkModel(Base):
    """主题文档关联数据库模型"""
    __tablename__ = "topic_document_links"
    
    id = Column(Integer, primary_key=True, index=True)
    topic_id = Column(Integer, ForeignKey("topics.id"), nullable=False, index=True)
    document_id = Column(Integer, nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    topic = relationship("TopicModel", back_populates="document_links")


class TopicStatisticsModel(Base):
    """主题统计数据库模型"""
    __tablename__ = "topic_statistics"
    
    id = Column(Integer, primary_key=True, index=True)
    topic_id = Column(Integer, ForeignKey("topics.id"), nullable=False, unique=True, index=True)
    document_count = Column(Integer, default=0)
    conversation_count = Column(Integer, default=0)
    last_activity_at = Column(DateTime, nullable=True)
    total_study_time = Column(Integer, default=0)  # 秒
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# ===== Pydantic API 模型 =====
class TopicBase(BaseModel):
    """主题基础模型"""
    title: str = Field(..., min_length=1, max_length=200, description="主题标题")
    description: Optional[str] = Field(None, max_length=1000, description="主题描述")


class TopicCreate(TopicBase):
    """创建主题请求模型"""
    user_id: int = Field(..., gt=0, description="用户ID")


class TopicUpdate(TopicBase):
    """更新主题请求模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="主题标题")
    description: Optional[str] = Field(None, max_length=1000, description="主题描述")
    is_active: Optional[bool] = Field(None, description="是否活跃")


class TopicResponse(BaseModel):
    """主题响应模型"""
    id: int
    title: str
    description: Optional[str]
    user_id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    document_count: int = 0
    conversation_count: int = 0
    
    class Config:
        from_attributes = True


class TopicListResponse(BaseModel):
    """主题列表响应模型"""
    topics: List[TopicResponse]
    total: int
    limit: int
    offset: int


# ===== 文档关联模型 =====
class AttachDocument(BaseModel):
    """关联文档请求模型"""
    document_id: int = Field(..., gt=0, description="文档ID")


class DocumentLink(BaseModel):
    """文档关联响应模型"""
    id: int
    topic_id: int
    document_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class DocumentLinkResponse(BaseModel):
    """文档关联列表响应模型"""
    topic_id: int
    documents: List[int]
    total: int


# ===== Manticore 搜索模型 =====
class MemorySearchResult(BaseModel):
    """记忆搜索结果模型"""
    id: int
    content: str
    relevance_score: float
    metadata: Dict[str, Any] = {}


class TopicMemoryResponse(BaseModel):
    """主题记忆响应模型"""
    topic_id: int
    memory_anchors: List[MemorySearchResult]
    total: int
    limit: int


# ===== 统计模型 =====
class TopicStats(BaseModel):
    """主题统计模型"""
    topic_id: int
    document_count: int
    conversation_count: int
    last_activity_at: Optional[datetime]
    total_study_time: int  # 秒
    updated_at: datetime
    
    class Config:
        from_attributes = True


class UserTopicStats(BaseModel):
    """用户主题统计模型"""
    user_id: int
    total_topics: int
    active_topics: int
    total_documents: int
    total_conversations: int
    total_study_time: int  # 秒
    most_active_topic: Optional[TopicResponse]
    recent_topics: List[TopicResponse]


# ===== 搜索和过滤模型 =====
class TopicSearchRequest(BaseModel):
    """主题搜索请求模型"""
    query: str = Field(..., min_length=1, description="搜索关键词")
    user_id: Optional[int] = Field(None, description="用户ID过滤")
    is_active: Optional[bool] = Field(None, description="活跃状态过滤")
    limit: int = Field(20, ge=1, le=100, description="结果数量限制")
    offset: int = Field(0, ge=0, description="结果偏移量")


class TopicFilter(BaseModel):
    """主题过滤模型"""
    user_id: Optional[int] = None
    is_active: Optional[bool] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    has_documents: Optional[bool] = None
    has_conversations: Optional[bool] = None


# ===== 批量操作模型 =====
class BulkTopicOperation(BaseModel):
    """批量主题操作模型"""
    topic_ids: List[int] = Field(..., min_items=1, max_items=100, description="主题ID列表")
    operation: str = Field(..., description="操作类型: activate, deactivate, delete")


class BulkDocumentAttach(BaseModel):
    """批量文档关联模型"""
    topic_id: int = Field(..., gt=0, description="主题ID")
    document_ids: List[int] = Field(..., min_items=1, max_items=50, description="文档ID列表")


# ===== 导入导出模型 =====
class TopicExport(BaseModel):
    """主题导出模型"""
    topic: TopicResponse
    documents: List[int]
    statistics: Optional[TopicStats]
    created_at: datetime


class TopicImport(BaseModel):
    """主题导入模型"""
    title: str
    description: Optional[str]
    document_ids: List[int] = []
    preserve_timestamps: bool = False
