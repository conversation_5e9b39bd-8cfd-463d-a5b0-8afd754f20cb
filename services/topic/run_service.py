#!/usr/bin/env python3
"""
Topic Service 启动脚本

启动完整的主题服务 API
"""

import os
import sys
import uvicorn
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault("TOPIC_ENVIRONMENT", "development")
os.environ.setdefault("TOPIC_DEBUG", "true")
os.environ.setdefault("TOPIC_DATABASE_URL", "sqlite:///./topic_service.db")


def main():
    """主函数"""
    print("🚀 启动 Topic Service...")
    
    try:
        # 导入应用
        from services.topic.api.main import app
        
        # 获取配置
        from services.topic.utils.config import get_settings
        settings = get_settings()
        
        print(f"📋 服务配置:")
        print(f"   环境: {settings.environment}")
        print(f"   端口: {settings.api_port}")
        print(f"   数据库: {settings.database_url}")
        print(f"   调试模式: {settings.debug}")
        
        print(f"\n🌐 服务地址:")
        print(f"   API: http://{settings.api_host}:{settings.api_port}")
        print(f"   文档: http://{settings.api_host}:{settings.api_port}/docs")
        print(f"   健康检查: http://{settings.api_host}:{settings.api_port}/health")
        
        # 启动服务
        uvicorn.run(
            app,
            host=settings.api_host,
            port=settings.api_port,
            reload=settings.debug,
            log_level=settings.log_level.lower(),
            access_log=True
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖已安装: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
