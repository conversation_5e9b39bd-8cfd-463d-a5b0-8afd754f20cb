"""
Topic Service 业务逻辑

实现主题管理的核心业务功能
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc

from ..models.topic import (
    TopicModel, TopicDocumentLinkModel, TopicStatisticsModel,
    TopicCreate, TopicUpdate, TopicResponse, TopicStats, UserTopicStats
)
from ..utils.exceptions import TopicNotFoundError, TopicValidationError, TopicPermissionError


class TopicService:
    """主题服务"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    async def create_topic(self, topic_data: TopicCreate) -> TopicResponse:
        """创建主题"""
        try:
            # 验证用户主题数量限制
            user_topic_count = self.db.query(TopicModel).filter(
                and_(
                    TopicModel.user_id == topic_data.user_id,
                    TopicModel.is_active == True
                )
            ).count()
            
            # 这里可以添加配置的最大主题数检查
            # if user_topic_count >= settings.max_topics_per_user:
            #     raise TopicValidationError("用户主题数量已达上限")
            
            # 创建主题
            topic = TopicModel(
                title=topic_data.title,
                description=topic_data.description,
                user_id=topic_data.user_id
            )
            
            self.db.add(topic)
            self.db.commit()
            self.db.refresh(topic)
            
            # 创建统计记录
            stats = TopicStatisticsModel(topic_id=topic.id)
            self.db.add(stats)
            self.db.commit()
            
            return await self._topic_to_response(topic)
            
        except Exception as e:
            self.db.rollback()
            raise TopicValidationError(f"创建主题失败: {str(e)}")
    
    async def get_topic(self, topic_id: int) -> Optional[TopicResponse]:
        """获取主题详情"""
        topic = self.db.query(TopicModel).filter(TopicModel.id == topic_id).first()
        
        if not topic:
            return None
        
        return await self._topic_to_response(topic)
    
    async def get_user_topics(
        self, 
        user_id: int, 
        limit: int = 20, 
        offset: int = 0,
        is_active: Optional[bool] = None
    ) -> List[TopicResponse]:
        """获取用户主题列表"""
        query = self.db.query(TopicModel).filter(TopicModel.user_id == user_id)
        
        if is_active is not None:
            query = query.filter(TopicModel.is_active == is_active)
        
        topics = query.order_by(desc(TopicModel.updated_at)).offset(offset).limit(limit).all()
        
        return [await self._topic_to_response(topic) for topic in topics]
    
    async def update_topic(self, topic_id: int, topic_data: TopicUpdate) -> Optional[TopicResponse]:
        """更新主题"""
        topic = self.db.query(TopicModel).filter(TopicModel.id == topic_id).first()
        
        if not topic:
            return None
        
        try:
            # 更新字段
            if topic_data.title is not None:
                topic.title = topic_data.title
            if topic_data.description is not None:
                topic.description = topic_data.description
            if topic_data.is_active is not None:
                topic.is_active = topic_data.is_active
            
            topic.updated_at = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(topic)
            
            return await self._topic_to_response(topic)
            
        except Exception as e:
            self.db.rollback()
            raise TopicValidationError(f"更新主题失败: {str(e)}")
    
    async def delete_topic(self, topic_id: int) -> bool:
        """删除主题"""
        topic = self.db.query(TopicModel).filter(TopicModel.id == topic_id).first()
        
        if not topic:
            return False
        
        try:
            # 删除相关统计
            self.db.query(TopicStatisticsModel).filter(
                TopicStatisticsModel.topic_id == topic_id
            ).delete()
            
            # 删除文档关联
            self.db.query(TopicDocumentLinkModel).filter(
                TopicDocumentLinkModel.topic_id == topic_id
            ).delete()
            
            # 删除主题
            self.db.delete(topic)
            self.db.commit()
            
            return True
            
        except Exception as e:
            self.db.rollback()
            raise TopicValidationError(f"删除主题失败: {str(e)}")
    
    async def attach_document(self, topic_id: int, document_id: int) -> bool:
        """关联文档到主题"""
        # 验证主题存在
        topic = self.db.query(TopicModel).filter(TopicModel.id == topic_id).first()
        if not topic:
            return False
        
        # 检查是否已经关联
        existing = self.db.query(TopicDocumentLinkModel).filter(
            and_(
                TopicDocumentLinkModel.topic_id == topic_id,
                TopicDocumentLinkModel.document_id == document_id
            )
        ).first()
        
        if existing:
            return True  # 已经关联，返回成功
        
        try:
            # 创建关联
            link = TopicDocumentLinkModel(
                topic_id=topic_id,
                document_id=document_id
            )
            self.db.add(link)
            
            # 更新统计
            await self._update_topic_stats(topic_id)
            
            self.db.commit()
            return True
            
        except Exception as e:
            self.db.rollback()
            raise TopicValidationError(f"关联文档失败: {str(e)}")
    
    async def detach_document(self, topic_id: int, document_id: int) -> bool:
        """取消文档关联"""
        link = self.db.query(TopicDocumentLinkModel).filter(
            and_(
                TopicDocumentLinkModel.topic_id == topic_id,
                TopicDocumentLinkModel.document_id == document_id
            )
        ).first()
        
        if not link:
            return False
        
        try:
            self.db.delete(link)
            
            # 更新统计
            await self._update_topic_stats(topic_id)
            
            self.db.commit()
            return True
            
        except Exception as e:
            self.db.rollback()
            raise TopicValidationError(f"取消文档关联失败: {str(e)}")
    
    async def get_topic_documents(self, topic_id: int) -> List[int]:
        """获取主题关联的文档ID列表"""
        links = self.db.query(TopicDocumentLinkModel).filter(
            TopicDocumentLinkModel.topic_id == topic_id
        ).all()
        
        return [link.document_id for link in links]
    
    async def get_topic_stats(self, topic_id: int) -> Optional[TopicStats]:
        """获取主题统计信息"""
        stats = self.db.query(TopicStatisticsModel).filter(
            TopicStatisticsModel.topic_id == topic_id
        ).first()
        
        if not stats:
            return None
        
        return TopicStats.from_orm(stats)
    
    async def get_user_topic_stats(self, user_id: int) -> UserTopicStats:
        """获取用户主题统计"""
        # 基础统计
        total_topics = self.db.query(TopicModel).filter(TopicModel.user_id == user_id).count()
        active_topics = self.db.query(TopicModel).filter(
            and_(TopicModel.user_id == user_id, TopicModel.is_active == True)
        ).count()
        
        # 文档统计
        total_documents = self.db.query(func.count(TopicDocumentLinkModel.id)).join(
            TopicModel, TopicModel.id == TopicDocumentLinkModel.topic_id
        ).filter(TopicModel.user_id == user_id).scalar() or 0
        
        # 学习时间统计
        total_study_time = self.db.query(func.sum(TopicStatisticsModel.total_study_time)).join(
            TopicModel, TopicModel.id == TopicStatisticsModel.topic_id
        ).filter(TopicModel.user_id == user_id).scalar() or 0
        
        # 最活跃主题
        most_active_topic = None
        most_active_stats = self.db.query(TopicStatisticsModel).join(
            TopicModel, TopicModel.id == TopicStatisticsModel.topic_id
        ).filter(TopicModel.user_id == user_id).order_by(
            desc(TopicStatisticsModel.document_count + TopicStatisticsModel.conversation_count)
        ).first()
        
        if most_active_stats:
            topic = self.db.query(TopicModel).filter(TopicModel.id == most_active_stats.topic_id).first()
            if topic:
                most_active_topic = await self._topic_to_response(topic)
        
        # 最近主题
        recent_topics_data = self.db.query(TopicModel).filter(
            TopicModel.user_id == user_id
        ).order_by(desc(TopicModel.updated_at)).limit(5).all()
        
        recent_topics = [await self._topic_to_response(topic) for topic in recent_topics_data]
        
        return UserTopicStats(
            user_id=user_id,
            total_topics=total_topics,
            active_topics=active_topics,
            total_documents=total_documents,
            total_conversations=0,  # TODO: 实现对话统计
            total_study_time=total_study_time,
            most_active_topic=most_active_topic,
            recent_topics=recent_topics
        )
    
    async def search_topics(
        self, 
        query: str, 
        user_id: Optional[int] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[TopicResponse]:
        """搜索主题"""
        db_query = self.db.query(TopicModel)
        
        # 添加搜索条件
        if query:
            search_filter = or_(
                TopicModel.title.ilike(f"%{query}%"),
                TopicModel.description.ilike(f"%{query}%")
            )
            db_query = db_query.filter(search_filter)
        
        # 添加用户过滤
        if user_id:
            db_query = db_query.filter(TopicModel.user_id == user_id)
        
        # 只返回活跃主题
        db_query = db_query.filter(TopicModel.is_active == True)
        
        topics = db_query.order_by(desc(TopicModel.updated_at)).offset(offset).limit(limit).all()
        
        return [await self._topic_to_response(topic) for topic in topics]
    
    async def _topic_to_response(self, topic: TopicModel) -> TopicResponse:
        """将数据库模型转换为响应模型"""
        # 获取文档数量
        document_count = self.db.query(TopicDocumentLinkModel).filter(
            TopicDocumentLinkModel.topic_id == topic.id
        ).count()
        
        # 获取对话数量（TODO: 实现）
        conversation_count = 0
        
        return TopicResponse(
            id=topic.id,
            title=topic.title,
            description=topic.description,
            user_id=topic.user_id,
            is_active=topic.is_active,
            created_at=topic.created_at,
            updated_at=topic.updated_at,
            document_count=document_count,
            conversation_count=conversation_count
        )
    
    async def _update_topic_stats(self, topic_id: int):
        """更新主题统计"""
        # 获取或创建统计记录
        stats = self.db.query(TopicStatisticsModel).filter(
            TopicStatisticsModel.topic_id == topic_id
        ).first()
        
        if not stats:
            stats = TopicStatisticsModel(topic_id=topic_id)
            self.db.add(stats)
        
        # 更新文档数量
        document_count = self.db.query(TopicDocumentLinkModel).filter(
            TopicDocumentLinkModel.topic_id == topic_id
        ).count()
        
        stats.document_count = document_count
        stats.last_activity_at = datetime.utcnow()
        stats.updated_at = datetime.utcnow()
        
        # 不在这里提交，由调用方决定
