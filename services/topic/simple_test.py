#!/usr/bin/env python3
"""
Topic Service 简单测试

直接测试核心功能，避免复杂的导入问题
"""

import asyncio
import sqlite3
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


# ===== 简化的数据模型 =====
class TopicCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    user_id: int = Field(..., gt=0)


class TopicResponse(BaseModel):
    id: int
    title: str
    description: Optional[str]
    user_id: int
    is_active: bool
    created_at: str
    updated_at: str
    document_count: int = 0


# ===== 简化的服务类 =====
class SimpleTopicService:
    def __init__(self, db_path: str = "test_topic.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path, check_same_thread=False)
        cursor = conn.cursor()
        
        # 创建主题表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS topics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                user_id INTEGER NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建主题文档关联表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS topic_document_links (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic_id INTEGER NOT NULL,
                document_id INTEGER NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (topic_id) REFERENCES topics (id),
                UNIQUE(topic_id, document_id)
            )
        """)
        
        conn.commit()
        conn.close()
        print("✅ 数据库初始化完成")
    
    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_path, check_same_thread=False)
    
    async def create_topic(self, topic_data: TopicCreate) -> TopicResponse:
        """创建主题"""
        conn = self.get_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        now = datetime.utcnow().isoformat()
        
        cursor.execute("""
            INSERT INTO topics (title, description, user_id, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?)
        """, (topic_data.title, topic_data.description, topic_data.user_id, now, now))
        
        topic_id = cursor.lastrowid
        conn.commit()
        
        # 获取创建的主题
        cursor.execute("SELECT * FROM topics WHERE id = ?", (topic_id,))
        row = cursor.fetchone()
        conn.close()
        
        return TopicResponse(
            id=row["id"],
            title=row["title"],
            description=row["description"],
            user_id=row["user_id"],
            is_active=bool(row["is_active"]),
            created_at=row["created_at"],
            updated_at=row["updated_at"],
            document_count=0
        )
    
    async def get_topic(self, topic_id: int) -> Optional[TopicResponse]:
        """获取主题"""
        conn = self.get_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM topics WHERE id = ?", (topic_id,))
        row = cursor.fetchone()
        
        if not row:
            conn.close()
            return None
        
        # 获取文档数量
        cursor.execute("""
            SELECT COUNT(*) as count FROM topic_document_links 
            WHERE topic_id = ?
        """, (topic_id,))
        doc_count = cursor.fetchone()["count"]
        
        conn.close()
        
        return TopicResponse(
            id=row["id"],
            title=row["title"],
            description=row["description"],
            user_id=row["user_id"],
            is_active=bool(row["is_active"]),
            created_at=row["created_at"],
            updated_at=row["updated_at"],
            document_count=doc_count
        )
    
    async def attach_document(self, topic_id: int, document_id: int) -> bool:
        """关联文档到主题"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # 检查主题是否存在
        cursor.execute("SELECT id FROM topics WHERE id = ?", (topic_id,))
        if not cursor.fetchone():
            conn.close()
            return False
        
        try:
            now = datetime.utcnow().isoformat()
            cursor.execute("""
                INSERT INTO topic_document_links (topic_id, document_id, created_at)
                VALUES (?, ?, ?)
            """, (topic_id, document_id, now))
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            # 已经存在关联
            conn.close()
            return True
    
    async def get_topic_documents(self, topic_id: int) -> List[int]:
        """获取主题关联的文档ID列表"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT document_id FROM topic_document_links 
            WHERE topic_id = ?
        """, (topic_id,))
        
        results = cursor.fetchall()
        conn.close()
        
        return [row[0] for row in results]
    
    async def get_user_topics(self, user_id: int, limit: int = 20) -> List[TopicResponse]:
        """获取用户主题列表"""
        conn = self.get_connection()
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT * FROM topics 
            WHERE user_id = ? AND is_active = 1
            ORDER BY updated_at DESC
            LIMIT ?
        """, (user_id, limit))
        
        rows = cursor.fetchall()
        conn.close()
        
        topics = []
        for row in rows:
            # 获取每个主题的文档数量
            doc_count = len(await self.get_topic_documents(row["id"]))
            
            topics.append(TopicResponse(
                id=row["id"],
                title=row["title"],
                description=row["description"],
                user_id=row["user_id"],
                is_active=bool(row["is_active"]),
                created_at=row["created_at"],
                updated_at=row["updated_at"],
                document_count=doc_count
            ))
        
        return topics


# ===== 测试函数 =====
async def test_topic_service():
    """测试主题服务"""
    print("🧪 开始 Topic Service 简单测试...")
    
    # 创建服务实例
    service = SimpleTopicService("test_topic.db")
    
    # 测试创建主题
    topic_data = TopicCreate(
        title="测试主题",
        description="这是一个测试主题",
        user_id=1
    )
    
    topic = await service.create_topic(topic_data)
    print(f"✅ 创建主题成功: {topic.title} (ID: {topic.id})")
    
    # 测试获取主题
    retrieved_topic = await service.get_topic(topic.id)
    print(f"✅ 获取主题成功: {retrieved_topic.title}")
    
    # 测试关联文档
    success = await service.attach_document(topic.id, 123)
    print(f"✅ 文档关联: {'成功' if success else '失败'}")
    
    success = await service.attach_document(topic.id, 456)
    print(f"✅ 文档关联: {'成功' if success else '失败'}")
    
    # 测试获取主题文档
    documents = await service.get_topic_documents(topic.id)
    print(f"✅ 主题文档: {documents}")
    
    # 测试获取主题（包含文档数量）
    topic_with_docs = await service.get_topic(topic.id)
    print(f"✅ 主题文档数量: {topic_with_docs.document_count}")
    
    # 测试获取用户主题列表
    user_topics = await service.get_user_topics(1)
    print(f"✅ 用户主题数量: {len(user_topics)}")
    
    # 创建更多测试主题
    for i in range(3):
        test_topic = TopicCreate(
            title=f"测试主题 {i+2}",
            description=f"这是第 {i+2} 个测试主题",
            user_id=1
        )
        await service.create_topic(test_topic)
    
    # 再次获取用户主题列表
    user_topics = await service.get_user_topics(1)
    print(f"✅ 用户主题总数: {len(user_topics)}")
    
    for topic in user_topics:
        print(f"   - {topic.title} (文档数: {topic.document_count})")
    
    print("\n🎉 Topic Service 简单测试完成！")


def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        conn = sqlite3.connect("test_topic.db", check_same_thread=False)
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        conn.close()
        print("✅ 数据库连接成功")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🎯 Topic Service 简单测试套件")
    print("=" * 50)
    
    # 数据库连接测试
    if not test_database_connection():
        return
    
    # 主题服务测试
    await test_topic_service()
    
    print("\n📊 测试总结:")
    print("   ✅ 数据库连接正常")
    print("   ✅ 主题创建功能正常")
    print("   ✅ 主题查询功能正常")
    print("   ✅ 文档关联功能正常")
    print("   ✅ 用户主题列表功能正常")
    
    print(f"\n💾 测试数据库: test_topic.db")


if __name__ == "__main__":
    asyncio.run(run_all_tests())
