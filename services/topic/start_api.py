#!/usr/bin/env python3
"""
Topic Service API 启动脚本

直接启动 FastAPI 应用，避免复杂的导入问题
"""

import os
import sys
import uvicorn
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault("TOPIC_ENVIRONMENT", "development")
os.environ.setdefault("TOPIC_DEBUG", "true")
os.environ.setdefault("TOPIC_DATABASE_URL", "sqlite:///./topic_service.db")
os.environ.setdefault("TOPIC_API_PORT", "9004")


def create_app():
    """创建 FastAPI 应用"""
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    
    app = FastAPI(
        title="Topic Service API",
        description="主题管理和文档关联服务",
        version="1.0.0"
    )
    
    # 添加 CORS 中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 基础路由
    @app.get("/")
    async def root():
        return {
            "service": "Topic Service",
            "version": "1.0.0",
            "status": "running"
        }
    
    @app.get("/health")
    async def health():
        return {
            "status": "healthy",
            "service": "Topic Service",
            "version": "1.0.0"
        }
    
    return app


def main():
    """主函数"""
    print("🚀 启动 Topic Service API...")
    
    # 创建应用
    app = create_app()
    
    # 获取配置
    host = os.getenv("TOPIC_API_HOST", "0.0.0.0")
    port = int(os.getenv("TOPIC_API_PORT", "9004"))
    debug = os.getenv("TOPIC_DEBUG", "true").lower() == "true"
    
    print(f"📋 服务配置:")
    print(f"   地址: {host}:{port}")
    print(f"   调试模式: {debug}")
    
    print(f"\n🌐 服务地址:")
    print(f"   API: http://{host}:{port}")
    print(f"   文档: http://{host}:{port}/docs")
    print(f"   健康检查: http://{host}:{port}/health")
    
    # 启动服务
    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=False,  # 禁用 reload 避免警告
        log_level="info"
    )


if __name__ == "__main__":
    main()
