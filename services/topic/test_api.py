#!/usr/bin/env python3
"""
Topic Service API 测试脚本

测试 Topic Service API 的各个端点
"""

import asyncio
import httpx
import json
from datetime import datetime


class TopicAPITester:
    def __init__(self, base_url: str = "http://localhost:9004"):
        self.base_url = base_url
        self.client = None
        self.test_results = []
    
    async def __aenter__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()
    
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })
    
    async def test_health_check(self):
        """测试健康检查"""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            success = response.status_code == 200
            
            if success:
                data = response.json()
                details = f"状态: {data.get('status')}, 服务: {data.get('service')}"
            else:
                details = f"状态码: {response.status_code}"
            
            self.log_test("健康检查", success, details)
            return success
        except Exception as e:
            self.log_test("健康检查", False, str(e))
            return False
    
    async def test_root_endpoint(self):
        """测试根端点"""
        try:
            response = await self.client.get(f"{self.base_url}/")
            success = response.status_code == 200
            
            if success:
                data = response.json()
                details = f"服务: {data.get('service')}, 版本: {data.get('version')}"
            else:
                details = f"状态码: {response.status_code}"
            
            self.log_test("根端点", success, details)
            return success
        except Exception as e:
            self.log_test("根端点", False, str(e))
            return False
    
    async def test_api_documentation(self):
        """测试 API 文档"""
        try:
            response = await self.client.get(f"{self.base_url}/docs")
            success = response.status_code == 200
            details = "API 文档可访问" if success else f"状态码: {response.status_code}"
            self.log_test("API 文档", success, details)
            return success
        except Exception as e:
            self.log_test("API 文档", False, str(e))
            return False
    
    async def test_openapi_spec(self):
        """测试 OpenAPI 规范"""
        try:
            response = await self.client.get(f"{self.base_url}/openapi.json")
            success = response.status_code == 200
            
            if success:
                data = response.json()
                details = f"标题: {data.get('info', {}).get('title')}, 版本: {data.get('info', {}).get('version')}"
            else:
                details = f"状态码: {response.status_code}"
            
            self.log_test("OpenAPI 规范", success, details)
            return success
        except Exception as e:
            self.log_test("OpenAPI 规范", False, str(e))
            return False
    
    async def test_cors_headers(self):
        """测试 CORS 头"""
        try:
            response = await self.client.options(f"{self.base_url}/")
            success = response.status_code in [200, 405]  # OPTIONS 可能返回 405
            
            cors_headers = {
                "access-control-allow-origin": response.headers.get("access-control-allow-origin"),
                "access-control-allow-methods": response.headers.get("access-control-allow-methods"),
                "access-control-allow-headers": response.headers.get("access-control-allow-headers")
            }
            
            details = f"CORS 头: {cors_headers}"
            self.log_test("CORS 配置", success, details)
            return success
        except Exception as e:
            self.log_test("CORS 配置", False, str(e))
            return False
    
    async def test_error_handling(self):
        """测试错误处理"""
        try:
            # 测试不存在的端点
            response = await self.client.get(f"{self.base_url}/nonexistent")
            success = response.status_code == 404
            
            details = f"404 错误处理: {response.status_code}"
            self.log_test("错误处理", success, details)
            return success
        except Exception as e:
            self.log_test("错误处理", False, str(e))
            return False
    
    async def test_response_format(self):
        """测试响应格式"""
        try:
            response = await self.client.get(f"{self.base_url}/")
            success = response.status_code == 200
            
            if success:
                # 检查响应是否为 JSON
                content_type = response.headers.get("content-type", "")
                is_json = "application/json" in content_type
                
                if is_json:
                    data = response.json()
                    has_required_fields = all(key in data for key in ["service", "version", "status"])
                    success = has_required_fields
                    details = f"JSON 格式: {is_json}, 必需字段: {has_required_fields}"
                else:
                    success = False
                    details = f"内容类型: {content_type}"
            else:
                details = f"状态码: {response.status_code}"
            
            self.log_test("响应格式", success, details)
            return success
        except Exception as e:
            self.log_test("响应格式", False, str(e))
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始 Topic Service API 测试...")
        print(f"🎯 测试目标: {self.base_url}")
        print("-" * 50)
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        await asyncio.sleep(3)
        
        # 基础连接测试
        health_ok = await self.test_health_check()
        if not health_ok:
            print("❌ 健康检查失败，跳过其他测试")
            return
        
        # 运行所有测试
        await self.test_root_endpoint()
        await self.test_api_documentation()
        await self.test_openapi_spec()
        await self.test_cors_headers()
        await self.test_error_handling()
        await self.test_response_format()
        
        # 测试总结
        print("-" * 50)
        self.print_summary()
    
    def print_summary(self):
        """打印测试总结"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过: {passed_tests}")
        print(f"   失败: {failed_tests}")
        print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['details']}")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！API 服务正常运行！")
        else:
            print(f"\n⚠️ 有 {failed_tests} 个测试失败，请检查服务状态")


async def test_service_startup():
    """测试服务启动状态"""
    print("🔍 检查服务启动状态...")
    
    base_url = "http://localhost:9004"
    
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(f"{base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 服务已启动: {data.get('service')} v{data.get('version')}")
                return True
            else:
                print(f"❌ 服务响应异常: HTTP {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        print("请确保服务已启动: python start_api.py")
        return False


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Topic Service API 测试")
    parser.add_argument(
        "--url", 
        default="http://localhost:9004",
        help="服务 URL (默认: http://localhost:9004)"
    )
    args = parser.parse_args()
    
    # 检查服务是否启动
    if not await test_service_startup():
        return
    
    # 运行完整测试
    async with TopicAPITester(args.url) as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
