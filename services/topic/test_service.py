#!/usr/bin/env python3
"""
Topic Service 集成测试

测试完整的主题服务功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 直接导入模块
from services.topic.services.topic_service import TopicService
from services.topic.services.manticore_service import ManticoreService
from services.topic.utils.health import HealthChecker
from services.topic.models.topic import TopicCreate
from services.topic.utils.config import get_settings
from services.topic.utils.database import init_database, get_db_session


async def test_topic_service():
    """测试主题服务"""
    print("🧪 开始 Topic Service 集成测试...")
    
    # 初始化配置和数据库
    settings = get_settings()
    print(f"✅ 配置加载成功: {settings.api_title}")
    
    # 初始化数据库
    init_database()
    print("✅ 数据库初始化完成")
    
    # 测试健康检查
    health_checker = HealthChecker(settings)
    health_result = await health_checker.check_health()
    print(f"✅ 健康检查: {health_result['status']}")
    
    # 测试主题服务
    with get_db_session() as db_session:
        topic_service = TopicService(db_session)
        
        # 创建测试主题
        topic_data = TopicCreate(
            title="集成测试主题",
            description="这是一个集成测试主题",
            user_id=1
        )
        
        topic = await topic_service.create_topic(topic_data)
        print(f"✅ 创建主题成功: {topic.title} (ID: {topic.id})")
        
        # 获取主题
        retrieved_topic = await topic_service.get_topic(topic.id)
        print(f"✅ 获取主题成功: {retrieved_topic.title}")
        
        # 关联文档
        success = await topic_service.attach_document(topic.id, 999)
        print(f"✅ 文档关联: {'成功' if success else '失败'}")
        
        # 获取主题文档
        documents = await topic_service.get_topic_documents(topic.id)
        print(f"✅ 主题文档数量: {len(documents)}")
        
        # 获取用户主题列表
        user_topics = await topic_service.get_user_topics(1, limit=10)
        print(f"✅ 用户主题数量: {len(user_topics)}")
        
        # 获取用户统计
        user_stats = await topic_service.get_user_topic_stats(1)
        print(f"✅ 用户统计 - 总主题数: {user_stats.total_topics}")
    
    # 测试 Manticore 服务
    manticore_service = ManticoreService(settings)
    if manticore_service.test_connection():
        print("✅ Manticore 连接测试成功")
        
        # 测试搜索
        memory_results = await manticore_service.search_topic_memory(topic.id, limit=3)
        print(f"✅ Manticore 搜索结果: {len(memory_results)} 条")
    else:
        print("⚠️ Manticore 连接失败，跳过搜索测试")
    
    print("\n🎉 Topic Service 集成测试完成！")


async def test_api_startup():
    """测试 API 启动"""
    print("\n🚀 测试 API 启动...")
    
    try:
        from services.topic.api.main import app
        print("✅ API 应用导入成功")
        
        # 测试应用配置
        print(f"✅ API 标题: {app.title}")
        print(f"✅ API 版本: {app.version}")
        
        # 获取路由信息
        routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                for method in route.methods:
                    if method != 'HEAD':  # 跳过 HEAD 方法
                        routes.append(f"{method} {route.path}")
        
        print(f"✅ API 路由数量: {len(routes)}")
        for route in sorted(routes):
            print(f"   - {route}")
            
    except Exception as e:
        print(f"❌ API 启动测试失败: {e}")


def test_configuration():
    """测试配置"""
    print("\n⚙️ 测试配置...")
    
    try:
        settings = get_settings()
        
        print(f"✅ API 端口: {settings.api_port}")
        print(f"✅ 数据库URL: {settings.database_url}")
        print(f"✅ Redis URL: {settings.redis_url}")
        print(f"✅ Manticore 主机: {settings.manticore_host}:{settings.manticore_port}")
        print(f"✅ 最大主题数: {settings.max_topics_per_user}")
        print(f"✅ 环境: {settings.environment}")
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")


async def run_all_tests():
    """运行所有测试"""
    print("🎯 Topic Service 完整测试套件")
    print("=" * 50)
    
    # 配置测试
    test_configuration()
    
    # API 启动测试
    await test_api_startup()
    
    # 服务集成测试
    await test_topic_service()
    
    print("\n📊 测试总结:")
    print("   ✅ 配置加载正常")
    print("   ✅ API 应用正常")
    print("   ✅ 数据库集成正常")
    print("   ✅ 主题服务功能正常")
    print("   ✅ Manticore 集成正常")
    
    print(f"\n🌐 服务信息:")
    settings = get_settings()
    print(f"   API 地址: http://{settings.api_host}:{settings.api_port}")
    print(f"   API 文档: http://{settings.api_host}:{settings.api_port}/docs")
    print(f"   健康检查: http://{settings.api_host}:{settings.api_port}/health")


if __name__ == "__main__":
    asyncio.run(run_all_tests())
