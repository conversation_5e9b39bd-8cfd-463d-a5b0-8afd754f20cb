"""
Topic Service 配置管理

基于项目配置模板的主题服务专用配置
"""

from pydantic import BaseModel, Field
from typing import List
from functools import lru_cache
import os


class TopicServiceSettings(BaseModel):
    """主题服务配置类"""
    
    # ===== API 服务配置 =====
    api_host: str = Field(default="0.0.0.0", description="API服务监听地址")
    api_port: int = Field(default=9004, description="主题服务端口")
    api_title: str = Field(default="Topic Service API", description="主题服务API标题")
    api_description: str = Field(default="主题管理和文档关联服务", description="主题服务API描述")
    api_version: str = Field(default="1.0.0", description="API版本")
    api_prefix: str = Field(default="/api/v1", description="API路径前缀")
    
    # ===== 环境配置 =====
    environment: str = Field(default="development", description="运行环境")
    debug: bool = Field(default=False, description="是否启用调试模式")
    log_level: str = Field(default="INFO", description="日志级别")
    
    # ===== 数据库配置 =====
    database_url: str = Field(
        default="postgresql://master_know_user:master_know_pass@localhost:5432/master_know",
        description="数据库连接URL"
    )
    database_pool_size: int = Field(default=10, description="数据库连接池大小")
    database_pool_timeout: int = Field(default=30, description="数据库连接超时时间")
    database_echo: bool = Field(default=False, description="是否输出SQL语句")
    
    # ===== Redis 配置 =====
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis连接URL")
    redis_pool_size: int = Field(default=10, description="Redis连接池大小")
    
    # ===== Manticore 配置 =====
    manticore_host: str = Field(default="localhost", description="Manticore主机地址")
    manticore_port: int = Field(default=9306, description="Manticore端口")
    manticore_http_port: int = Field(default=9308, description="Manticore HTTP端口")
    
    # ===== 外部服务依赖 =====
    user_service_url: str = Field(default="http://localhost:9002", description="用户服务URL")
    document_service_url: str = Field(default="http://localhost:9005", description="文档服务URL")
    embedding_service_url: str = Field(default="http://localhost:9001", description="向量化服务URL")
    
    # ===== 业务配置 =====
    max_topics_per_user: int = Field(default=100, description="每个用户最大主题数")
    max_documents_per_topic: int = Field(default=50, description="每个主题最大文档数")
    topic_name_max_length: int = Field(default=200, description="主题名称最大长度")
    topic_description_max_length: int = Field(default=1000, description="主题描述最大长度")
    
    # ===== 搜索配置 =====
    enable_topic_search: bool = Field(default=True, description="是否启用主题搜索")
    search_results_limit: int = Field(default=20, description="搜索结果数量限制")
    search_timeout: int = Field(default=5, description="搜索超时时间(秒)")
    
    # ===== 缓存配置 =====
    enable_topic_cache: bool = Field(default=True, description="是否启用主题缓存")
    topic_cache_ttl: int = Field(default=3600, description="主题缓存过期时间(秒)")
    popular_topics_cache_ttl: int = Field(default=1800, description="热门主题缓存过期时间(秒)")
    
    # ===== 统计配置 =====
    enable_statistics: bool = Field(default=True, description="是否启用统计功能")
    statistics_update_interval: int = Field(default=300, description="统计更新间隔(秒)")
    
    # ===== 权限配置 =====
    enable_topic_sharing: bool = Field(default=True, description="是否启用主题分享")
    default_topic_visibility: str = Field(default="private", description="默认主题可见性")
    
    # ===== CORS 配置 =====
    enable_cors: bool = Field(default=True, description="是否启用CORS")
    cors_origins: List[str] = Field(default=["*"], description="CORS允许的源")
    
    # ===== 健康检查配置 =====
    health_check_timeout: int = Field(default=5, description="健康检查超时时间(秒)")
    enable_health_check: bool = Field(default=True, description="是否启用健康检查")
    
    # ===== 请求配置 =====
    request_timeout: int = Field(default=30, description="请求超时时间(秒)")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟时间(秒)")
    
    class Config:
        env_prefix = "TOPIC_"
        case_sensitive = False
        
        @classmethod
        def parse_env_var(cls, field_name: str, raw_val: str):
            if field_name == 'cors_origins':
                # 解析 CORS origins 列表
                if raw_val.startswith('[') and raw_val.endswith(']'):
                    import json
                    return json.loads(raw_val)
                return [origin.strip() for origin in raw_val.split(',')]
            return cls.json_loads(raw_val)


@lru_cache()
def get_settings() -> TopicServiceSettings:
    """获取主题服务配置实例（单例模式）"""
    return TopicServiceSettings()


def create_env_example() -> str:
    """创建主题服务的.env.example文件内容"""
    return """# Topic Service Configuration

# ===== API服务配置 =====
TOPIC_API_HOST=0.0.0.0
TOPIC_API_PORT=9004
TOPIC_API_TITLE="Topic Service API"
TOPIC_API_DESCRIPTION="主题管理和文档关联服务"
TOPIC_API_VERSION=1.0.0
TOPIC_API_PREFIX=/api/v1

# ===== 环境配置 =====
TOPIC_ENVIRONMENT=development
TOPIC_DEBUG=false
TOPIC_LOG_LEVEL=INFO

# ===== 数据库配置 =====
TOPIC_DATABASE_URL=postgresql://master_know_user:master_know_pass@localhost:5432/master_know
TOPIC_DATABASE_POOL_SIZE=10
TOPIC_DATABASE_POOL_TIMEOUT=30
TOPIC_DATABASE_ECHO=false

# ===== Redis 配置 =====
TOPIC_REDIS_URL=redis://localhost:6379/0
TOPIC_REDIS_POOL_SIZE=10

# ===== Manticore 配置 =====
TOPIC_MANTICORE_HOST=localhost
TOPIC_MANTICORE_PORT=9306
TOPIC_MANTICORE_HTTP_PORT=9308

# ===== 外部服务依赖 =====
TOPIC_USER_SERVICE_URL=http://localhost:9002
TOPIC_DOCUMENT_SERVICE_URL=http://localhost:9005
TOPIC_EMBEDDING_SERVICE_URL=http://localhost:9001

# ===== 业务配置 =====
TOPIC_MAX_TOPICS_PER_USER=100
TOPIC_MAX_DOCUMENTS_PER_TOPIC=50
TOPIC_TOPIC_NAME_MAX_LENGTH=200
TOPIC_TOPIC_DESCRIPTION_MAX_LENGTH=1000

# ===== 搜索配置 =====
TOPIC_ENABLE_TOPIC_SEARCH=true
TOPIC_SEARCH_RESULTS_LIMIT=20
TOPIC_SEARCH_TIMEOUT=5

# ===== 缓存配置 =====
TOPIC_ENABLE_TOPIC_CACHE=true
TOPIC_TOPIC_CACHE_TTL=3600
TOPIC_POPULAR_TOPICS_CACHE_TTL=1800

# ===== 统计配置 =====
TOPIC_ENABLE_STATISTICS=true
TOPIC_STATISTICS_UPDATE_INTERVAL=300

# ===== 权限配置 =====
TOPIC_ENABLE_TOPIC_SHARING=true
TOPIC_DEFAULT_TOPIC_VISIBILITY=private

# ===== CORS 配置 =====
TOPIC_ENABLE_CORS=true
TOPIC_CORS_ORIGINS=["*"]

# ===== 健康检查配置 =====
TOPIC_HEALTH_CHECK_TIMEOUT=5
TOPIC_ENABLE_HEALTH_CHECK=true

# ===== 请求配置 =====
TOPIC_REQUEST_TIMEOUT=30
TOPIC_MAX_RETRIES=3
TOPIC_RETRY_DELAY=1.0
"""


if __name__ == "__main__":
    # 测试配置加载
    try:
        settings = get_settings()
        print("✅ 主题服务配置加载成功")
        print(f"API端口: {settings.api_port}")
        print(f"数据库URL: {settings.database_url}")
        print(f"用户服务URL: {settings.user_service_url}")
        print(f"最大主题数: {settings.max_topics_per_user}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
    
    # 生成.env.example文件
    env_content = create_env_example()
    with open("services/topic/.env.example", "w", encoding="utf-8") as f:
        f.write(env_content)
    print("✅ 已生成 services/topic/.env.example 文件")
