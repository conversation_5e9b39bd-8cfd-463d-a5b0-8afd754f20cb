"""
Topic Service 异常定义

定义主题服务相关的异常类
"""


class TopicServiceError(Exception):
    """主题服务基础异常"""
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class TopicNotFoundError(TopicServiceError):
    """主题不存在异常"""
    def __init__(self, topic_id: int):
        message = f"主题不存在: {topic_id}"
        super().__init__(message, "TOPIC_NOT_FOUND")
        self.topic_id = topic_id


class TopicValidationError(TopicServiceError):
    """主题验证异常"""
    def __init__(self, message: str, field: str = None):
        super().__init__(message, "TOPIC_VALIDATION_ERROR")
        self.field = field


class TopicPermissionError(TopicServiceError):
    """主题权限异常"""
    def __init__(self, message: str, user_id: int = None, topic_id: int = None):
        super().__init__(message, "TOPIC_PERMISSION_ERROR")
        self.user_id = user_id
        self.topic_id = topic_id


class DocumentLinkError(TopicServiceError):
    """文档关联异常"""
    def __init__(self, message: str, topic_id: int = None, document_id: int = None):
        super().__init__(message, "DOCUMENT_LINK_ERROR")
        self.topic_id = topic_id
        self.document_id = document_id


class ManticoreSearchError(TopicServiceError):
    """Manticore搜索异常"""
    def __init__(self, message: str):
        super().__init__(message, "MANTICORE_SEARCH_ERROR")


class DatabaseError(TopicServiceError):
    """数据库异常"""
    def __init__(self, message: str, original_error: Exception = None):
        super().__init__(message, "DATABASE_ERROR")
        self.original_error = original_error


class ConfigurationError(TopicServiceError):
    """配置异常"""
    def __init__(self, message: str, config_key: str = None):
        super().__init__(message, "CONFIGURATION_ERROR")
        self.config_key = config_key


class ExternalServiceError(TopicServiceError):
    """外部服务异常"""
    def __init__(self, message: str, service_name: str = None):
        super().__init__(message, "EXTERNAL_SERVICE_ERROR")
        self.service_name = service_name


class RateLimitError(TopicServiceError):
    """频率限制异常"""
    def __init__(self, message: str, retry_after: int = None):
        super().__init__(message, "RATE_LIMIT_ERROR")
        self.retry_after = retry_after


class CacheError(TopicServiceError):
    """缓存异常"""
    def __init__(self, message: str):
        super().__init__(message, "CACHE_ERROR")
