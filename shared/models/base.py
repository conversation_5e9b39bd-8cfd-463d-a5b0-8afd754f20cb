"""
共享数据模型
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class BaseEntity(BaseModel):
    """基础实体类"""
    id: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

class TextChunk(BaseEntity):
    """文本块模型"""
    content: str
    chunk_index: int
    start_char: int
    end_char: int
    token_count: Optional[int] = None
    embedding: Optional[List[float]] = None

class Document(BaseEntity):
    """文档模型"""
    title: str
    content: str
    file_type: str
    size: int
    chunks: Optional[List[TextChunk]] = None
    topic_id: Optional[str] = None

class SplitResult(BaseModel):
    """分割结果模型"""
    document_id: str
    chunks: List[TextChunk]
    strategy_used: str
    total_chunks: int
    error: Optional[str] = None

class ProcessingStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
