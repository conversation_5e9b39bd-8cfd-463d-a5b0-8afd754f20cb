# -*- coding: utf-8 -*-
"""
test_context_engine_evolution.py

## 💡 设计说明：可执行的演进蓝图

本测试文件旨在作为“知深学习导师”项目中 Context Engine 模块化重构的“可执行蓝图”。
它并非一个传统的单元测试，而是一个用代码阐述架构思想的示例，演示了如何将现有的
Context Engine 从一个“全能服务”平滑演进为一个“智能调度中心 + 专家系统”的模式。

核心思想，源于我们对 MIRIX 多智能体框架的探讨，并将其适配到本项目的实际情况中：

1.  **上下文引擎 (ContextEngineV2)**:
    -   **角色**: 智能调度中心 / 总指挥。
    -   **职责**: 不再亲自执行搜索，而是负责接收请求，理解用户意图，并将任务分派给最合适的“专家”。

2.  **意图路由器 (IntentRouter)**:
    -   **角色**: 调度员 / 路由。
    -   **职责**: 对用户的查询进行快速、轻量级的意图分析，判断用户是想“查询知识”还是“回溯记忆”。
    -   **实现**: 在本示例中，我们用简单的关键词匹配来模拟。在实际生产中，这可以是一个更复杂的分类模型。

3.  **检索器 (Retrievers) - 专家系统**:
    -   **角色**: 各领域的专家。
    -   **职责**: 每个 Retriever 只负责一种特定类型的检索，并可以为此优化其内部的查询逻辑。
    -   **示例**:
        -   `KnowledgeRetriever`: 知识专家。只在“文档原文切片”中搜索，追求高精度。
        -   `MemoryRetriever`: 记忆专家。只在“历史对话摘要”中搜索，追求高相关性。

这种架构的优势:
-   **高内聚、低耦合**: 每个组件职责单一，易于维护和独立优化。
-   **易扩展**: 未来增加新的记忆类型（如任务记忆、用户画像记忆），只需增加一个新的 Retriever 专家即可，无需改动核心逻辑。
-   **更智能**: 将“意图理解”从大模型的能力下沉为系统架构的一部分，使上下文构建更精准。

通过运行此测试，您可以清晰地看到数据流转和各组件的交互过程。
"""
import pytest
from typing import List, Dict, Any, Literal

# --- 模拟组件和数据结构 (Mocks & Stubs) ---
# 这些是真实组件的简化版，用于演示核心逻辑

class MockManticoreClient:
    """模拟 Manticore Search 客户端，用于演示不同检索逻辑"""
    def __init__(self):
        # 模拟两个不同的索引表
        self.knowledge_index = {
            "doc_chunk_1": "Manticore Search 是一个高性能的全文搜索引擎。",
            "doc_chunk_2": "Text-Splitter 引擎负责将文档分割成语义块。",
        }
        self.memory_index = {
            "summary_1": "用户询问了关于 Manticore 的基本概念。",
            "summary_2": "AI 解释了 Text-Splitter 的作用。",
        }

    def search(self, index: str, query: str) -> List[Dict[str, Any]]:
        print(f"\n🔍 [Manticore Mock] Searching in index '{index}' for query: '{query}'")
        if index == "knowledge_index":
            # 模拟知识检索：返回包含查询词的文档块
            results = [content for content in self.knowledge_index.values() if query in content]
            print(f"  -> Found {len(results)} knowledge chunks.")
            return [{"content": r} for r in results]
        elif index == "memory_index":
            # 模拟记忆检索：返回包含查询词的摘要
            results = [content for content in self.memory_index.values() if query in content]
            print(f"  -> Found {len(results)} memory summaries.")
            return [{"content": r} for r in results]
        return []

# --- “专家”定义 (Specialized Retrievers) ---

class KnowledgeRetriever:
    """知识专家：只负责从文档原文中检索知识"""
    def __init__(self, db_client: MockManticoreClient):
        self.db = db_client

    def retrieve(self, query: str, topic_id: str) -> List[str]:
        print("  - [Expert] KnowledgeRetriever is working...")
        # 实际实现中，这里的查询会更复杂，包含 topic_id 过滤等
        search_results = self.db.search(index="knowledge_index", query=query)
        return [res["content"] for res in search_results]

class MemoryRetriever:
    """记忆专家：只负责从历史摘要中检索对话记忆"""
    def __init__(self, db_client: MockManticoreClient):
        self.db = db_client

    def retrieve(self, query: str, topic_id: str) -> List[str]:
        print("  - [Expert] MemoryRetriever is working...")
        # 记忆检索可能使用不同的查询逻辑，例如更宽松的匹配
        search_results = self.db.search(index="memory_index", query=query)
        return [res["content"] for res in search_results]

# --- “调度员”定义 (Intent Router) ---

UserIntent = Literal["knowledge_query", "memory_retrieval", "unknown"]

class IntentRouter:
    """意图调度员：判断用户查询的意图"""
    def route(self, query: str) -> UserIntent:
        print(f"  - [Router] Analyzing intent for query: '{query}'")
        query_lower = query.lower()
        # 简化版规则：实际可使用模型或更复杂的规则
        if "是什么" in query_lower or "解释一下" in query_lower:
            print("    -> Intent identified as: knowledge_query")
            return "knowledge_query"
        if "我们上次" in query_lower or "你还记得" in query_lower:
            print("    -> Intent identified as: memory_retrieval")
            return "memory_retrieval"
        
        print("    -> Intent identified as: unknown (defaulting to knowledge_query)")
        return "unknown" # 默认或未知意图

# --- “总指挥”/“调度中心”定义 (The Orchestrator) ---

class ContextEngineV2:
    """
    上下文引擎 v2.0: 演进后的智能调度中心
    符合 `docs/2_Architecture/03_modular_refactor_plan.md` 中对 Context Engine 的优化方向。
    """
    def __init__(self, db_client: MockManticoreClient):
        print("\n🚀 ContextEngineV2 Initialized.")
        self.router = IntentRouter()
        # 专家们被实例化并由引擎统一管理
        self.retrievers = {
            "knowledge": KnowledgeRetriever(db_client),
            "memory": MemoryRetriever(db_client),
        }
        print("  - Experts onboard: KnowledgeRetriever, MemoryRetriever")
        print("  - Router is ready.")


    def build_context(self, query: str, topic_id: str) -> Dict[str, Any]:
        print(f"\n--- Building context for query: '{query}' ---")
        
        # 1. 意图路由
        intent = self.router.route(query)

        # 2. 根据意图，委派任务给最合适的专家
        retrieved_docs = []
        if intent == "knowledge_query":
            retriever = self.retrievers["knowledge"]
            retrieved_docs = retriever.retrieve(query="Manticore", topic_id=topic_id) # 简化演示
        elif intent == "memory_retrieval":
            retriever = self.retrievers["memory"]
            retrieved_docs = retriever.retrieve(query="Manticore", topic_id=topic_id) # 简化演示
        else:
            # 默认行为：当意图不明确时，可以同时求助于多个专家，或使用默认专家
            print("  - Defaulting to KnowledgeRetriever for unknown intent.")
            retriever = self.retrievers["knowledge"]
            retrieved_docs = retriever.retrieve(query="Manticore", topic_id=topic_id)

        # 3. 整合上下文并返回
        final_context = {
            "query": query,
            "intent": intent,
            "retrieved_docs": retrieved_docs,
            "final_prompt": f"基于以下信息：{retrieved_docs}\n\n请回答：{query}"
        }
        print(f"--- Context built successfully. ---")
        return final_context

# --- 测试用例 (Pytest Test Cases) ---

def test_context_engine_handles_knowledge_query():
    """
    测试场景: 用户提出一个明确的“知识查询”类问题。
    预期行为: ContextEngine 应路由到 KnowledgeRetriever，并从知识库中检索。
    """
    # 1. 准备 (Arrange)
    db_client = MockManticoreClient()
    engine = ContextEngineV2(db_client)
    query = "Manticore Search 是什么？"
    
    # 2. 执行 (Act)
    result = engine.build_context(query, topic_id="topic_123")
    
    # 3. 断言 (Assert)
    assert result["intent"] == "knowledge_query"
    assert len(result["retrieved_docs"]) == 1
    assert "高性能的全文搜索引擎" in result["retrieved_docs"][0]
    assert "用户询问了" not in result["retrieved_docs"][0] # 确认没有从记忆库检索
    print("\n✅ Test Passed: Correctly handled knowledge query.")

def test_context_engine_handles_memory_retrieval_query():
    """
    测试场景: 用户提出一个明确的“记忆回溯”类问题。
    预期行为: ContextEngine 应路由到 MemoryRetriever，并从历史摘要中检索。
    """
    # 1. 准备 (Arrange)
    db_client = MockManticoreClient()
    engine = ContextEngineV2(db_client)
    query = "我们上次聊到 Manticore 的什么了？"
    
    # 2. 执行 (Act)
    result = engine.build_context(query, topic_id="topic_123")
    
    # 3. 断言 (Assert)
    assert result["intent"] == "memory_retrieval"
    assert len(result["retrieved_docs"]) == 1
    assert "用户询问了关于 Manticore 的基本概念" in result["retrieved_docs"][0]
    assert "高性能的全文搜索引擎" not in result["retrieved_docs"][0] # 确认没有从知识库检索
    print("\n✅ Test Passed: Correctly handled memory retrieval query.")

# 运行测试 (可以直接用 pytest 执行此文件)
if __name__ == "__main__":
    print("="*50)
    print("  Running Test: Knowledge Query")
    print("="*50)
    test_context_engine_handles_knowledge_query()
    
    print("\n" + "="*50)
    print("  Running Test: Memory Retrieval Query")
    print("="*50)
    test_context_engine_handles_memory_retrieval_query()